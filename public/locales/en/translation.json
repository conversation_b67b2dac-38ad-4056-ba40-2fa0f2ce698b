{"navbar": {"reports": "Reports", "settings": "Settings", "ports": "Ports", "myPorts": "My Ports", "arrivalTracking": "Arrival Tracking", "dashboard": "Dashboard", "trips": "Trips", "monitoring": "Monitoring", "usersManagement": "Users Management", "language": "Language", "logout": "Logout", "username": "Username"}, "alerts": {"alerts": "<PERSON><PERSON><PERSON>", "alert": "<PERSON><PERSON>", "time": "Time", "totalAckAlerts": "Total Acknowledged Al<PERSON>s", "totalNotAckAlerts": "Total Unacknowledged Alerts", "totalActiveTrips": "Total Active Trips", "totalInActiveTrips": "Total Inactive Trips"}, "reports": {"tripsReport": "Trips Report", "alertsReport": "Alerts Report", "trips": "Trips", "alerts": "<PERSON><PERSON><PERSON>", "newAlerts": "New Alerts", "records": "Records", "statistics": "Statistics", "stops": "Stops", "employees": "Employees", "portDistribution": "Port Distribution", "tripTracking": "Trip Tracking", "activeTrips": "Active Trips"}, "common": {"tags": "Tags", "chart": "Chart", "PDFPreview": "PDF Preview", "downloadPDF": "Download PDF", "download": "Download", "preparing": "Preparing...", "continue": "Continue", "cancel": "Cancel", "alertId": "<PERSON><PERSON>", "alertType": "Alert <PERSON>", "transitNumber": "Transit Number", "routeName": "Route Name", "timestamp": "Timestamp", "address": "Address", "alertStatus": "Alert <PERSON>", "shipmentDescription": "Shipment Description", "details": "Details", "tripDetails": "Trip Details", "alertsDetails": "<PERSON><PERSON><PERSON>", "notAcknowledged": "Not Acknowledged", "fromState": "From State", "toState": "To State", "portIn": "Port In", "portOut": "Port Out", "latestInformationAboutTheTarget": "Latest Information About The Target", "tracker": "Tracker", "driver": "Driver", "truck": "Truck", "warnings": "Warnings", "noWarnings": "No Warnings", "status": "Status", "state": "State", "error": "Error", "success": "Success", "save": "Save", "saving": "Saving", "edit": "Edit", "activityLog": "View logs for this activity", "delete": "Delete", "add": "Add", "send": "Send", "sending": "Sending...", "confirm": "Confirm", "search": "Search", "filter": "Filter", "advancedFilter": "Advanced Filter", "routes": "Routes", "justMyRoutes": "Just My Routes", "showAll": "Show All", "showPorts": "Show Ports", "showCheckPoint": "Show Check Point", "measureDistance": "Measure Distance", "distanceTool": "Distance Tool", "stopMeasuring": "Stop Measuring", "distanceMeasurementActive": "Distance Measurement Active", "clickToSetStartPoint": "Click to set start point", "clickToSetEndPoint": "Click to set end point and measure", "clickAnywhereToStartNew": "Click anywhere to start new measurement", "measurementResults": "MEASUREMENT RESULTS", "totalDistance": "Total Distance", "bearing": "Bearing", "switchToImperial": "Switch to Imperial", "switchToMetric": "Switch to Metric", "clearMeasurement": "Clear Measurement", "startPoint": "Start Point", "endPoint": "End Point", "clear": "Clear", "viewDetails": "View Details", "downloadTripPanelAsXLS": "Download Trip Panel as <PERSON><PERSON>", "back": "Back", "pageNotFound": "Page Not Found", "page": "Page", "of": "of", "activeTripsWithAlerts": "Active Trips With Al<PERSON>s", "activeTripsWithoutAlerts": "Active Trips Without Alerts", "totalActiveTrips": "Total Active Trips", "totalClosedTrips": "Total Closed Trips", "closedTrips": "Closed Trips", "viewedTrips": "Viewed Trips", "notViewedTrips": "Not Viewed Trips", "tripCode": "Trip Code", "transitSequenceNo": "Transit Sequence No", "owner": "Owner", "elocks": "Elocks", "vehicleDetails": "Vehicle Details", "vehicleCountry": "Vehicle Country", "completeDistance": "Complete Distance", "remainingDistance": "Remaining Distance", "driverInfo": "Driver Info", "expectedArrivalDate": "Expected Arrival Date", "endDate": "End Date", "securityNotes": "Security Notes", "securityNotesUpdated": "Security Notes Updated", "securityNotesUpdateFailed": "Security Notes Update Failed", "noDataExist": "No Data Exist", "tripMap": "Trip Map", "tripWarnings": "Trip Warnings", "pings": "Pings", "tripPings": "<PERSON>", "movementReport": "Stops Report", "activitiesReport": "Activities Report", "eventsReport": "Logs Report", "tripViewer": "<PERSON>er", "alertsViewer": "<PERSON><PERSON><PERSON>", "tripAlerts": "<PERSON>", "location": "Location", "speed": "Speed", "kmh": " km/h", "noCharging": "No Charging", "batteryLife": "Battery Life {{ value }}", "close": "Close", "removeMarker": "<PERSON><PERSON><PERSON>", "display_settings_button": "Display Settings", "tripFilters": "<PERSON>s", "changesApplyRealTime": "Changes apply in real time", "from": "From", "to": "To", "start": "Start", "end": "End", "yes": "Yes", "no": "No", "field": "Field", "value": "Value", "duration": "Duration", "minutes": "Minutes", "hours": "Hours", "hour": "Hour", "noContactInfo": "No Contact Info", "procedures": "Procedures", "comingSoon": "Coming Soon", "featureInDevelopment": "This feature is currently in development and will be available soon.", "notes": "Notes", "currentTripLocation": "Current Trip Location", "print": "Print", "connected": "Connected", "disconnected": "Disconnected", "successfulOperation": "Successful Operation", "failedOperation": "Failed Operation"}, "pings": {"title": "<PERSON>", "lat": "Latitude", "long": "Longitude", "time": "Time", "speed": "Speed", "tripId": "Trip Id"}, "checkpoints": {"contactInfo": "Contact Information", "contactName": "Contact Name", "contactPhone": "Contact Phone", "checkpoint": "Customs Checkpoint", "policeStation": "Police Checkpoint", "suspectedArea": "Suspected Area", "default": "Checkpoint"}, "displaySettingsTab": {"displaySettings": "Display Settings", "tripDisplayMode": "Trip Display Mode", "clusterTrips": "Cluster Nearby Trips", "individualTrips": "Display Individual Trips", "checkpoints": "Checkpoints Display", "customsCheckpoints": "Customs Checkpoints", "policeCheckpoints": "Police Checkpoints", "suspiciousGeofences": "Suspicious Geofences", "ports": "Ports Display", "landPorts": "Land Ports", "seaports": "Seaports", "airports": "Airports", "landPort": "LandPort", "seaport": "Seaport", "airport": "Airport", "changesApplyRealTime": "Changes apply in real-time"}, "tooltips": {"email": "Email"}, "filter": {"warnings": "Warnings", "truckInfo": "Truck Info", "transitNumber": "Transit Number", "transitSequenceNumber": "Transit Sequence Number", "driverName": "Driver Name", "plateNumber": "Plate Number", "trackerNumber": "Tracker Number", "tripCode": "Trip Code", "tripPriority": "Trip Priority", "high": "High", "medium": "Medium", "low": "Low", "tripStatus": "Trip Status", "active": "Active", "ended": "Ended", "tripLocation": "Trip Location", "onRoute": "On Route", "unknown": "Unknown", "inExitBorder": "In Exit Border", "inEntryBorder": "In Entry Border", "date": "Date", "transactionDate": "Transaction Date", "startDate": "Start Date", "endDate": "End Date", "orderBy": "Order By", "descending": "Descending", "ascending": "Ascending", "entryPort": "Entry Port", "exitPort": "Exit Port", "transitNo": "Transit No", "transitDate": "Transit Date", "entryDate": "Entry Date", "exitDate": "Exit Date", "createdDate": "Created Date", "reset": "Reset", "applyFilter": "Apply Filter", "scope": "Scope Selection", "useFilters": "Use Filters", "applyingFilter": "Applying Filter", "allTrips": "All Trips", "tripCategory": "Trip Category", "myRoutes": "My Routes", "suspiciousTrips": "Suspicious Trips", "focusedTrips": "Focused Trips", "stoppedTrips": "Stopped Trips", "inEntryPort": "In Entry Port", "inExitPort": "In Exit Port", "selectAll": "Select All", "deSelectAll": "Deselect All"}, "placeholder": {"transitNumber": "Enter Transit Number Here", "transitSequenceNumber": "Enter Transit Sequence Number Here", "driverName": "Enter Driver Name Here", "plateNumber": "Enter Plate Number Here", "trackerNumber": "Enter Tracker Number Here", "tripCode": "Enter Trip Code Here"}, "pages": {"monitorBoard": "Monitor Board", "ports": "Ports", "login": "<PERSON><PERSON>", "notFound": "Page Not Found"}, "login": {"welcomeBack": "Welcome Back", "login": "<PERSON><PERSON>", "username": "Username", "password": "Password", "loginButton": "<PERSON><PERSON>", "pleaseLoginToContinue": "Please login to continue", "usernamePlaceholder": "Enter your username", "passwordPlaceholder": "Enter your password", "rememberMe": "Remember me", "forgotPassword": "Forgot password?", "loginSuccessful": "Login successful!", "loginFailed": "<PERSON><PERSON> failed. Please check your credentials and try again.", "invalidCredentials": "Invalid login credentials", "invalidResponse": "Invalid response from server", "invalidRefreshToken": "Invalid refresh token", "invalidUserProfile": "Invalid user profile response", "validationError": "An unexpected validation error occurred", "emailRequired": "Email is required", "emailInvalid": "Please enter a valid email address", "passwordRequired": "Password is required", "passwordInvalid": "Password must be at least 8 characters and include: uppercase letter, lowercase letter, number, and special character", "showPassword": "Show password", "hidePassword": "Hide password"}, "tripDetails": {"tripId": "Trip Id", "routeId": "Route Id", "trackingPriority": "Tracking Priority", "arrivalTracking": "Arrival Tracking", "moving": "Moving", "stopped": "Stopped", "suspectedTrip": "Suspected <PERSON>", "Suspicious": "Suspicious", "unSuspicious": "Un Suspicious", "markSuspicious": "<PERSON> as Suspicious", "unmarkSuspicious": "Unmark as Suspicious", "transitNumber": "Transit Number", "transitType": "Transit Type", "transitDate": "Transit Date", "declarationDate": "Declaration Date", "transitSeqNo": "Transit Seq No", "ownerDescription": "Owner Description", "shipmentDescription": "Shipment Description", "vehicleDetails": "Vehicle Details", "vehicleCountry": "Vehicle Country", "vehicleType": "Vehicle Type", "vehicleModel": "Vehicle Model", "vehiclePlateNumber": "Vehicle Plate Number", "vehicleColor": "Vehicle Color", "entryPort": "Entry Port", "exitPort": "Exit Port", "startingDate": "Starting Date", "expectedArrivalDate": "Expected Arrival Date", "endDate": "End Date", "trackerNo": "Tracker No", "tripStatusChangeSuccess": "Trip Status Changed Susscessffully", "tripStatusChangeFailed": "Failed to Change Trip Status", "elocks": "Elocks", "driverName": "Driver Name", "driverPassportNumber": "Driver Passport Number", "driverNationality": "Driver Nationality", "driverContactNo": "Driver Contact No", "securityNotes": "Security Notes", "distances": "Distances", "completeDistance": "Complete Distance", "remainingDistance": "Remaining Distance", "movementNumber": "Movement Number", "tripCode": "Trip Code", "owner": "Owner", "tripPriority": "Trip Priority", "startDate": "Start Date", "trackingDevice": "Tracking Device", "driverData": "Driver Data", "truckDetails": "Truck Details", "notes": "Notes", "driverPhoneNumber": "Driver Phone Number", "totalDistance": "Total Distance", "tripStatus": "Trip Status", "open": "Open", "closed": "Closed", "addSecurityNotes": "Add Security Notes", "editSecurityNotes": "Edit Security Notes", "noSecurityNotes": "No Security Notes Yet", "enterSecurityNotes": "Enter Security Notes Here", "endingTrip": "Ending Trip", "endTripReasonRequired": "Reason is required to end the trip.", "tripEndedSuccessfully": "Trip ended successfully.", "FailedToEndTrip": "Failed to end trip. Please try again.", "reason": "Reason", "enterReason": "Enter Reason Here", "tripTracking": "Trip Tracking", "tracking": "Tracking", "unTracking": "UnTracking", "trackReplay": "Track Replay", "alertIcons": "<PERSON><PERSON>", "status": "Status", "aboutDriver": "About Driver", "aboutTrip": "About Trip", "aboutVehicle": "About Vehicle", "aboutShipment": "About Shipment", "trackingSerialNumber": "Tracking Serial Number", "elockSerialNumber": "Elock Serial Number", "startedAtDate": "Started At Date", "endedAtDate": "Ended At Date", "tripDetails": "Trip Details", "alerts": "<PERSON><PERSON><PERSON>", "noAlerts": "There are no alerts now", "acknowledgeAllAlerts": "Acknowledge All Alerts", "AcknowledgingAllAlerts": "Acknowledging <PERSON> Alerts", "allAlertsAcknowledged": "All Alerts Acknowledged", "acknowledgeAlert": "Acknowledge <PERSON>", "alertAcknowledged": "<PERSON><PERSON> Acknowledged", "acknowledged": "Acknowledged", "NotAcknowledged": "Not Acknowledged", "acknowledge": "Acknowledge", "activeTrips": "Active Trips", "liveData": "Live Data", "liveDataError": "Failed to load live data", "statusConditions": {"gpsSignalStrength": "GPS Signal Strength", "gsmSignalStrength": "GSM Signal Strength", "batteryLevel": "Battery Level", "chargerStatus": "Charger Status", "currentSpeed": "Current Speed", "routeZone": "Route Zone", "routeGeofence": "Is Within Route Geofence", "timeElapsedSinceTripStart": "Time Elapsed Since Trip Start", "remainingDistance": "Remaining Distance", "suspiciousZone": "Is Within Suspicious Zone"}, "tripAlerts": {"lastUpdate": "Last Update", "from": "From", "to": "To"}, "tripMap": {"tripRoute": "Trip Route", "truckStopped": "Truck Stopped", "trackerTamper": "Tracker <PERSON>", "trackerDropped": "Tracker Dropped", "lockTamper": "Lock Tamper", "lockOpen": "Lock Open", "lockConnectionLost": "Lock Connection Lost", "trackerBatteryLow": "Tracker Battery Low", "lockBatteryLow": "Lock Battery Low", "gsmSignalLost": "GSM Signal Lost", "gpsSignalLost": "GPS Signal Lost", "entringGeofence": "Entering Geofence", "leavingGeofence": "Leaving Geofence", "trackerConnectionLost": "Tracker Connection Lost", "overSpeeding": "Over Speeding", "wrongDirection": "Wrong Direction", "suspiciousArea": "Entered Suspicious Area", "activity": "All Activities", "fourHourExceeded": "Exceeded Four Hours Of Stoppage", "generalInfo": "General information", "truckStateAtStartingPoint": "Truck State At Starting Point", "truckStateAtEndingPoint": "Truck State At Ending Point", "chargerStatusChanged": "Charger Status Changed", "defaultAlert": "Warning"}, "timeline": {"interval": {"title": "Timeline", "30m": "Every 30 minutes", "1h": "Every hour", "3h": "Every 3 hours", "6h": "Every 6 hours", "12h": "Every 12 hours", "24h": "Every 24 hours"}}}, "emailDialog": {"title": "Send Email", "email": "Email", "cc": "CC", "recipientEmail": "Recipient Email Address", "ccPlaceholder": "Enter CC Emails Here", "subject": "Subject", "subjectPlaceholder": "Email Subject", "descriptionPlaceholder": "Email Description", "sendSuccess": "<PERSON>ail sent successfully", "sendFailed": "Failed to send email. Please try again."}, "validation": {"emailRequired": "Email is required", "emailInvalid": "Please enter a valid email address", "subjectRequired": "Subject is required", "descriptionRequired": "Description is required"}, "alert": {"name": "Name", "timeStamp": "Time Stamp", "status": "Status", "tripId": "Trip Id", "stopDuration": "Stop Duration"}, "events": {"title": "Event Log", "eventReports": "Event Reports", "event": "Event", "user": "User", "details": "Details", "time": "Time", "trip": "Trips", "alert": "<PERSON><PERSON><PERSON> ", "all": "All", "scope": "<PERSON><PERSON>", "action": "Action", "watch": "Watch", "acknowledge": "Acknowledge"}, "stops": {"title": "Stops Report", "placeName": "Place Name", "from": "From", "to": "To", "duration": "Duration", "tripId": "Trip Id"}, "tripActivities": {"title": "Trip Activities Report", "reportTitle": "Trip Activities Report", "deletedActivitiesLog": "Deleted Activities Log", "deleted": "Deleted", "create": "Create", "employee": "Employee", "date": "Date", "state": "State", "action": "Action", "details": "Details", "location": "Location", "clickToViewOnMap": "Click to view on map", "place": "Place", "notes": "Notes", "updatedDate": "Updated Date", "status": "Status", "reportNumber": "Report Number", "reportNumberPlaceholder": "Enter Report Number Here", "detailsPlaceholder": "Enter Details Here", "actionPlaceholder": "Choose Action Here", "statusPlaceholder": "Choose Status Here", "deleteReason": "Delete Reason", "deleteReasonPlaceholder": "Enter Delete Reason Here", "deletedSuccessfully": "Deleted Successfully", "deleteFailed": "Failed to delete", "created": "Created Successfully", "success": "Success", "createFailed": "Failed to create", "updatedSuccessfully": "Updated Successfully", "updateFailed": "Failed to update", "edit": "Edit"}, "tripReplay": {"title": "Track Replay", "description": "You can use this feature to replay the trip from the beginning and view its path as it happened", "rewind": "Rewind", "fastForward": "Fast Forward", "speed": "Speed", "play": "Play", "pause": "Pause"}, "activityLogs": {"title": "Activity Logs", "tripActivity": "Trip Activity", "deletedActivities": "Deleted Activities", "user": "User", "date": "Date", "status": "Status", "action": "Action", "details": "Details", "reportNumber": "Report Number", "deleteReason": "Delete Reason", "beforeAfter": "Details", "before": "Before", "after": "After", "rowTitle": "Row"}, "emails": {"greeting": "Peace be upon you and God's mercy", "trip": {"title": "Trip Details", "transitNumber": "Transit Number", "transitSequenceNumber": "Transit Sequence Number", "movementNumber": "Movement Number", "truckData": "Truck Data", "truckDescription": "Truck Description", "truckPlate": "Truck Plate", "driverName": "Driver Name", "driverNationality": "Driver Nationality", "driverPhone": "Driver Phone Number", "ownerName": "Owner Name", "shipmentDescription": "Shipment Description", "transitType": "Transit Type", "trackingDevice": "Tracking Device", "lockNumber": "Lock Number", "trackingPriority": "Tracking Priority", "entryPort": "Entry Port", "exitPort": "Exit Port", "entryDate": "Entry Date", "entryTime": "Entry Time", "alerts": "<PERSON><PERSON><PERSON>", "alertID": "<PERSON><PERSON>", "alertType": "Alert <PERSON>", "alertTime": "<PERSON><PERSON>", "colleagues": "Dear Colleagues:", "greetingMessage": "Peace be upon you and <PERSON>'s mercy and blessings:", "verificationMessage": "We hope to ensure the safety of customs seals and tracking device and match the shipment to what is recorded in the customs declaration and inform us at the tracking unit email", "transitDepartment": "Transit and Monitoring Department"}}, "map": {"showTraffic": "Show Traffic", "hideTraffic": "Hide Traffic", "showTrafficInformation": "Show traffic information", "hideTrafficInformation": "Hide traffic information"}, "users": {"totalUsers": "Total Users", "totalActiveUsers": "Total Active Users", "totalInActiveUsers": "Total Inactive Users", "userName": "User Name", "email": "Email", "phone": "Phone", "roles": "Roles", "status": "Status", "active": "Active", "inactive": "Inactive", "actions": "Actions", "addUser": "Add User", "editUser": "Edit User", "deleteUser": "Delete User", "showDetails": "Show Details"}}