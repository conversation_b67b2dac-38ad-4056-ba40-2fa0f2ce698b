import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';

import { Icon, type IconName } from '@/components/common/ui/Icon';

const meta: Meta<typeof Icon> = {
    title: 'Components/Icon',
    component: Icon,
    tags: ['autodocs'],
    parameters: {
        docs: {
            description: {
                component: `
# Icon Component

A reusable SVG-based Icon component that integrates with \`vite-plugin-svg-icons\`.

## Features:
- Dynamically renders an SVG \`<use>\` reference from the sprite generated by \`vite-plugin-svg-icons\`.
- Supports custom \`className\` for styling (size, color, margin, etc.).
- Defaults to a consistent style (\`w-5 h-5 text-green-700\`) if no custom class is provided.
- Uses \`currentColor\` for \`fill\`, making the icon automatically adapt to the text color (\`color\` CSS property).

## Usage:
\`\`\`tsx
<Icon name="shipment-tracking" />
<Icon name="alert" className="w-8 h-8 text-red-600" />
<Icon name="filter" className="w-6 h-6" />
\`\`\`
                `,
            },
        },
    },
    argTypes: {
        name: {
            description: 'Icon name - must match the SVG filename without extension.',
            control: 'select',
            options: [
                // Map & Navigation Icons
                'tripPings',
                'movementReport',
                'activitiesReport',
                'truck',
                'truck-2',
                'tripPin',
                'checkPointPin',
                'policeCheckPointPin',
                'customCheckpoint',
                'customPoliceStation',
                'seaPortPin',
                'airPortPin',
                'landPortPin',
                'enteringGeoFence',
                'leavingGeofence',
                'shipmentTracking',
                'mapLocation',
                'noMap',
                'ship',
                'plane',

                // Battery & Charging Icons
                'batteryEmpty',
                'batteryVeryLow',
                'batteryLow',
                'batteryMedium',
                'batteryFull',
                'batteryCharging',
                'charger',
                'chargerOn',
                'charging',

                // Signal & GPS Icons
                'gpsDisconnected',
                'gpsSignal',
                'gpsSignalFull',
                'gpsSignalWeak',
                'signalNo',
                'signalLow',
                'signalLowMedium',
                'signalMedium',
                'signalFull',

                // Status & Alert Icons
                'alert',
                'filter',
                'speed',
                'lock',

                // Other Icons
                'excel',
                'target',
                'notification',
                'notificationMuted',
            ],
        },
        className: {
            description: 'Additional CSS classes for styling (size, color, etc.).',
            control: 'text',
        },
    },
};

export default meta;
type Story = StoryObj<typeof Icon>;

// All Icons Overview
export const AllIcons: Story = {
    render: () => {
        const allIcons = [
            'tripPings',
            'movementReport',
            'activitiesReport',
            'truck',
            'truck-2',
            'tripPin',
            'checkPointPin',
            'policeCheckPointPin',
            'customCheckpoint',
            'customPoliceStation',
            'seaPortPin',
            'airPortPin',
            'landPortPin',
            'enteringGeoFence',
            'leavingGeofence',
            'shipmentTracking',
            'mapLocation',
            'noMap',
            'ship',
            'plane',
            'batteryEmpty',
            'batteryVeryLow',
            'batteryLow',
            'batteryMedium',
            'batteryFull',
            'batteryCharging',
            'charger',
            'chargerOn',
            'charging',
            'gpsDisconnected',
            'gpsSignal',
            'gpsSignalFull',
            'gpsSignalWeak',
            'signalNo',
            'signalLow',
            'signalLowMedium',
            'signalMedium',
            'signalFull',
            'alert',
            'filter',
            'speed',
            'lock',
            'excel',
            'target',
            'notification',
            'notificationMuted',
        ];

        return (
            <div className="grid grid-cols-6 gap-4 p-4">
                {allIcons.map((iconName) => (
                    <div key={iconName} className="flex flex-col items-center gap-2 p-2 border rounded">
                        <Icon name={iconName as IconName} className="w-6 h-6" />
                        <span className="text-xs text-center">{iconName}</span>
                    </div>
                ))}
            </div>
        );
    },
    parameters: {
        docs: {
            description: {
                story: 'Complete overview of all available icons in the system.',
            },
        },
    },
};
