import type { Meta, StoryObj } from '@storybook/react';

import SummaryCard from '@/components/common/ui/SummaryCard';

const meta: Meta<typeof SummaryCard> = {
    title: 'Components/SummaryCard',
    component: SummaryCard,
    tags: ['autodocs'],
    argTypes: {
        details: {
            description: 'Card details object',
            control: 'object',
        },
        minWidth: {
            description: 'Minimum width',
            control: 'number',
        },
    },
};

export default meta;
type Story = StoryObj<typeof SummaryCard>;

export const Default: Story = {
    args: {
        details: {
            title: 'Total Users',
            iconName: 'alert', // <span>👥</span>,
            value: 1234,
        },
    },
};

export const WithCustomWidth: Story = {
    args: {
        details: {
            title: 'Active Users',
            iconName: 'alert', // <span>✅</span>,
            value: 567,
        },
        minWidth: 300,
    },
};
