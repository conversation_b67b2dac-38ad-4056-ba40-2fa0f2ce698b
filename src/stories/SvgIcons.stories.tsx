import type { <PERSON>a, StoryObj } from '@storybook/react';

import { Icon, type IconName } from '@/components/common/ui/Icon';

const meta: Meta = {
    title: 'Design System/SVG Icons',
    parameters: {
        docs: {
            description: {
                component: `
# SVG Icons

A comprehensive collection of all SVG icons available in the design system. These icons are organized by category and can be used throughout the application for consistent visual communication.

## Usage:
\`\`\`tsx
import { Icon } from '@/components/common/ui/Icon';

<Icon name="truck" className="w-6 h-6" />
<Icon name="alert" className="w-8 h-8 text-red-500" />
\`\`\`

## Icon Categories:
- **Map & Navigation**: Location pins, tracking, and navigation icons
- **Battery & Charging**: Battery status and charging indicators
- **Signal & GPS**: GPS and signal strength indicators
- **Status & Alerts**: General status and utility icons
                `,
            },
        },
    },
};

export default meta;
type Story = StoryObj;

// All SVG Icons Overview
export const SvgIcons: Story = {
    render: () => {
        const allIcons = [
            // Map & Navigation Icons
            { name: 'truck', category: 'Map & Navigation' },
            { name: 'tripPin', category: 'Map & Navigation' },
            { name: 'checkPointPin', category: 'Map & Navigation' },
            { name: 'policeCheckPointPin', category: 'Map & Navigation' },
            { name: 'seaPortPin', category: 'Map & Navigation' },
            { name: 'airPortPin', category: 'Map & Navigation' },
            { name: 'landPortPin', category: 'Map & Navigation' },
            { name: 'enteringGeoFence', category: 'Map & Navigation' },
            { name: 'leavingGeofence', category: 'Map & Navigation' },
            { name: 'shipmentTracking', category: 'Map & Navigation' },

            // Battery & Charging Icons
            { name: 'batteryEmpty', category: 'Battery & Charging' },
            { name: 'batteryVeryLow', category: 'Battery & Charging' },
            { name: 'batteryLow', category: 'Battery & Charging' },
            { name: 'batteryMedium', category: 'Battery & Charging' },
            { name: 'batteryFull', category: 'Battery & Charging' },
            { name: 'batteryCharging', category: 'Battery & Charging' },
            { name: 'charger', category: 'Battery & Charging' },
            { name: 'chargerOn', category: 'Battery & Charging' },
            { name: 'charging', category: 'Battery & Charging' },

            // Signal & GPS Icons
            { name: 'gpsDisconnected', category: 'Signal & GPS' },
            { name: 'gpsSignal', category: 'Signal & GPS' },
            { name: 'gpsSignalFull', category: 'Signal & GPS' },
            { name: 'gpsSignalWeak', category: 'Signal & GPS' },
            { name: 'signalNo', category: 'Signal & GPS' },
            { name: 'signalLow', category: 'Signal & GPS' },
            { name: 'signalLowMedium', category: 'Signal & GPS' },
            { name: 'signalMedium', category: 'Signal & GPS' },
            { name: 'signalFull', category: 'Signal & GPS' },

            // Status & Alert Icons
            { name: 'alert', category: 'Status & Alerts' },
            { name: 'filter', category: 'Status & Alerts' },
            { name: 'speed', category: 'Status & Alerts' },
        ];

        const categories = [...new Set(allIcons.map((icon) => icon.category))];

        return (
            <div className="space-y-8 p-6">
                {categories.map((category) => (
                    <div key={category} className="space-y-4">
                        <h3 className="text-lg font-semibold text-gray-800 border-b pb-2">{category}</h3>
                        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                            {allIcons
                                .filter((icon) => icon.category === category)
                                .map((icon) => (
                                    <div
                                        key={icon.name}
                                        className="flex flex-col items-center gap-2 p-3 border border-gray-200 rounded-lg hover:border-gray-300 hover:shadow-sm transition-colors">
                                        <Icon name={icon.name as IconName} className="w-8 h-8" />

                                        <span className="text-xs text-center text-gray-600 font-mono">{icon.name}</span>
                                    </div>
                                ))}
                        </div>
                    </div>
                ))}
            </div>
        );
    },
    parameters: {
        docs: {
            description: {
                story: 'Complete collection of all SVG icons organized by category. Each icon is displayed with its name for easy reference and implementation.',
            },
        },
    },
};
