import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react';

import DraggableList from '@/components/common/ui/DraggableList';

/**
 * # DraggableList Component
 *
 * A reusable drag-and-drop list component built with `@dnd-kit`. It allows users to reorder a list of items via drag-and-drop interactions. The component is highly customizable, supporting both horizontal and vertical layouts, localStorage persistence, and a callback for order changes.
 *
 * ## Features
 * - **Reusable with Any Item Type**: Render any content (cards, list items, widgets, etc.) using the `renderItem` prop.
 * - **Horizontal and Vertical Layouts**: Switch between layouts using the `strategy` prop (`horizontal` or `vertical`).
 * - **LocalStorage Persistence**: Optionally save and restore item order across sessions with the `storageKey` prop.
 * - **Order Change Callback**: Receive updates on order changes via the `onChange` callback.
 * - **Customizable Styling**: Supports Tailwind CSS classes for gap (`gap`) and container styling (`className`).
 * - **Smooth Animations**: Powered by `@dnd-kit` for fluid drag-and-drop interactions.
 *
 * ## Props
 * | Prop Name       | Type                              | Description                                                                 | Default       |
 * |-----------------|-----------------------------------|-----------------------------------------------------------------------------|---------------|
 * | `items`         | `string[]`                        | List of item identifiers (unique strings).                                   | Required      |
 * | `renderItem`    | `(item: string) => ReactNode`     | Function to render each item based on its identifier.                        | Required      |
 * | `storageKey`    | `string \| undefined`             | Key to persist order in localStorage. If omitted, persistence is disabled.   | `undefined`   |
 * | `strategy`      | `'horizontal' \| 'vertical'`      | Layout orientation for the list.                                             | `'horizontal'`|
 * | `className`     | `string \| undefined`             | Additional CSS classes for the container (Tailwind-friendly).                | `''`          |
 * | `gap`           | `string \| undefined`             | Tailwind gap class for spacing between items.                               | `'gap-3'`     |
 * | `onChange`      | `(newOrder: string[]) => void`    | Callback invoked when the item order changes.                                | `undefined`   |
 *
 * ## Example Usage
 * ```tsx
 * import DraggableList from '@/components/common/ui/DraggableList';
 *
 * const cards = [
 *   { id: 'card1', title: 'Card 1', subtitle: 'Description 1' },
 *   { id: 'card2', title: 'Card 2', subtitle: 'Description 2' },
 *   { id: 'card3', title: 'Card 3', subtitle: 'Description 3' },
 *   { id: 'card4', title: 'Card 4', subtitle: 'Description 4' },
 * ];
 *
 * export default function Dashboard() {
 *   return (
 *     <DraggableList
 *       items={cards.map((card) => card.id)}
 *       storageKey="dashboardCardOrder"
 *       strategy="horizontal"
 *       gap="gap-4"
 *       renderItem={(id) => {
 *         const card = cards.find((c) => c.id === id);
 *         return (
 *           <div className="p-4 bg-white shadow rounded-lg w-48 h-32">
 *             <h3 className="text-lg font-semibold">{card?.title}</h3>
 *             <p className="text-sm text-gray-500">{card?.subtitle}</p>
 *           </div>
 *         );
 *       }}
 *       onChange={(newOrder) => console.log("New order:", newOrder)}
 *     />
 *   );
 * }
 * ```
 */
const meta: Meta<typeof DraggableList> = {
    title: 'Components/DraggableList',
    component: DraggableList,
    tags: ['autodocs'],
    args: {
        items: ['card1', 'card2', 'card3', 'card4'],
        renderItem: (id: string) => {
            const cardData = [
                { id: 'card1', title: 'Card 1', subtitle: 'Description 1' },
                { id: 'card2', title: 'Card 2', subtitle: 'Description 2' },
                { id: 'card3', title: 'Card 3', subtitle: 'Description 3' },
                { id: 'card4', title: 'Card 4', subtitle: 'Description 4' },
            ];
            const card = cardData.find((c) => c.id === id);
            return (
                <div className="p-4 bg-white shadow rounded-lg w-48 h-32 flex flex-col justify-center">
                    <h3 className="text-lg font-semibold">{card?.title}</h3>
                    <p className="text-sm text-gray-500">{card?.subtitle}</p>
                </div>
            );
        },
        gap: 'gap-4',
    },
};

export default meta;
type Story = StoryObj<typeof DraggableList>;

/**
 * ## Horizontal Layout
 * Displays the draggable list in a horizontal layout with default spacing.
 * Each card includes a title and subtitle. Drag items to reorder them, and use the browser's console to see the `onChange` callback output.
 */
export const Horizontal: Story = {
    args: {
        strategy: 'horizontal',
        className: 'p-4',
        onChange: () => {},
    },
};

/**
 * ## Vertical Layout
 * Displays the draggable list in a vertical layout, ideal for lists or stacked items.
 * Each card includes a title and subtitle. Drag items to reorder them, and use the browser's console to see the `onChange` callback output.
 */
export const Vertical: Story = {
    args: {
        strategy: 'vertical',
        className: 'p-4',
        onChange: () => {},
    },
};

/**
 * ## LocalStorage Persistence
 * Persists the item order in localStorage using the `storageKey` prop.
 * Each card includes a title and subtitle. Reorder items, refresh the page, and the order will be retained.
 */
export const WithLocalStorage: Story = {
    args: {
        strategy: 'horizontal',
        storageKey: 'storybook-draggable-list-order',
        className: 'p-4',
        onChange: () => {},
    },
};

/**
 * ## Custom Gap
 * Demonstrates custom spacing between items using the `gap` prop.
 * Each card includes a title and subtitle. Uses a larger gap (`gap-8`) for a more spread-out layout.
 */
export const CustomGap: Story = {
    args: {
        strategy: 'horizontal',
        gap: 'gap-8',
        className: 'p-4',
        onChange: () => {},
    },
};
