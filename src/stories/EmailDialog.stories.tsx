import type { Meta, StoryObj } from '@storybook/react';

import EmailDialog from '@/components/common/ui/EmailDialog';

const meta: Meta<typeof EmailDialog> = {
    title: 'Components/EmailDialog',
    component: EmailDialog,
    tags: ['autodocs'],
    parameters: {
        layout: 'centered',
    },
};

export default meta;
type Story = StoryObj<typeof EmailDialog>;

export const Default: Story = {
    render: () => (
        <EmailDialog scope={1}>
            <button className="px-4 py-2 bg-blue-500 text-white rounded">Open Email Dialog</button>
        </EmailDialog>
    ),
};
