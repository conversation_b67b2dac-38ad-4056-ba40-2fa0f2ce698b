<svg width="67" height="101" viewBox="0 0 67 101" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_dd_766_17716)">
<path d="M63 33.5413C63 49.8565 49.7924 72.4821 33.5 72.4821C17.2076 72.4821 4 49.8565 4 33.5413C4 17.2261 17.2076 4 33.5 4C49.7924 4 63 17.2261 63 33.5413Z" fill="#E4514F" fill-opacity="0.5" shape-rendering="crispEdges"/>
</g>
<rect x="8.21436" y="8" width="50.5714" height="50.5714" rx="25.2857" fill="#E4514F"/>
<path d="M20.1667 26.9521C19.6145 26.9521 19.1667 27.3999 19.1667 27.9521C19.1667 28.5044 19.6145 28.9521 20.1667 28.9521H28.1667C28.719 28.9521 29.1667 28.5044 29.1667 27.9521C29.1667 27.3999 28.719 26.9521 28.1667 26.9521H20.1667Z" fill="white"/>
<path d="M20.1667 30.9521C19.6145 30.9521 19.1667 31.3999 19.1667 31.9521C19.1667 32.5044 19.6145 32.9521 20.1667 32.9521H25.5001C26.0524 32.9521 26.5001 32.5044 26.5001 31.9521C26.5001 31.3999 26.0524 30.9521 25.5001 30.9521H20.1667Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M33.7605 23.7658C33.3465 23.6313 32.8003 23.619 31.2334 23.619H20.1667C19.6145 23.619 19.1667 23.1713 19.1667 22.619C19.1667 22.0667 19.6145 21.619 20.1667 21.619L31.4188 21.6189C32.7281 21.6184 33.6225 21.6181 34.3785 21.8637C35.6265 22.2692 36.6535 23.1445 37.2556 24.2856L39.3113 24.2856C40.2394 24.2855 41.0096 24.2855 41.6439 24.3496C42.3119 24.417 42.9101 24.5614 43.4748 24.8987C44.0394 25.2359 44.4501 25.6942 44.8262 26.2504C45.1833 26.7785 45.5485 27.4566 45.9885 28.2738L47.7062 31.4638C47.7462 31.5352 47.7777 31.612 47.7994 31.6928C47.8242 31.7847 47.8352 31.8784 47.8334 31.971V34.6906C47.8334 36.201 47.8335 37.4353 47.7025 38.4098C47.5655 39.4284 47.2691 40.3115 46.5642 41.0165C45.8108 41.7699 44.8484 42.0604 43.7226 42.1837C43.3221 43.7746 41.8819 44.9522 40.1667 44.9522C38.4884 44.9522 37.0734 43.8246 36.6381 42.2857H30.362C29.9267 43.8246 28.5118 44.9522 26.8334 44.9522C25.1182 44.9522 23.6781 43.7746 23.2776 42.1837C22.1517 42.0604 21.1894 41.7699 20.436 41.0165C19.5015 40.082 19.279 38.826 19.2047 37.3354C19.1772 36.7838 19.6021 36.3144 20.1537 36.2869C20.7053 36.2594 21.1747 36.6843 21.2022 37.2359C21.2737 38.6699 21.4863 39.2384 21.8502 39.6022C22.1282 39.8803 22.5257 40.07 23.3377 40.1759C23.8079 38.6933 25.1952 37.6189 26.8334 37.6189C28.5118 37.6189 29.9268 38.7466 30.3621 40.2857H36.6381C37.0733 38.7466 38.4883 37.6189 40.1667 37.6189C41.805 37.6189 43.1923 38.6933 43.6625 40.1759C44.4744 40.07 44.872 39.8803 45.15 39.6022C45.4214 39.3309 45.6132 38.9401 45.7203 38.1433C45.8313 37.3177 45.8334 36.2186 45.8334 34.619V32.9523L40.4104 32.9524C39.572 32.9531 38.9232 32.9536 38.367 32.7729C37.2507 32.4102 36.3756 31.535 36.0129 30.4187C35.8322 29.8625 35.8327 29.2138 35.8333 28.3754L35.8334 28.219C35.8334 26.6521 35.8211 26.1059 35.6866 25.6919C35.3898 24.7786 34.6738 24.0626 33.7605 23.7658ZM45.1592 30.9523L44.251 29.2657C43.7816 28.3938 43.4634 27.8054 43.1695 27.3707C42.8882 26.9547 42.6741 26.75 42.4492 26.6157C42.2243 26.4813 41.9426 26.3899 41.443 26.3395C40.9209 26.2867 40.2519 26.2856 39.2617 26.2856H37.7993C37.834 26.7817 37.8337 27.3513 37.8335 28.0336L37.8334 28.219C37.8334 29.2895 37.8458 29.5876 37.915 29.8007C38.0799 30.3081 38.4777 30.7059 38.9851 30.8708C39.1981 30.94 39.4962 30.9523 40.5667 30.9523H45.1592ZM25.1667 41.2856C25.1667 40.3651 25.9129 39.6189 26.8334 39.6189C27.7539 39.6189 28.5001 40.3651 28.5001 41.2856C28.5001 42.206 27.7539 42.9522 26.8334 42.9522C25.9129 42.9522 25.1667 42.206 25.1667 41.2856ZM38.5001 41.2856C38.5001 40.3651 39.2463 39.6189 40.1667 39.6189C41.0872 39.6189 41.8334 40.3651 41.8334 41.2856C41.8334 42.206 41.0872 42.9522 40.1667 42.9522C39.2463 42.9522 38.5001 42.206 38.5001 41.2856Z" fill="white"/>
<circle opacity="0.5" cx="14.2327" cy="14.2327" r="14.2327" transform="matrix(0.866044 -0.499967 0.866044 0.499967 8.84766 86.7139)" fill="#E4514F"/>
<circle cx="9.9894" cy="9.9894" r="9.9894" transform="matrix(0.866044 -0.499967 0.866044 0.499967 15.905 85.9155)" fill="#E4514F"/>
<defs>
<filter id="filter0_dd_766_17716" x="0" y="0" width="67" height="76.4822" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="2"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_766_17716"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-2" dy="-2"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_766_17716" result="effect2_dropShadow_766_17716"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_766_17716" result="shape"/>
</filter>
</defs>
</svg>
