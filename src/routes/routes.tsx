// src/routes/AppRoutes.tsx
import React from 'react';
import { Routes, Route, useLocation, Navigate } from 'react-router-dom';

import Layout from '@/components/features/layout/Layout';
import ProtectedRoute from '@/components/common/ProtectedRoute';
import { useAuthStore } from '@/stores/auth.store';

/**
 * AppRoutes Component
 * Defines the main routing structure for the application
 * Handles navigation between different pages/components
 */

//-- Lazy Loading
const LazyLogin = React.lazy(() => import('../components/pages/Login'));
const LazyTrips = React.lazy(() => import('../components/pages/Trips'));
const LazyAlerts = React.lazy(() => import('../components/pages/Alerts'));
const LazyDashboard = React.lazy(() => import('../components/pages/Dashboard'));
const LazyMonitorBoard = React.lazy(() => import('../components/pages/MonitorBoard'));
const LazyTripDetails = React.lazy(() => import('../components/features/trips/TripsDetails'));
const LazyPageNotFound = React.lazy(() => import('../components/pages/PageNotFound'));
const LazyPorts = React.lazy(() => import('../components/pages/Ports'));
const LazySettings = React.lazy(() => import('../components/pages/Settings'));
const LazyUsers = React.lazy(() => import('../components/pages/Users'));

export default function AppRoutes() {
    const location = useLocation();
    const { isAuthenticated } = useAuthStore();

    // If we navigated with state.background, this becomes the "background" page.
    const state = location.state as { background?: Location };
    const background = state?.background;

    return (
        <>
            {/* Primary routes render using background location when present */}
            <Routes location={background || location}>
                {/* Protected routes - require authentication */}
                <Route
                    element={
                        <ProtectedRoute>
                            <Layout />
                        </ProtectedRoute>
                    }>
                    <Route path="/" element={<LazyMonitorBoard />} />
                    <Route path="/trips" element={<LazyTrips />} />
                    <Route path="/dashboard" element={<LazyDashboard />} />
                    <Route path="/alerts" element={<LazyAlerts />} />
                    <Route path="/monitor-board" element={<LazyMonitorBoard />} />
                    <Route path="/ports" element={<LazyPorts />} />
                    <Route path="/settings" element={<LazySettings />} />
                    <Route path="/users" element={<LazyUsers />} />
                    <Route path="/trips/:id/details" element={<LazyTripDetails />} />
                </Route>

                {/* Public routes */}
                <Route
                    path="/login"
                    element={isAuthenticated ? <Navigate to="/monitor-board" replace /> : <LazyLogin />}
                />
                <Route path="*" element={<LazyPageNotFound />} />
            </Routes>

            {/* If we have a background, also render the modal route on top.
          This will mount the same TripsDetails component but as a modal overlay. */}
            {background && (
                <Routes>
                    <Route path="/trips/:id/details" element={<LazyTripDetails />} />
                </Routes>
            )}
        </>
    );
}
