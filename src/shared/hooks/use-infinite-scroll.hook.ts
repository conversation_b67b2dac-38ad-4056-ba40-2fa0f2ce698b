import { useEffect, useRef } from 'react';

/*
   - A reusable hook that triggers the `onLoadMore` callback when the target element
   - becomes visible within the viewport (using the Intersection Observer API).
 
   => Usage example:
      const { loaderRef } = useInfiniteScroll({ loading, hasMore, onLoadMore });
      <div ref={loaderRef}>Loading...</div>
*/

interface InfiniteScrollProps {
    loading: boolean;
    hasMore: boolean;
    onLoadMore: () => void;
}

export function useInfiniteScroll({ loading, hasMore, onLoadMore }: InfiniteScrollProps) {
    const loaderRef = useRef<HTMLDivElement | null>(null);

    useEffect(() => {
        //--- Do not attach the observer while loading to prevent multiple triggers

        if (loading || !hasMore) return;

        //--- Create an Intersection Observer instance
        const options = { root: null, rootMargin: '0px', threshold: 0.5 };

        const observer = new IntersectionObserver((entries) => {
            const target = entries[0];
            if (target.isIntersecting && !loading) {
                onLoadMore();
            }
        }, options);
        //--- Start observing the target element
        const current = loaderRef.current;
        if (current) observer.observe(current);

        //--- Cleanup observer on component unmount or dependency change
        return () => {
            if (current) observer.unobserve(current);
        };
    }, [loading, hasMore, onLoadMore]);

    return { loaderRef };
}
