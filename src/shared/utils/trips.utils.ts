import { t } from 'i18next';

import type { IconName } from '@/components/common/ui/Icon';
import type { TripState, PortType } from '@/infrastructure/api/trips/types';

import { ChargerStatus } from '../enums';

export type TripStates = {
    id: string;
    icon: IconName;
    value: string | number | boolean;
    textColor: string;
    bgColor: string;
    tooltipKey: string;
    tooltip?: string; // ← optional tooltip
    unit?: string; // ← optional unit (e.g., "%", "km/h", "min")
};

export function constructTripStates(tripState: TripState | null): TripStates[] {
    if (tripState === null || tripState === undefined) return [];

    const states: TripStates[] = [
        {
            id: 'gps-signal',
            icon:
                tripState.gpsSignalStrength > 3
                    ? 'gpsSignalFull'
                    : tripState.gpsSignalStrength > 1
                      ? 'gpsSignalWeak'
                      : 'gpsDisconnected',
            value: tripState.gpsSignalStrength,
            unit: '%',
            textColor: tripState.gpsSignalStrength > 2 ? 'text-green-700' : 'text-red-700',
            bgColor: tripState.gpsSignalStrength > 2 ? 'bg-green-100' : 'bg-red-100',
            tooltipKey: 'tripDetails.statusConditions.gpsSignalStrength',
        },
        {
            id: 'gsm-signal',
            icon:
                tripState.gsmSignalStrength >= 3
                    ? 'signalFull'
                    : tripState.gsmSignalStrength === 2
                      ? 'signalMedium'
                      : tripState.gsmSignalStrength === 1
                        ? 'signalLow'
                        : 'signalNo',
            value: tripState.gsmSignalStrength,
            unit: '%',
            textColor: tripState.gsmSignalStrength > 2 ? 'text-green-700' : 'text-red-700',
            bgColor: tripState.gsmSignalStrength > 2 ? 'bg-green-100' : 'bg-red-100',
            tooltipKey: 'tripDetails.statusConditions.gsmSignalStrength',
        },
        {
            id: 'battery',
            icon:
                tripState.batteryLevelPercentage > 80
                    ? 'batteryFull'
                    : tripState.batteryLevelPercentage > 50
                      ? 'batteryMedium'
                      : tripState.batteryLevelPercentage > 20
                        ? 'batteryLow'
                        : tripState.batteryLevelPercentage > 5
                          ? 'batteryVeryLow'
                          : 'batteryEmpty',
            value: tripState.batteryLevelPercentage,
            unit: '%',
            textColor:
                tripState.batteryLevelPercentage > 50
                    ? 'text-green-700'
                    : tripState.batteryLevelPercentage > 20
                      ? 'text-yellow-700'
                      : 'text-red-700',
            bgColor:
                tripState.batteryLevelPercentage > 50
                    ? 'bg-green-100'
                    : tripState.batteryLevelPercentage > 20
                      ? 'bg-yellow-100'
                      : 'bg-red-100',
            tooltipKey: 'tripDetails.statusConditions.batteryLevel',
        },
        {
            id: 'charger-status',
            icon: tripState.chargerStatus === ChargerStatus.CONNECTED ? 'chargerOn' : 'charger',
            value: tripState.chargerStatus === ChargerStatus.CONNECTED ? 'Connected' : 'Disconnected',
            textColor: tripState.chargerStatus === ChargerStatus.CONNECTED ? 'text-green-700' : 'text-red-700',
            bgColor: tripState.chargerStatus === ChargerStatus.CONNECTED ? 'bg-green-100' : 'bg-red-100',
            tooltipKey: 'tripDetails.statusConditions.chargerStatus',
        },

        // {
        //     id: 'speed',
        //     icon: 'speed',
        //     value: `${tripState.currentSpeed} km/h`,
        //     textColor: tripState.currentSpeed > 0 ? 'text-blue-700' : 'text-gray-700',
        //     bgColor: tripState.currentSpeed > 0 ? 'bg-blue-100' : 'bg-gray-100',
        //     tooltipKey: 'tripDetails.statusConditions.currentSpeed',
        // },
        {
            id: 'zone',
            icon: 'alert',
            value: tripState.isWithinSuspiciousZone ? 'Suspicious' : '',
            textColor: tripState.isWithinSuspiciousZone ? 'text-red-700' : 'text-green-700',
            bgColor: tripState.isWithinSuspiciousZone ? 'bg-red-100' : 'bg-green-100',
            tooltipKey: 'tripDetails.statusConditions.suspiciousZone',
        },
        {
            id: 'route-geofence',
            icon: tripState.isWithinRouteGeofence ? 'enteringGeoFence' : 'leavingGeofence',
            value: tripState.isWithinRouteGeofence ? 'Inside Geofence' : 'Outside Geofence',
            textColor: tripState.isWithinRouteGeofence ? 'text-green-700' : 'text-red-700',
            bgColor: tripState.isWithinRouteGeofence ? 'bg-green-100' : 'bg-red-100',
            tooltipKey: 'tripDetails.statusConditions.routeGeofence',
        },
        // {
        //     id: 'trip-time',
        //     icon: 'tripPin', // 🕒 closest trip-related icon
        //     value: `${tripState.timeElapsedSinceTripStartInMinutes} min`,
        //     textColor: 'text-gray-700',
        //     bgColor: 'bg-gray-100',
        //     tooltipKey: 'tripDetails.statusConditions.timeElapsedSinceTripStart',
        // },
        // {
        //     id: 'remaining-distance',
        //     icon: tripState.remainingDistanceInMeters > 0 ? 'truck' : 'checkPointPin',
        //     value:
        //         tripState.remainingDistanceInMeters > 0
        //             ? `${(tripState.remainingDistanceInMeters / 1000).toFixed(1)} km`
        //             : 'Arrived',
        //     textColor: tripState.remainingDistanceInMeters > 0 ? 'text-blue-700' : 'text-green-700',
        //     bgColor: tripState.remainingDistanceInMeters > 0 ? 'bg-blue-100' : 'bg-green-100',
        //     tooltipKey: 'tripDetails.statusConditions.remainingDistance',
        // },
    ];
    return states
        .filter((s) => s.value !== '')
        .map((s) => ({
            ...s,
            // Build tooltip dynamically: translated key + value + optional unit
            tooltip: s.unit ? `${t(s.tooltipKey)} ${s.value}${s.unit}` : `${t(s.tooltipKey)}`, // translate textual values like "Suspicious"
        }));
}

export function getPortIconColor(Type: PortType): string {
    switch (Type) {
        case 1:
            return 'text-[#179FCA]'; // Sea port
        case 2:
            return 'text-[#FB963C]'; // Air port
        case 3:
            return 'text-[#29BE7A]'; // Land port
        default:
            return 'text-[#29BE7A]';
    }
}
