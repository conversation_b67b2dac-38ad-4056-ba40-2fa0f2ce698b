/** @jsxImportSource react */

import ReactDOMServer from 'react-dom/server';

import { logger } from '@/infrastructure/logging';
import { TripDetailsSection } from '@/components/common/printing-templates/TripDetailsSection';
import { SaudiCustomsTuHeader } from '@/components/common/printing-templates/SaudiCustomsTuHeader';
import type { TripDetail } from '@/infrastructure/api/trips/types';

/**
 * Print map element as image OR (if not present) print header + details *with original styles*.
 */
export async function printMap(mapElementId: string, tripDetail: TripDetail | null) {
    const node = document.getElementById(mapElementId);

    try {
        if (node) {
            // --- Case 1: Map exists → capture image ---
            const inlineSvgIcons = () => {
                const uses = node.querySelectorAll('use');
                uses.forEach((useEl) => {
                    const href = useEl.getAttribute('href') || useEl.getAttribute('xlink:href');
                    if (!href || !href.startsWith('#')) return;
                    const symbolId = href.replace('#', '');
                    const symbol = document.getElementById(symbolId);

                    if (symbol && symbol.tagName === 'symbol') {
                        const parentSvg = useEl.closest('svg');
                        if (parentSvg) {
                            const viewBox = symbol.getAttribute('viewBox') || '';
                            const width = parentSvg.getAttribute('width') || '24';
                            const height = parentSvg.getAttribute('height') || '24';
                            parentSvg.innerHTML = symbol.innerHTML;
                            parentSvg.setAttribute('viewBox', viewBox);
                            parentSvg.setAttribute('width', width);
                            parentSvg.setAttribute('height', height);
                        }
                    }
                });
            };

            inlineSvgIcons();

            // Clone the node to preserve its styles
            const clone = node.cloneNode(true) as HTMLElement;

            const stylesHtml = Array.from(document.querySelectorAll('link[rel="stylesheet"], style'))
                .map((el) => el.outerHTML)
                .join('\n');

            const printableHtml = `
                        <!doctype html>
                        <html>
                          <head>
                            <meta charset="utf-8" />
                            <title>Print Map</title>
                            ${stylesHtml}
                            <style>
              body, html {
                margin: 0;
                padding: 0;
              }
              #print-root {
                width: 100%;
                height: 100%;
              }
                            </style>
                          </head>
                          <body>
                            <div id="print-root"></div>
                          </body>
                        </html>
                      `;

            const printFrame = document.createElement('iframe');
            printFrame.style.position = 'fixed';
            printFrame.style.width = '0';
            printFrame.style.height = '0';
            printFrame.style.border = 'none';
            document.body.appendChild(printFrame);

            const doc = printFrame.contentWindow?.document;
            if (doc) {
                doc.open();
                doc.write(printableHtml);
                doc.close();

                const printRoot = doc.getElementById('print-root');
                if (printRoot) {
                    printRoot.appendChild(clone); // append cloned map
                }

                printFrame.contentWindow?.focus();
                printFrame.contentWindow?.print();
                setTimeout(() => document.body.removeChild(printFrame), 1000);
            }
        } else {
            // --- Case 2: No map → print trip details template ---
            const headerHtml = ReactDOMServer.renderToStaticMarkup(<SaudiCustomsTuHeader />);
            const detailsHtml = ReactDOMServer.renderToStaticMarkup(<TripDetailsSection tripDetail={tripDetail} />);

            // Collect styles (Tailwind + injected)
            const collectStyles = () => {
                const links = Array.from(document.querySelectorAll('link[rel="stylesheet"]'))
                    .map((l) => l.outerHTML)
                    .join('\n');
                const styles = Array.from(document.querySelectorAll('style'))
                    .map((s) => s.outerHTML)
                    .join('\n');
                return links + '\n' + styles;
            };

            const stylesHtml = collectStyles();

            const printableHtml = `
                <!doctype html>
                <html dir="${document.dir || 'ltr'}">
                  <head>
                    <meta charset="utf-8" />
                    <base href="${location.origin}/" />
                    <title>Trip Details</title>
                    ${stylesHtml}
                  </head>
                  <body>
                    <div id="print-root">
                      ${headerHtml}
                      ${detailsHtml}
                    </div>
                  </body>
                </html>
            `;

            const printFrame = document.createElement('iframe');
            printFrame.style.position = 'fixed';
            printFrame.style.width = '0';
            printFrame.style.height = '0';
            printFrame.style.border = 'none';
            document.body.appendChild(printFrame);

            const doc = printFrame.contentWindow?.document;
            if (doc) {
                doc.open();
                doc.write(printableHtml);
                doc.close();

                const onFrameReady = () => {
                    try {
                        printFrame.contentWindow?.focus();
                        printFrame.contentWindow?.print();
                    } catch (err) {
                        logger.error('[map-printer.utils.tsx] printMap failed:', new Error(`printMap failed: ${err}`));
                    } finally {
                        setTimeout(() => document.body.removeChild(printFrame), 1000);
                    }
                };

                let fired = false;
                const win = printFrame.contentWindow;
                if (!win) return;
                win.addEventListener('load', () => {
                    if (fired) return;
                    fired = true;
                    onFrameReady();
                });
                setTimeout(() => {
                    if (fired) return;
                    fired = true;
                    onFrameReady();
                }, 800);
            }
        }
    } catch (err) {
        logger.error('[map-printer.utils.ts] printMap failed:', new Error(`printMap failed: ${err}`));
    }
}
