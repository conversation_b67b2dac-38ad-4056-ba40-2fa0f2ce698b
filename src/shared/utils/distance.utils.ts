import i18n from '@/shared/config/i18n.config';

export function formatDistance(distance: number): string {
    return distance.toFixed(1) + ' ' + i18n.t('common.kmh');
}

export function metersToKilometers(distance: number): number {
    return distance / 1000;
}

export function metersToKilometersFormatted(distance: number): string {
    return formatDistance(metersToKilometers(distance));
}

export function formateBearing(bearing: number): string {
    return bearing.toFixed(1) + '°';
}
