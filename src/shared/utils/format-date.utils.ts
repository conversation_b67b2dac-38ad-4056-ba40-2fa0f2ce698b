import { format } from 'date-fns';
import { enUS, ar } from 'date-fns/locale';

import i18n from '../config/i18n.config';

const formatters = {
    ksaAr: new Intl.DateTimeFormat('ar-SA', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: true,
        timeZone: 'Asia/Riyadh',
    }),
    ksaEn: new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: true,
        timeZone: 'Asia/Riyadh',
    }),
    hijri: new Intl.DateTimeFormat('ar-SA-u-ca-islamic', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: true,
        timeZone: 'Asia/Riyadh',
    }),
};

export function formatLocalizedDate(dateValue: Date | string | undefined): string {
    if (!dateValue) return '-';

    const d = typeof dateValue === 'string' ? new Date(dateValue) : dateValue;

    const isArabic = i18n.language === 'ar';
    const locale = isArabic ? ar : enUS;

    // Time format differs for LTR/RTL
    const time = isArabic
        ? format(d, 'a hh:mm:ss', { locale }) // RTL Arabic
        : format(d, 'hh:mm:ss a', { locale: enUS }); // LTR English

    const date = format(d, 'dd/MM/yyyy', { locale });

    // Return string with correct order
    return isArabic ? `${time} - ${date}` : `${date} - ${time}`;
}

export function formatKSADateTime(dateValue: Date | string | undefined, locale?: 'ar' | 'en' | undefined): string {
    if (!dateValue) return '-';

    const d = ensureDate(dateValue);

    if (!locale) {
        locale = i18n.language === 'ar' ? 'ar' : 'en';
    }

    const isArabic = locale === 'ar';
    const dateTimeFormat = formatters[isArabic ? 'ksaAr' : 'ksaEn'];

    return dateTimeFormat.format(d);
}

export function formatHijriDateTime(dateValue: Date | string | undefined): string {
    if (!dateValue) return '-';

    const d = ensureDate(dateValue);

    const hijriDate = formatters.hijri.format(d);
    return hijriDate;
}

export function ensureDate(dateValue: string | Date): Date {
    return typeof dateValue === 'string' ? new Date(dateValue) : dateValue;
}

export function minutesToHours(minutes: number): number {
    return minutes / 60;
}

export function formatHours(hours: number): string {
    return hours.toFixed(0) + ' ' + i18n.t('common.hour');
}

export function minutesToHoursFormatted(minutes: number): string {
    return formatHours(minutesToHours(minutes));
}

export function formatDuration(from: Date, to: Date): string {
    let diffMs = to.getTime() - from.getTime();

    if (diffMs < 0) diffMs = Math.abs(diffMs);

    const MINUTE = 60 * 1000;
    const HOUR = 60 * MINUTE;
    const DAY = 24 * HOUR;

    const days = Math.floor(diffMs / DAY);
    const hours = Math.floor((diffMs % DAY) / HOUR);
    const minutes = Math.floor((diffMs % HOUR) / MINUTE);

    const parts: string[] = [];

    if (days) parts.push(`${days}d`);
    if (hours) parts.push(`${hours}h`);
    if (minutes || parts.length === 0) parts.push(`${minutes}m`);

    return parts.join(' ');
}

export function formatDurationFromString(from: string, to: string | undefined | null): string {
    const fromDate = new Date(from);
    const toDate = to ? new Date(to) : new Date();

    return formatDuration(fromDate, toDate);
}
