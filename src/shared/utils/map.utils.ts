// New marker icons
import CheckpointIcon from '@/assets/imgs/svg/checkPointPin.svg';
import PoliceStationIcon from '@/assets/imgs/svg/policeCheckPointPin.svg';
import SuspectedAreaIcon from '@/assets/imgs/svg/alert.svg';
import SeaportIcon from '@/assets/imgs/svg/seaPortPin.svg';
import AirportIcon from '@/assets/imgs/svg/airPortPin.svg';
import LandportIcon from '@/assets/imgs/svg/landportPin.svg';
import TripTruckIcon from '@/assets/imgs/svg/tripPin.svg';
import TripAlertIcon from '@/assets/imgs/svg/alertPin-2.svg';
// default icon
import DefaultIcon from '@/assets/imgs/svg/checkPointPin.svg';
import { PortType, MapPointEntryType } from '@/shared/enums';

const mapPointIconDict: Record<string, string> = {
    [`mapPoint_${MapPointEntryType.CHECKPOINT}`]: CheckpointIcon,
    [`mapPoint_${MapPointEntryType.POLICE_STATION}`]: PoliceStationIcon,
    [`mapPoint_${MapPointEntryType.SUSPECTED_AREA}`]: SuspectedAreaIcon,

    [`port_${PortType.SEA_PORT}`]: SeaportIcon,
    [`port_${PortType.AIR_PORT}`]: AirportIcon,
    [`port_${PortType.LAND_PORT}`]: LandportIcon,

    [`trip_truck`]: TripTruckIcon,
    [`trip_alert`]: TripAlertIcon,
};

const DEFAULT_ICON = DefaultIcon;

export function getMapPointIcon(
    entryType: MapPointEntryType | PortType | string,
    type: 'mapPoint' | 'port' | 'trip' = 'mapPoint',
) {
    return mapPointIconDict[`${type}_${entryType}`] ?? DEFAULT_ICON;
}
