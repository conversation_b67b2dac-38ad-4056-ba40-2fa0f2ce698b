import type { IconName } from '@/components/common/ui/Icon';
import TrackerTamperMarker from '@/assets/imgs/svg/trackerTamperMarker.svg';
import TrackerDroppedMarker from '@/assets/imgs/svg/trackerDroppedMarker.svg';
import LockTamperMarker from '@/assets/imgs/svg/lockTamperMarker.svg';
import LockConnectionLostMarker from '@/assets/imgs/svg/lockConnectionLostMarker.svg';
import TrackerBatteryLowMarker from '@/assets/imgs/svg/trackerBatteryLowMarker.svg';
import LockBatteryLowMarker from '@/assets/imgs/svg/lockBatteryLowMarker.svg';
import GsmSignalLostMarker from '@/assets/imgs/svg/gsmSignalLostMarker.svg';
import GpsSignalLostMarker from '@/assets/imgs/svg/gpsSignalLostMarker.svg';
import LockOpenMarker from '@/assets/imgs/svg/lockOpenMarker.svg';
import genericAlertMarker from '@/assets/imgs/svg/genericAlertMarker.svg';
import trackerConnectionLostMarker from '@/assets/imgs/svg/trackerConnectionLostMarker.svg';
import enteringGeofenceMarker from '@/assets/imgs/svg/enteringGeofenceMarker.svg';
import leavingGeofenceMarker from '@/assets/imgs/svg/leavingGeofenceMarker.svg';
import wrongDirectionMarker from '@/assets/imgs/svg/wrongDirectionMarker.svg';
import suspiciousAreaMarker from '@/assets/imgs/svg/suspiciousAreaMarker.svg';
import fourHourStopMarker from '@/assets/imgs/svg/fourHourStopMarker.svg';
import overspeedingMarker from '@/assets/imgs/svg/overspeedingMarker.svg';
import stoppedTruckMarker from '@/assets/imgs/svg/stoppedTruckMarker.svg';

type alertDefinitionType = Record<number, { token: string; icon: IconName; mapMarker: IconName; MarkerImage: string }>;

const alertDefinitions: alertDefinitionType = {
    10000: {
        token: 'tracker_tamper',
        icon: 'trackerTamper',
        mapMarker: 'trackerTamperMarker',
        MarkerImage: TrackerTamperMarker,
    },
    10001: {
        token: 'tracker_dropped',
        icon: 'trackerDropped',
        mapMarker: 'trackerDroppedMarker',
        MarkerImage: TrackerDroppedMarker,
    },
    10002: {
        token: 'lock_tamper',
        icon: 'lockTamper',
        mapMarker: 'lockTamperMarker',
        MarkerImage: LockTamperMarker,
    },
    10003: { token: 'lock_open', icon: 'lockOpen', mapMarker: 'lockOpenMarker', MarkerImage: LockOpenMarker },
    10004: {
        token: 'lock_connection_lost',
        icon: 'lockConnectionLost',
        mapMarker: 'lockConnectionLostMarker',
        MarkerImage: LockConnectionLostMarker,
    },
    10005: {
        token: 'tracker_battery_low',
        icon: 'trackerBatteryLow',
        mapMarker: 'trackerBatteryLowMarker',
        MarkerImage: TrackerBatteryLowMarker,
    },
    10006: {
        token: 'lock_low_battery',
        icon: 'batteryVeryLow',
        mapMarker: 'lockBatteryLowMarker',
        MarkerImage: LockBatteryLowMarker,
    },
    10007: {
        token: 'lock_very_low_battery',
        icon: 'batteryVeryLow',
        mapMarker: 'lockBatteryLowMarker',
        MarkerImage: LockBatteryLowMarker,
    },
    10008: {
        token: 'gsm_signal_lost',
        icon: 'gsmSignalLost',
        mapMarker: 'gsmSignalLostMarker',
        MarkerImage: GsmSignalLostMarker,
    },
    10009: {
        token: 'gps_signal_lost',
        icon: 'gpsSignalLost',
        mapMarker: 'gpsSignalLostMarker',
        MarkerImage: GpsSignalLostMarker,
    },
    10010: {
        token: 'lock_diagnostic_warning',
        icon: 'signalLow',
        mapMarker: 'genericAlertMarker',
        MarkerImage: genericAlertMarker,
    },
    10011: {
        token: 'tracker_diagnostic_warning',
        icon: 'signalLow',
        mapMarker: 'genericAlertMarker',
        MarkerImage: genericAlertMarker,
    },
    10012: {
        token: 'trip_activate',
        icon: 'tripPin',
        mapMarker: 'genericAlertMarker',
        MarkerImage: genericAlertMarker,
    },
    10013: {
        token: 'trip_deactivate',
        icon: 'truck',
        mapMarker: 'genericAlertMarker',
        MarkerImage: genericAlertMarker,
    },
    10014: {
        token: 'tracker_battery_status',
        icon: 'batteryLow',
        mapMarker: 'genericAlertMarker',
        MarkerImage: genericAlertMarker,
    },
    10015: {
        token: 'charger_status_changed',
        icon: 'batteryLow',
        mapMarker: 'genericAlertMarker',
        MarkerImage: genericAlertMarker,
    },
    10016: {
        token: 'gps_signal_status',
        icon: 'gpsDisconnected',
        mapMarker: 'genericAlertMarker',
        MarkerImage: genericAlertMarker,
    },
    10017: {
        token: 'gsm_signal_status',
        icon: 'gpsDisconnected',
        mapMarker: 'genericAlertMarker',
        MarkerImage: genericAlertMarker,
    },
    10018: {
        token: 'lock_open_count',
        icon: 'gpsDisconnected',
        mapMarker: 'genericAlertMarker',
        MarkerImage: genericAlertMarker,
    },
    10019: {
        token: 'lock_diagnostic_information',
        icon: 'gpsDisconnected',
        mapMarker: 'genericAlertMarker',
        MarkerImage: genericAlertMarker,
    },
    10020: {
        token: 'tracker_diagnostic_information',
        icon: 'gpsDisconnected',
        mapMarker: 'genericAlertMarker',
        MarkerImage: genericAlertMarker,
    },
    10021: {
        token: 'geofence_entry_breach',
        icon: 'enteringGeoFence',
        mapMarker: 'enteringGeofenceMarker',
        MarkerImage: enteringGeofenceMarker,
    },
    10022: {
        token: 'geofence_exit_breach',
        icon: 'leavingGeofence',
        mapMarker: 'leavingGeofenceMarker',
        MarkerImage: leavingGeofenceMarker,
    },
    10023: {
        token: 'tracker_connection_lost',
        icon: 'trackerConnectionLost',
        mapMarker: 'trackerConnectionLostMarker',
        MarkerImage: trackerConnectionLostMarker,
    },
    10024: {
        token: 'trip_distance_exceeded',
        icon: 'tripDistanceExceeded',
        mapMarker: 'genericAlertMarker',
        MarkerImage: genericAlertMarker,
    },
    10025: {
        token: 'trip_time_exceeded',
        icon: 'tripTimeExceeded',
        mapMarker: 'genericAlertMarker',
        MarkerImage: genericAlertMarker,
    },
    10040: {
        token: 'over_speeding',
        icon: 'overSpeeding',
        mapMarker: 'overspeedingMarker',
        MarkerImage: overspeedingMarker,
    },
    10060: {
        token: 'truck_stopped',
        icon: 'truckStopped',
        mapMarker: 'truckStopped',
        MarkerImage: stoppedTruckMarker,
    },
    10061: {
        token: 'wrong_direction',
        icon: 'wrongDirection',
        mapMarker: 'wrongDirectionMarker',
        MarkerImage: wrongDirectionMarker,
    },
    10062: {
        token: 'lock_message_count',
        icon: 'gpsDisconnected',
        mapMarker: 'genericAlertMarker',
        MarkerImage: genericAlertMarker,
    },
    10063: {
        token: 'report_interval_change',
        icon: 'gpsDisconnected',
        mapMarker: 'genericAlertMarker',
        MarkerImage: genericAlertMarker,
    },
    10066: {
        token: 'suspected_area',
        icon: 'gpsDisconnected',
        mapMarker: 'suspiciousAreaMarker',
        MarkerImage: suspiciousAreaMarker,
    },
    10067: {
        token: 'truck_moved',
        icon: 'gpsDisconnected',
        mapMarker: 'genericAlertMarker',
        MarkerImage: genericAlertMarker,
    },
    10080: {
        token: '4_hours_exceeded',
        icon: 'fourHourExceeded',
        mapMarker: 'fourHourStopMarker',
        MarkerImage: fourHourStopMarker,
    },
    10101: {
        token: 'entry_into_customs_area',
        icon: 'gpsDisconnected',
        mapMarker: 'genericAlertMarker',
        MarkerImage: genericAlertMarker,
    },
    10102: {
        token: 'door_forced_open',
        icon: 'gpsDisconnected',
        mapMarker: 'genericAlertMarker',
        MarkerImage: genericAlertMarker,
    },
} as const;

export type AlertTypeId = keyof typeof alertDefinitions;
export type AlertTypeTokenName = (typeof alertDefinitions)[AlertTypeId]['token'];

export const AlertTypeMapById: Record<AlertTypeId, AlertTypeTokenName> = Object.fromEntries(
    Object.entries(alertDefinitions).map(([id, def]) => [Number(id), def.token]),
) as Record<AlertTypeId, AlertTypeTokenName>;

export const AlertTypeMapByName: Record<AlertTypeTokenName, AlertTypeId> = Object.fromEntries(
    Object.entries(alertDefinitions).map(([id, def]) => [def.token, Number(id)]),
) as Record<AlertTypeTokenName, AlertTypeId>;

export const alertIconMapping: Record<AlertTypeId, IconName> = Object.fromEntries(
    Object.entries(alertDefinitions).map(([id, def]) => [Number(id), def.icon]),
) as Record<AlertTypeId, IconName>;

// get map Marker by alert type id
export const getAlertMarkerByAlertTypeId = (alertTypeId: AlertTypeId) => {
    return alertDefinitions[alertTypeId].mapMarker ?? 'genericAlertMarker';
};

export const getAlertIconByAlertTypeId = (alertTypeId: AlertTypeId) => {
    return alertDefinitions[alertTypeId].icon ?? '';
};

export const getAlertMarkerImageByAlertTypeId = (alertTypeId: AlertTypeId) => {
    return alertDefinitions[alertTypeId].MarkerImage ?? genericAlertMarker;
};

// Helper to map route zone numbers to translation keys
export const getZoneLabel = (zoneValue: number): string => {
    switch (zoneValue) {
        case 1:
            return 'inEntryPort';
        case 2:
            return 'onRoute';
        case 3:
            return 'inExitPort';
        default:
            return 'unknown';
    }
};

// Helper to format from-to values
export const formatFromTo = (fromVal: string | number | boolean, toVal?: string | number | boolean) => {
    const format = (val: string | number | boolean) => (val === true ? 'Yes' : val === false ? 'No' : val);
    return toVal !== undefined && toVal !== null ? `${format(fromVal)}\n${format(toVal)}` : `${format(fromVal)}`;
};
