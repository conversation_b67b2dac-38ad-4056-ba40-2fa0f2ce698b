import { logger } from '@/infrastructure/logging';

import { appConfig } from './app-settings.config';

export default async function registerDataMock() {
    if (!appConfig.get('mockApiData')) {
        return;
    }

    logger.info('Starting MSW (Mock Service Worker)...');
    const { worker } = await import('@/infrastructure/mocks/browser');
    await worker.start({
        onUnhandledRequest: 'bypass',
    });

    logger.info('MSW started successfully!');
}
