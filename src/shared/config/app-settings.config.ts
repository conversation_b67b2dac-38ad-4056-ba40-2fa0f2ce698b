// appSettings.ts
export interface AppSettings {
    apiUrl: string;
    googleDefaultMapCenter: { lat: number; lng: number };
    googleDefaultMapZoom: number;
    googleMapsApiKey: string;
    hubBaseUrl: string;
    mockApiData: boolean;
    disableAlertHub: boolean;
    highlightZoom: number;
    googleMapId: string;
    markerHighlightDurationInMs: number;
    pdfPreviewEnabled: boolean;
}

class AppConfig {
    private settings: AppSettings;
    private static instance: AppConfig;

    private constructor() {
        this.settings = this.loadSettings();
    }

    public static getInstance(): AppConfig {
        if (!AppConfig.instance) {
            AppConfig.instance = new AppConfig();
        }
        return AppConfig.instance;
    }

    private loadSettings(): AppSettings {
        // Default values
        const defaultZoom = 6;

        return {
            apiUrl: import.meta.env.VITE_TTS_API_URL || '',
            googleDefaultMapCenter: {
                lat: parseFloat(import.meta.env.VITE_TTS_GOOGLE_MAP_DEFAULT_LAT ?? ''),
                lng: parseFloat(import.meta.env.VITE_TTS_GOOGLE_MAP_DEFAULT_LONG ?? ''),
            },
            googleDefaultMapZoom: parseFloat(import.meta.env.VITE_TTS_GOOGLE_MAP_ZOOM ?? '') || defaultZoom,
            googleMapsApiKey: import.meta.env.VITE_TTS_GOOGLE_MAPS_API_KEY || '',
            hubBaseUrl: import.meta.env.VITE_TTS_HUB_BASE_URL || '',
            mockApiData: import.meta.env.VITE_TTS_MOCK_API_DATA === 'true',
            disableAlertHub: import.meta.env.VITE_TTS_DISABLE_ALERT_HUB === 'true',
            highlightZoom: parseInt(import.meta.env.VITE_TTS_HIGHLIGHT_ZOOM) || defaultZoom,
            googleMapId: import.meta.env.VITE_TTS_GOOGLE_MAPS_MAP_ID || '',
            markerHighlightDurationInMs: Number(import.meta.env.VITE_TTS_MARKER_HIGHLIGHT_DURATION_IN_MS) || 3000,
            pdfPreviewEnabled: import.meta.env.VITE_TTS_PDF_PREVIEW_ENABLED === 'true',
        };
    }

    /** Get a single setting by key */
    public get<K extends keyof AppSettings>(section: K): AppSettings[K] {
        return this.settings[section];
    }

    /** Get all settings */
    public getAll(): AppSettings {
        return { ...this.settings };
    }

    /** Update settings at runtime */
    public updateSettings(updates: Partial<AppSettings>): void {
        this.settings = { ...this.settings, ...updates };
    }
}

export const appConfig = AppConfig.getInstance();
