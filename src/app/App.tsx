import { BrowserRouter } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';

import AppRoutes from '../routes/routes';

function App() {
    return (
        /* BrowserRouter enables client-side routing for the entire application */
        <BrowserRouter>
            <AppRoutes />
            {/* Toaster will take care of rendering all notifications/feedback emitted */}
            <Toaster position="top-center" />
        </BrowserRouter>
    );
}

export default App;
