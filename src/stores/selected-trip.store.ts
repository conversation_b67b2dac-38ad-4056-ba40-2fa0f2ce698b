// import { create } from 'zustand';
// import { devtools, subscribeWithSelector } from 'zustand/middleware';

// import { logger } from '@/infrastructure/logging';

// export interface Trip {
//     id: number;
//     name: string;
//     description: string;
//     transitNumber: string;
//     transitType: string;
//     declarationDate: string;
//     transitSeqNo: string;
//     ownerDescription: string;
//     entryPort: string;
//     exitPort: string;
//     startingDate: string;
//     expectedArrivalDate: string;
//     endDate: string;
//     trackerNo: string;
//     elocks: string;
//     shipmentDescription: string;
//     vehicleDetails: string;
//     driverName: string;
//     driverPassportNumber: string;
//     driverNationality: string;
//     driverContactNo: string;
//     securityNotes: string;
//     completeDistance: string;
//     remainingDistance: string;
//     status: string;
// }

// const fakeTrip: Trip = {
//     id: 1,
//     name: 'Trip 1',
//     description: 'Description 1',
//     transitNumber: '1234567890',
//     transitType: 'Type 1',
//     declarationDate: '2021-01-01',
//     transitSeqNo: '1234567890',
//     ownerDescription: 'Owner Description 1',
//     entryPort: 'Entry Port 1',
//     exitPort: 'Exit Port 1',
//     startingDate: '2021-01-01',
//     expectedArrivalDate: '2021-01-01',
//     endDate: '2021-01-01',
//     trackerNo: '1234567890',
//     elocks: '1234567890',
//     shipmentDescription: 'Shipment Description 1',
//     vehicleDetails: 'Vehicle Details 1',
//     driverName: 'Driver Name 1',
//     driverPassportNumber: '1234567890',
//     driverNationality: 'Nationality 1',
//     driverContactNo: '1234567890',
//     securityNotes: 'Security Notes 1',
//     completeDistance: '1234567890',
//     remainingDistance: '1234567890',
//     status: 'Status 1',
// };

// export interface SelectedTripState {
//     selectedTrip: Trip | null;
// }

// export interface SelectedTripActions {
//     loadTrip: (tripId: number) => void;
// }

// export type SelectedTripStoreType = SelectedTripState & SelectedTripActions;

// const _selectedTripStore = (instanceId: string) => {
//     // eslint-disable-next-line @typescript-eslint/no-explicit-any
//     return (set: any): SelectedTripStoreType => ({
//         selectedTrip: fakeTrip,
//         loadTrip: async (tripId: number) => {
//             await new Promise((resolve) => setTimeout(resolve, 100)); // TODO do the actual api call
//             logger.info('loadTrip', { instanceId, tripId });
//             set({ selectedTrip: fakeTrip });
//         },
//     });
// };

// export const useSelectedTripStore = create<SelectedTripStoreType>()(
//     subscribeWithSelector(devtools(_selectedTripStore('main'), { name: 'selected-trip-store' })),
// );
