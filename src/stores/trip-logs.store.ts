import { create, type StateCreator } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';

import { logger } from '@/infrastructure/logging';
import type { TripLog, GetTripLogsQueryParams } from '@/infrastructure/api/trip-logs/types';
import { DEFAULT_PAGINATION, type Pagination } from '@/infrastructure/validation/schemas/pagination.schema';
import { tripLogsService } from '@/infrastructure/api/trip-logs/trip-logs.service';

export type TripLogsStoreType = TripLogsState & TripLogsActions;

interface TripLogsState {
    tripLogs: TripLog[];
    pagination: Pagination;
    isLoading: boolean;
    currentTripId: number | null;
}

const DEFAULT_STATE: TripLogsState = {
    tripLogs: [],
    pagination: DEFAULT_PAGINATION,
    isLoading: false,
    currentTripId: null,
};

interface TripLogsActions {
    loadTripLogs: (params: GetTripLogsQueryParams) => Promise<void>;
    clearTripLogs: () => void;
    reset: () => void;
}

const _tripLogsStore = (instanceId: string): StateCreator<TripLogsStoreType> => {
    return (set, get): TripLogsStoreType => ({
        ...DEFAULT_STATE,

        loadTripLogs: async (params: GetTripLogsQueryParams) => {
            const { isLoading } = get();
            if (isLoading) return;

            set({ isLoading: true });

            try {
                const response = await tripLogsService.getTripLogs(params);

                set({
                    tripLogs: response.data,
                    pagination: response.pagination,
                    currentTripId: params.tripId,
                    isLoading: false,
                });

                logger.info(`tripLogsStore(${instanceId}): loadTripLogs success`, {
                    tripId: params.tripId,
                    count: response.data.length,
                });
            } catch (error) {
                logger.error(`tripLogsStore(${instanceId}): loadTripLogs error`, error as Error);
                set({
                    isLoading: false,
                    tripLogs: [],
                    pagination: DEFAULT_PAGINATION,
                });
            }
        },

        clearTripLogs: () => {
            set({ ...DEFAULT_STATE });
            logger.info(`tripLogsStore(${instanceId}): clearTripLogs`);
        },

        reset: () => {
            set(DEFAULT_STATE);
            logger.info(`tripLogsStore(${instanceId}): reset to default state`);
        },
    });
};

export const useTripLogsStore = create<TripLogsStoreType>()(
    subscribeWithSelector(devtools(_tripLogsStore('main'), { name: 'trip-logs-store' })),
);

export const useTripLogsLookups = () =>
    useTripLogsStore((state) => state.tripLogs).map((log) => ({
        id: log.id,
        log: log.log,
        user: log.user.name,
        action: log.action,
        scope: log.scope,
        createdAt: log.createdAt,
    }));
