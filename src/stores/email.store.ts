import { create, type StateCreator } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';

import { logger } from '@/infrastructure/logging';
import type { SendEmailRequest, SendEmailResponse } from '@/infrastructure/api/email/types';
import { emailService } from '@/infrastructure/api/email/email.service';

// ---------------------------------------------------
// ---------------------- types ----------------------
// ---------------------------------------------------
export type EmailStoreType = EmailState & EmailActions;

// ---------------------------------------------------
// ---------------------- state ----------------------
// ---------------------------------------------------
interface EmailState {
    to: string[];
    cc: string[];
    subject: string;
    body: string;
    scope: number | null;
    lastEmail: SendEmailResponse | null;
    isLoading: boolean;
    error: string | null;
}

// ----------------------------------------------------------
// ---------------------- default state ---------------------
// ----------------------------------------------------------
const DEFAULT_STATE: EmailState = {
    to: [],
    cc: [],
    subject: '',
    body: '',
    scope: null,
    lastEmail: null,
    isLoading: false,
    error: null,
};

// -----------------------------------------------------
// ---------------------- actions ----------------------
// -----------------------------------------------------
interface EmailActions {
    setTo: (emails: string[]) => void;
    setCc: (emails: string[]) => void;
    setSubject: (subject: string) => void;
    setBody: (body: string) => void;
    setScope: (scope: number) => void;
    sendEmail: () => Promise<void>;
    reset: () => void;
}

// ---------------------------------------------------
// ---------------------- store ----------------------
// ---------------------------------------------------
const _emailStore = (instanceId: string): StateCreator<EmailStoreType> => {
    return (set, get): EmailStoreType => ({
        ...DEFAULT_STATE,

        setTo: (emails) => set((state) => ({ ...state, to: emails })),
        setCc: (emails) => set((state) => ({ ...state, cc: emails })),
        setSubject: (subject) => set((state) => ({ ...state, subject })),
        setBody: (body) => set((state) => ({ ...state, body })),
        setScope: (scope) => set((state) => ({ ...state, scope })),

        sendEmail: async () => {
            const { to, cc, subject, body, scope } = get();

            if (!scope) {
                logger.error(`emailStore(${instanceId}): scope not set`);
                set((s) => ({ ...s, error: 'Scope is required' }));
                return;
            }

            set((state) => ({ ...state, isLoading: true, error: null }));
            try {
                const payload: SendEmailRequest = { to, cc, subject, body, scope };
                const res = await emailService.sendEmail(payload);

                if (res) {
                    set((state) => ({
                        ...state,
                        lastEmail: res,
                        isLoading: false,
                        error: null,
                    }));
                    logger.info(`emailStore(${instanceId}): sendEmail success: `, res);
                } else {
                    set((state) => ({
                        ...state,
                        isLoading: false,
                        error: 'Failed to send email',
                    }));
                    logger.warn(`emailStore(${instanceId}): sendEmail returned null`);
                }
            } catch (error) {
                logger.error(`emailStore(${instanceId}): sendEmail error: `, error as Error);
                set((state) => ({
                    ...state,
                    isLoading: false,
                    error: (error as Error).message,
                }));
            }
        },

        reset: () => {
            set(() => DEFAULT_STATE);
            logger.info(`emailStore(${instanceId}): state reset`);
        },
    });
};

// ----------------------------------------------------------
// ---------------------- export store ----------------------
// ----------------------------------------------------------
export const useEmailStore = create<EmailStoreType>()(
    subscribeWithSelector(devtools(_emailStore('main'), { name: 'email-store' })),
);
