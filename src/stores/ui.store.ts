// uiStore.ts
// ---------------------------------------------------
// ---------------------- imports --------------------
// ---------------------------------------------------
import { create, type StateCreator } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';

import { logger } from '@/infrastructure/logging';

// ---------------------------------------------------
// ---------------------- types ----------------------
// ---------------------------------------------------
export type Tool = 'ruler' | 'tabs' | null;
export type InfoWindowType = { kind: 'trip' | 'point' | 'port' | 'alert' | 'activity'; id: string | number } | null;

export type UIStoreType = UIState & UIActions;

// ---------------------------------------------------
// ---------------------- state ----------------------
// ---------------------------------------------------
interface UIState {
    activeTool: Tool;
    activeTab: string | null;
    activeInfoWindow: InfoWindowType;
}

// ----------------------------------------------------------
// ---------------------- default state ---------------------
// ----------------------------------------------------------
const DEFAULT_STATE: UIState = {
    activeTool: null,
    activeTab: null,
    activeInfoWindow: null,
};

// -----------------------------------------------------
// ---------------------- actions ----------------------
// -----------------------------------------------------
interface UIActions {
    setActiveTool: (tool: Tool) => void;
    setActiveTab: (tab: string | null) => void;
    setActiveInfoWindow: (info: InfoWindowType) => void;
    closeAll: () => void;
}

// ---------------------------------------------------
// ---------------------- store ----------------------
// ---------------------------------------------------
const _uiStore = (instanceId: string): StateCreator<UIStoreType> => {
    return (set): UIStoreType => ({
        ...DEFAULT_STATE,

        setActiveTool: (tool) => {
            if (tool === 'ruler') {
                set(() => ({
                    activeTool: 'ruler',
                    activeTab: null,
                    activeInfoWindow: null,
                }));
                logger.info(`uiStore(${instanceId}): setActiveTool -> ruler`);
            } else {
                set(() => ({ activeTool: null }));
                logger.info(`uiStore(${instanceId}): setActiveTool -> null`);
            }
        },

        setActiveTab: (tab) => {
            if (tab) {
                set(() => ({
                    activeTool: 'tabs',
                    activeTab: tab,
                    activeInfoWindow: null,
                }));
                logger.info(`uiStore(${instanceId}): setActiveTab -> ${tab}`);
            } else {
                set(() => ({
                    activeTool: null,
                    activeTab: null,
                }));
                logger.info(`uiStore(${instanceId}): setActiveTab -> null`);
            }
        },

        setActiveInfoWindow: (info) => {
            if (info) {
                set(() => ({
                    activeInfoWindow: info,
                    activeTool: null,
                    // activeTab: null,
                }));
                logger.info(`uiStore(${instanceId}): setActiveInfoWindow -> `, info);
            } else {
                set(() => ({ activeInfoWindow: null }));
                logger.info(`uiStore(${instanceId}): setActiveInfoWindow -> null`);
            }
        },

        closeAll: () => {
            set(() => ({
                activeTool: null,
                activeTab: null,
                activeInfoWindow: null,
            }));
            logger.info(`uiStore(${instanceId}): closeAll`);
        },
    });
};

// ----------------------------------------------------------
// ---------------------- export store ----------------------
// ----------------------------------------------------------
export const useUIStore = create<UIStoreType>()(
    subscribeWithSelector(devtools(_uiStore('main'), { name: 'ui-store' })),
);
