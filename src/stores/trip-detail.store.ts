import { create, type StateCreator } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';

import { logger } from '@/infrastructure/logging';
import type { GetTripDetailQueryParams, TripDetail } from '@/infrastructure/api/trips/types';
import { tripsService } from '@/infrastructure/api/trips/trips.service';
import type { GetPortByIdApiResponse } from '@/infrastructure/api/ports/types';
import { portService } from '@/infrastructure/api/ports/port.service';
import type { Location } from '@/shared/types/common';

// ---------------------------------------------------
// ---------------------- types ----------------------
// ---------------------------------------------------
export type TripDetailStoreType = TripDetailState & TripDetailActions;
export type Trip = TripDetail;
export type Port = GetPortByIdApiResponse;

// ---------------------------------------------------
// ---------------------- state ----------------------
// ---------------------------------------------------
interface TripDetailState {
    tripDetail: Trip | null;
    EntryPort: Port | null;
    ExitPort: Port | null;
    CurrentLocation: Location | null;
    isLoading: boolean; // TODO:: this loader is not effective with multiple requests,
    isLoadingTripDetail: boolean;
}

// ----------------------------------------------------------
// ---------------------- default state ---------------------
// ----------------------------------------------------------
const DEFAULT_STATE: TripDetailState = {
    tripDetail: null,
    EntryPort: null,
    ExitPort: null,
    CurrentLocation: null,
    isLoading: false,
    isLoadingTripDetail: false,
};

// -----------------------------------------------------
// ---------------------- actions ----------------------
// -----------------------------------------------------
interface TripDetailActions {
    loadTripDetail: (params?: Partial<GetTripDetailQueryParams>) => Promise<void>;
    loadEntryPort: (portId: number) => Promise<void>;
    loadExitPort: (portId: number) => Promise<void>;
    endTrip: (tripId: number, reason: string) => Promise<void>;
    setCurrentLocation: (location: Location) => void;
    markAsFocused: (tripId: number, isFocused: boolean) => Promise<void>;
    markAsSuspicious: (tripId: number, isSuspicious: boolean) => Promise<void>;
    updateSecurityNotes: (tripId: number, securityNotes: string | null) => Promise<void>;
    reset: () => void;
}

// ---------------------------------------------------
// ---------------------- store ----------------------
// ---------------------------------------------------
const _tripDetailStore = (instanceId: string): StateCreator<TripDetailStoreType> => {
    return (set, get): TripDetailStoreType => ({
        ...DEFAULT_STATE,

        loadTripDetail: async (params?: Partial<GetTripDetailQueryParams>) => {
            // Prevent duplicate loads
            if (get().isLoadingTripDetail) return;

            set({ isLoadingTripDetail: true, isLoading: true });
            try {
                const res = await tripsService.getTripDetail({ ...params } as GetTripDetailQueryParams);

                set((state: TripDetailState) => ({
                    ...state,
                    tripDetail: res,
                    isLoadingTripDetail: false,
                    isLoading: false,
                }));
                logger.info(`tripDetailStore(${instanceId}): loadTripDetail: status update to `, res ?? {});

                if (res)
                    get().setCurrentLocation({ lat: res.currentState?.lat ?? 0, lng: res.currentState?.long ?? 0 });
                logger.info(`tripDetailStore(${instanceId}): loadTripDetail: current location update to `, res ?? {});
            } catch (error) {
                logger.error(`tripDetailStore(${instanceId}): loadTripDetail: error: `, error as Error);
                set({ isLoadingTripDetail: false, isLoading: false });
            }
        },
        setCurrentLocation: (location: Location) => {
            set((state: TripDetailState) => ({ ...state, CurrentLocation: location }));
        },
        //TODO:: extract loadPort to a separate method and use it with both loadEntry and loadExit
        loadEntryPort: async (portId: number) => {
            set({ isLoading: true });
            try {
                const res = await portService.getPortById(portId);
                if (res) {
                    set((state: TripDetailState) => ({ ...state, EntryPort: res }));
                }
            } catch (error) {
                logger.error(`tripDetailStore(${instanceId}): loadPort error: `, error as Error);
            } finally {
                set({ isLoading: false });
            }
        },

        loadExitPort: async (portId: number) => {
            set({ isLoading: true });
            try {
                const res = await portService.getPortById(portId);
                if (res) {
                    set((state: TripDetailState) => ({ ...state, ExitPort: res }));
                }
            } catch (error) {
                logger.error(`tripDetailStore(${instanceId}): loadPort error: `, error as Error);
            } finally {
                set((state: TripDetailState) => ({ ...state, isLoading: false }));
            }
        },
        endTrip: async (tripId: number, reason: string) => {
            set({ isLoading: true });
            try {
                await tripsService.endTrip(tripId, { isEnded: true, reason });
                await get().loadTripDetail({ id: tripId }); // refresh
            } catch (error) {
                logger.error(`tripDetailStore(${instanceId}): endTrip error: `, error as Error);
            } finally {
                set({ isLoading: false });
            }
        },

        markAsFocused: async (tripId: number, isFocused: boolean) => {
            set({ isLoading: true });
            try {
                await tripsService.markTripAsFocused(tripId, { isFocused });
                // set({ tripDetail: { ...get().tripDetail, isFocused } as TripDetail });
                await get().loadTripDetail({ id: tripId }); // refresh
            } catch (error) {
                logger.error(`tripDetailStore(${instanceId}): markAsFocused error: `, error as Error);
            } finally {
                set({ isLoading: false });
            }
        },

        markAsSuspicious: async (tripId: number, isSuspicious: boolean) => {
            set({ isLoading: true });
            try {
                await tripsService.markTripAsSuspicious(tripId, { isSuspicious });
                // set({ tripDetail: { ...get().tripDetail, isSuspicious } as TripDetail });
                await get().loadTripDetail({ id: tripId }); // refresh after update
            } catch (error) {
                logger.error(`tripDetailStore(${instanceId}): markAsSuspicious error: `, error as Error);
            } finally {
                set({ isLoading: false });
            }
        },

        updateSecurityNotes: async (tripId: number, securityNotes: string | null) => {
            set({ isLoading: true });
            try {
                await tripsService.updateSecurityNotes(tripId, securityNotes);
                await get().loadTripDetail({ id: tripId }); // refresh after update
            } catch (error) {
                logger.error(`tripDetailStore(${instanceId}): updateSecurityNotes error: `, error as Error);
            } finally {
                set({ isLoading: false });
            }
        },

        reset: () => {
            set(DEFAULT_STATE);
            logger.info(`tripDetailStore(${instanceId}): reset to default state`);
        },
    });
};

// ----------------------------------------------------------
// ---------------------- export store ----------------------
// ----------------------------------------------------------
export const useTripDetailStore = create<TripDetailStoreType>()(
    subscribeWithSelector(devtools(_tripDetailStore('main'), { name: 'trip-detail-store' })),
);

// ------------------------------------------------------------
// ---------------------- export lookups ----------------------
// ------------------------------------------------------------
