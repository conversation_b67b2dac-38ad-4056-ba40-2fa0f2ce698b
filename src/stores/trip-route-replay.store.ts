import { create, type StateCreator } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';

// ---------------------------------------------------
// ---------------------- types ----------------------
// ---------------------------------------------------

export type TripRouteReplayStoreType = TripRouteReplayState & TripRouteReplayActions;
export type TripRouteReplayDirectionType = 'forward' | 'rewind';
export type TripRouteReplayTypeSpeed = 1 | 1.5 | 2;

// ---------------------------------------------------
// ---------------------- state ----------------------
// ---------------------------------------------------

interface TripRouteReplayState {
    isPlaying: boolean;
    direction: TripRouteReplayDirectionType;
    speed: TripRouteReplayTypeSpeed;
}

// ----------------------------------------------------------
// ---------------------- default state ---------------------
// ----------------------------------------------------------

const DEFAULT_STATE: TripRouteReplayState = {
    isPlaying: false,
    direction: 'forward',
    speed: 1.5,
};

// -----------------------------------------------------
// ---------------------- actions ----------------------
// -----------------------------------------------------

interface TripRouteReplayActions {
    changeRoutePlayingStatus: (isPlaying: boolean) => void;
    changeRoutePlayingSpeed: (speed: TripRouteReplayTypeSpeed) => void;
    changeRoutePlayingDirection: (direction: TripRouteReplayDirectionType) => void;
    reset: () => void;
}

// ---------------------------------------------------
// ---------------------- store ----------------------
// ---------------------------------------------------

const _tripReplayStore = (): StateCreator<TripRouteReplayStoreType> => {
    return (set): TripRouteReplayStoreType => {
        return {
            ...DEFAULT_STATE,

            changeRoutePlayingStatus: (isPlaying: boolean) => {
                set({ isPlaying });
            },

            changeRoutePlayingSpeed: (speed: TripRouteReplayTypeSpeed) => {
                set({ speed });
            },

            changeRoutePlayingDirection: (direction: TripRouteReplayDirectionType) => {
                set({ direction });
            },

            reset: () => {
                set(DEFAULT_STATE);
            },
        };
    };
};

// ----------------------------------------------------------
// ---------------------- export store ----------------------
// ----------------------------------------------------------

export const useTripReplayStore = create<TripRouteReplayStoreType>()(
    subscribeWithSelector(devtools(_tripReplayStore(), { name: 'trip-replay-store' })),
);
