import { create, type StateCreator } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';

import { logger } from '@/infrastructure/logging';
import type {
    TripLocation,
    GetTripLocationsQueryParams as GetTripPingsQueryParams,
} from '@/infrastructure/api/trip-pings/types';
import { DEFAULT_PAGINATION, type Pagination } from '@/infrastructure/validation/schemas/pagination.schema';
import { tripLocationsService } from '@/infrastructure/api/trip-pings/trip-pings.service';
import type { GroupName, SubscriptionId } from '@/shared/lib/SignalR';
import { appConfig } from '@/shared/config/app-settings.config';
import { tripHub } from '@/infrastructure/signalr/trip/trip.hub';
import type { LocationUpdateMessage, LocationUpdateMessagePayload } from '@/infrastructure/signalr/trip/types';
// ---------------------------------------------------
// ---------------------- types ----------------------
// ---------------------------------------------------
export type TripPingsStoreType = TripPingsState & TripPingsActions;
export type TripPing = TripLocation;
const subscriptionKeys: Record<GroupName, SubscriptionId> = {};

// ---------------------------------------------------
// ---------------------- state ----------------------
// ---------------------------------------------------
interface TripPingsState {
    tripPings: TripPing[];
    selectedTripPings: TripPing[];
    pagination: Pagination;
    isLoading: boolean;
    currentTripId: number | null;
    isConnectingToHub: boolean;
    lastLocationUpdate: LocationUpdateMessagePayload | null;
}

// ----------------------------------------------------------
// ---------------------- default state ---------------------
// ----------------------------------------------------------
const DEFAULT_STATE: TripPingsState = {
    tripPings: [],
    selectedTripPings: [],
    pagination: DEFAULT_PAGINATION,
    isLoading: false,
    currentTripId: null,
    isConnectingToHub: false,
    lastLocationUpdate: null,
};

// -----------------------------------------------------
// ---------------------- actions ----------------------
// -----------------------------------------------------
interface TripPingsActions {
    loadTripPings: (params: GetTripPingsQueryParams) => Promise<void>;
    selectTripPings: (tripPings: TripPing[]) => void;
    selectAllTripPings: () => void;
    clearSelectedTripPings: () => void;
    clearTripPings: () => void;
    listenToLocationUpdates: (tripId: number) => void;
    unListenToLocationUpdates: () => void;
    reset: () => void;
}

const tripPingMapper = (locationUpdate: LocationUpdateMessagePayload): TripPing => {
    return {
        id: Date.now(), // Generate a temporary ID for real-time updates
        trackerDateTime: new Date().toISOString(),
        serverDateTime: new Date().toISOString(),
        density: 1, // Default density for real-time updates
        location: {
            latitude: locationUpdate.newLocation.lat,
            longitude: locationUpdate.newLocation.long,
        },
        address: {
            arabic: 'test address coming from generated content, location update',
            english: 'test address coming from generated content, location update',
        }, // Address not available in real-time updates
        bearing: locationUpdate.newLocation.bearing,
        speed: 0, // Speed not available in location updates
        batteryLevelPercentage: 0, // Not available in real-time updates
        chargerStatus: 0, // Not available in real-time updates
        gpsSignalStrength: 0, // Not available in real-time updates
        gsmSignalStrength: 0, // Not available in real-time updates
        routeZone: 0, // Not available in real-time updates
        isWithinRouteGeofence: false, // Not available in real-time updates
        timeElapsedSinceTripStartInMinutes: 0, // Not available in real-time updates
        remainingDistanceInMeters: 0, // Not available in real-time updates
        completeDistanceInMeters: 0, // Not available in real-time updates
        isWithinSuspiciousZone: false, // Not available in real-time updates
    };
};

// ---------------------------------------------------
// ---------------------- store ----------------------
// ---------------------------------------------------
const _tripPingsStore = (instanceId: string): StateCreator<TripPingsStoreType> => {
    return (set, get): TripPingsStoreType => {
        const _addLocationUpdate = (locationUpdate: LocationUpdateMessagePayload) => {
            const d = tripPingMapper(locationUpdate);
            set((state: TripPingsState) => ({
                ...state,
                tripPings: [d, ...state.tripPings], // TODO location update need to be mapped + add to filter if filter applied
                lastLocationUpdate: locationUpdate,
            }));

            logger.info(`${instanceId}: addLocationUpdate: location update added`, { locationUpdate });
        };
        return {
            ...DEFAULT_STATE,

            loadTripPings: async (params: GetTripPingsQueryParams) => {
                const { currentTripId, isLoading } = get();

                // Prevent duplicate loads for same trip
                if (isLoading && currentTripId === params.tripId) {
                    return;
                }

                set((state: TripPingsState) => ({ ...state, isLoading: true }));

                try {
                    const response = await tripLocationsService.getTripLocations(params);

                    set((state: TripPingsState) => ({
                        ...state,
                        tripPings: response.data,
                        pagination: response.pagination,
                        currentTripId: params.tripId,
                        isLoading: false,
                    }));

                    logger.info(`tripPingsStore(${instanceId}): loadTripPings: status update`, {
                        tripId: params.tripId,
                        count: response.data.length,
                    });
                } catch (error) {
                    logger.error(`tripPingsStore(${instanceId}): loadTripPings: error`, error as Error);
                    set((state: TripPingsState) => ({
                        ...state,
                        isLoading: false,
                        tripPings: [],
                        pagination: DEFAULT_PAGINATION,
                    }));
                }
            },
            selectTripPings: (tripPings: TripPing[]) => {
                set({ selectedTripPings: tripPings });
            },
            clearSelectedTripPings: () => {
                set({ selectedTripPings: [] });
            },
            selectAllTripPings: () => {
                set({ selectedTripPings: get().tripPings });
            },
            clearTripPings: () => {
                set(() => ({
                    ...DEFAULT_STATE,
                }));
                logger.info(`tripPingsStore(${instanceId}): clearTripPings`);
            },

            listenToLocationUpdates: async (tripId: number) => {
                if (appConfig.get('disableAlertHub')) return; // TODO:: this env key is not correct, there are some miss here need to be cleaned and fixed
                if (get().isConnectingToHub) return;
                set(() => ({ isConnectingToHub: true }));
                try {
                    await tripHub.connect();
                    logger.info(`${instanceId}: listenToLocationUpdates: connected to hub for trip ${tripId}`);

                    await tripHub.joinTripLocationUpdateGroup(tripId);
                    logger.info(
                        `${instanceId}: listenToLocationUpdates: joined location update group for trip ${tripId}`,
                    );

                    if (subscriptionKeys['ReceiveTripLocationUpdate']) {
                        get().unListenToLocationUpdates();
                    }

                    const subscriptionKey = tripHub.subscribe(
                        'ReceiveTripLocationUpdate',
                        (locationUpdate: LocationUpdateMessage) => {
                            logger.info(
                                `${instanceId}: listenToLocationUpdates: location update received for trip ${tripId}`,
                                {
                                    locationUpdate,
                                },
                            );

                            _addLocationUpdate(locationUpdate.payload);
                        },
                    );
                    logger.info(
                        `${instanceId}: listenToLocationUpdates: subscribed to location updates for trip ${tripId}`,
                        {
                            subscriptionKey,
                        },
                    );
                    subscriptionKeys['ReceiveTripLocationUpdate'] = subscriptionKey;
                } catch (error) {
                    logger.error(
                        `${instanceId}: listenToLocationUpdates: failed to connect for trip ${tripId}`,
                        error as Error,
                    );
                } finally {
                    set(() => ({ isConnectingToHub: false }));
                }
            },

            unListenToLocationUpdates: () => {
                if (!subscriptionKeys['ReceiveTripLocationUpdate']) return;

                tripHub.unsubscribe('ReceiveTripLocationUpdate', subscriptionKeys['ReceiveTripLocationUpdate']);
                delete subscriptionKeys['ReceiveTripLocationUpdate'];
                logger.info(`${instanceId}: unListenToLocationUpdates: location update unListened`, {});
            },

            reset: () => {
                // Clean up SignalR subscriptions
                get().unListenToLocationUpdates();
                set(DEFAULT_STATE);
                logger.info(`${instanceId}: reset to default state`);
            },
        };
    };
};

// ----------------------------------------------------------
// ---------------------- export store ----------------------
// ----------------------------------------------------------
export const useTripPingsStore = create<TripPingsStoreType>()(
    subscribeWithSelector(devtools(_tripPingsStore('main'), { name: 'trip-pings-store' })),
);
