import { create, type StateCreator } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';

import { DEFAULT_PAGINATION, type Pagination } from '@/infrastructure/validation/schemas/pagination.schema';
import { logger } from '@/infrastructure/logging';
import type { TripsItem, TripsRequest } from '@/infrastructure/api/trips/types';
import { getMapPointIcon } from '@/shared/utils/map.utils';
import { tripsService } from '@/infrastructure/api/trips/trips.service';

// ---------------------------------------------------
// ---------------------- types ----------------------
// ---------------------------------------------------
export type TripsStoreType = TripsState & TripsActions;

// ---------------------------------------------------
// ---------------------- state ----------------------
// ---------------------------------------------------
interface TripsState {
    trips: TripsItem[];
    pagination: Pagination;
    isLoading: boolean;
}

// ----------------------------------------------------------
// ---------------------- default state ---------------------
// ----------------------------------------------------------
const DEFAULT_STATE: TripsState = {
    trips: [],
    pagination: DEFAULT_PAGINATION,
    isLoading: false,
};

// -----------------------------------------------------
// ---------------------- actions ----------------------
// -----------------------------------------------------
interface TripsActions {
    loadTrips: (params?: Partial<TripsRequest>) => Promise<void>;
    loadMoreTrips: (params?: Partial<TripsRequest>) => Promise<void>;
}

// ---------------------------------------------------
// ---------------------- store ----------------------
// ---------------------------------------------------
const _TripsStore = (instanceId: string): StateCreator<TripsStoreType> => {
    return (set, get): TripsStoreType => ({
        ...DEFAULT_STATE,

        async loadTrips(params: Partial<TripsRequest> = { pageNumber: 1, pageSize: 1000 }) {
            // Prevent duplicate loads
            if (get().isLoading) return;

            set({ isLoading: true });
            try {
                const response = await tripsService.getTrips({
                    ...params,
                } as TripsRequest);

                set(() => ({
                    trips: response.data,
                    pagination: response.pagination,
                    isLoading: false,
                }));

                logger.info(`${instanceId}: loadTrips updating state`, { response });
            } catch (error: unknown) {
                logger.error(`${instanceId}: loadTrips error`, error as Error);
                set({ isLoading: false });
            }
        },

        async loadMoreTrips(options: Partial<TripsRequest> = {}) {
            // Prevent duplicate loads
            if (get().isLoading) return;

            const currentPagination = get().pagination;

            // If we know we've fetched all pages, skip
            if (currentPagination.totalCount > 0 && currentPagination.currentPage >= currentPagination.totalPages) {
                return;
            }

            set({ isLoading: true });
            try {
                // default next page if not provided
                options.pageNumber = options.pageNumber ?? currentPagination.currentPage + 1;

                const response = await tripsService.getTrips({
                    ...options,
                } as TripsRequest);
                logger.info(`${instanceId}: loadMoreTrips updating state`, { response });

                set((state: TripsState) => ({
                    trips: response.pagination.currentPage > 1 ? [...state.trips, ...response.data] : response.data,
                    pagination: response.pagination,
                    isLoading: false,
                }));
            } catch (error: unknown) {
                logger.error(`${instanceId}: loadMoreTrips error`, error as Error);
                set({ isLoading: false });
            }
        },
    });
};

// ----------------------------------------------------------
// ---------------------- export store ----------------------
// ----------------------------------------------------------
export const useTripsStore = create<TripsStoreType>()(
    subscribeWithSelector(devtools(_TripsStore('main'), { name: 'Trips-store' })),
);

// ------------------------------------------------------------
// ---------------------- export lookups ----------------------
// ------------------------------------------------------------
export const useTripsLookups = () =>
    useTripsStore((state) => state.trips).map((item) => ({
        ...item,
        icon: getMapPointIcon('truck', 'trip'),
    }));
