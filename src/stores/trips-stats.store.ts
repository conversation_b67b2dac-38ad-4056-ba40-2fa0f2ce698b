import { create, type StateCreator } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';

import { logger } from '@/infrastructure/logging';
import type { TripsStatsResponse } from '@/infrastructure/api/trips/types';
import { tripsService } from '@/infrastructure/api/trips/trips.service';

export type TripsStatsStoreType = TripsStatsState & TripsStatsActions;

interface TripsStatsState {
    tripsStats: TripsStatsResponse | null;
    isLoading: boolean;
}

const DEFAULT_STATE: TripsStatsState = {
    tripsStats: null,
    isLoading: false,
};

interface TripsStatsActions {
    loadTripsStats: () => Promise<void>;
}

const _tripsStatsStore = (instanceId: string): StateCreator<TripsStatsStoreType> => {
    return (set): TripsStatsStoreType => ({
        ...DEFAULT_STATE,

        loadTripsStats: async () => {
            set((state: TripsStatsState) => ({ ...state, isLoading: true }));
            try {
                const res = await tripsService.getTripsStats();

                set((state: TripsStatsState) => ({
                    ...state,
                    tripsStats: res,
                    isLoading: false,
                }));

                logger.info(`tripsStatsStore(${instanceId}): loadTripsStats: status update to `, res ?? {});
            } catch (error) {
                logger.error(`tripsStatsStore(${instanceId}): loadTripsStats: error: `, error as Error);
                set((state: TripsStatsState) => ({ ...state, isLoading: false }));
            }
        },
    });
};

export const useTripsStatsStore = create<TripsStatsStoreType>()(
    subscribeWithSelector(devtools(_tripsStatsStore('main'), { name: 'trips-stats-store' })),
);
