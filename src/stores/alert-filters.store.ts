import { create, type StateCreator } from 'zustand';
import { devtools, persist, subscribeWithSelector } from 'zustand/middleware';

import { LocalStorageKeys } from '@/infrastructure/local-storage/local-storage-keys.constants';
import { logger } from '@/infrastructure/logging';
import type { AlertsQueryParams } from '@/infrastructure/api/alerts/types';
import { AlertAcknowledgement } from '@/shared/enums';

// ---------------------------------------------------
// ---------------------- types ----------------------
// ---------------------------------------------------

/** Complete filter shape: AlertsQueryParams + extra UI filter state */
export type AlertFiltersShape = AlertsQueryParams & {
    alertTypes: number[]; // Multi-select for alert types
    activeAlertsOnly: boolean;
    alertAcknowledgement: AlertAcknowledgement;
};

/** Sections that share the list toggle (ID + boolean) shape */
export type AlertListSection = 'alertTypes';

/** Final store type */
export type AlertFiltersStoreType = AlertFiltersState & AlertFiltersActions;

// ---------------------------------------------------
// ---------------------- state ----------------------
// ---------------------------------------------------

/** Store state */
export interface AlertFiltersState {
    filters: AlertFiltersShape;
    appliedFiltersCount: number;
}

// -----------------------------------------------------
// ---------------------- actions ----------------------
// -----------------------------------------------------

/** Store actions */
export interface AlertFiltersActions {
    setFilters: (filters: AlertFiltersShape) => void;
    updateListSection: (section: AlertListSection, id: number, value: boolean) => void;
    setFilter: <K extends keyof AlertFiltersShape>(key: K, value: AlertFiltersShape[K]) => void;
    resetFilters: () => void;
    applyFilters: (callback?: (filters: AlertFiltersShape) => void) => void;
}

// ----------------------------------------------------------
// ---------------------- default state ---------------------
// ----------------------------------------------------------

/** Default filter values */
export const createDefaultAlertFilters = (): AlertFiltersShape => ({
    // AlertsQueryParams defaults
    tripId: null,
    alertTypeId: null,
    isAcknowledged: null,
    fromServerDate: null,
    toServerDate: null,
    fromTrackerDate: null,
    toTrackerDate: null,
    pageNumber: 1,
    pageSize: 20,
    // Extra filter state
    alertTypes: [],
    activeAlertsOnly: true,
    alertAcknowledgement: AlertAcknowledgement.ALL,
});

// ---------------------------------------------------
// ---------------------- helpers ----------------------
// ---------------------------------------------------

/** Helper: update list by ID toggle */
const getUpdatedList = (list: number[], id: number, checked: boolean) => {
    if (checked) {
        return list.includes(id) ? list : [...list, id];
    }
    return list.filter((x) => x !== id);
};

/**
 * Count the number of applied filters by comparing `filters` with `defaults`.
 * - Excludes pagination keys (pageSize, pageNumber).
 * - If useFilters === false, returns 0 (no filters applied).
 */
const getAppliedFiltersCount = (
    filters: AlertFiltersShape,
    defaults: AlertFiltersShape,
    excludeKeys: (keyof AlertFiltersShape)[] = ['pageSize', 'pageNumber'],
): number => {
    let count = 0;

    for (const key in filters) {
        const k = key as keyof AlertFiltersShape;
        if (excludeKeys.includes(k)) continue;

        const value = filters[k];
        const def = defaults[k];

        // Arrays: applied if length differs (default arrays are empty)
        if (Array.isArray(value) && Array.isArray(def)) {
            if (value.length !== def.length) count++;
            continue;
        }

        // For primitives / nullables: applied if not strictly equal to default
        if (value !== def) count++;
    }

    return count;
};

// ---------------------------------------------------
// ---------------------- store ----------------------
// ---------------------------------------------------

/** Store factory */
const createAlertFiltersState =
    (instanceId = 'alert-filters'): StateCreator<AlertFiltersStoreType> =>
    (set, get) =>
        ({
            filters: createDefaultAlertFilters(),
            appliedFiltersCount: 0,

            setFilters: (filters) => {
                set({ filters, appliedFiltersCount: getAppliedFiltersCount(filters, createDefaultAlertFilters()) });
                logger.info('setFilters', { instanceId, filters });
            },
            updateListSection: (section, id, checked) => {
                set((currentState: AlertFiltersState) => {
                    const newFilters = {
                        ...currentState.filters,
                        [section]: getUpdatedList(currentState.filters[section], id, checked),
                    };
                    return {
                        filters: newFilters,
                        appliedFiltersCount: getAppliedFiltersCount(newFilters, createDefaultAlertFilters()),
                    };
                });
                logger.info('updateListSection', { instanceId, section, id, checked });
            },
            setFilter: (key, value) => {
                set((currentState: AlertFiltersState) => {
                    const newFilters = { ...currentState.filters, [key]: value };
                    return {
                        filters: newFilters,
                        appliedFiltersCount: getAppliedFiltersCount(newFilters, createDefaultAlertFilters()),
                    };
                });
                logger.info('setFilter', { instanceId, key, value });
            },
            resetFilters: () => {
                const defaults = createDefaultAlertFilters();
                set({ filters: defaults, appliedFiltersCount: 0 });
            },
            applyFilters: (callback) => callback?.(get().filters),
        }) as AlertFiltersStoreType;

// ----------------------------------------------------------
// ---------------------- export store ----------------------
// ----------------------------------------------------------

/** Export hook */
export const useAlertFiltersStore = create<AlertFiltersStoreType>()(
    subscribeWithSelector(
        devtools(persist(createAlertFiltersState(), { name: LocalStorageKeys.ALERT_FILTERS }), {
            name: 'alert-filters-store',
        }),
    ),
);
