import { create, type StateCreator } from 'zustand';
import { devtools, persist, subscribeWithSelector } from 'zustand/middleware';

import { LocalStorageKeys } from '@/infrastructure/local-storage/local-storage-keys.constants';
import { logger } from '@/infrastructure/logging';
import type { TripLocationRequest } from '@/infrastructure/api/trips/types';
import { AlertAcknowledgement } from '@/shared/enums';

// ---------------------------------------------------
// ---------------------- types ----------------------
// ---------------------------------------------------

/** Complete filter shape: TripLocationRequest + extra UI filter state */
export type FiltersShape = TripLocationRequest & {
    useFilters: boolean;
};

/** Sections that share the NamedToggle (ID + boolean) shape */
export type ListSection = 'inPorts' | 'outPorts' | 'alertTypes' | 'tripLocations';

/** Final store type */
export type TripFiltersStoreType = TripFiltersState & TripFiltersActions;

// ---------------------------------------------------
// ---------------------- state ----------------------
// ---------------------------------------------------

export interface TripFiltersState {
    /** Committed filters (used by the app / API) */
    filters: FiltersShape;

    /** Draft filters (used by UI while editing) */
    draftFilters: FiltersShape;

    /** Count of applied (draft) filters shown in the UI button */
    appliedFiltersCount: number;
}

// -----------------------------------------------------
// ---------------------- actions ----------------------
// -----------------------------------------------------

export interface TripFiltersActions {
    setFilters: (filters: FiltersShape) => void;
    updateListSection: (section: ListSection, id: number, value: boolean) => void;
    setFilter: <K extends keyof FiltersShape>(key: K, value: FiltersShape[K]) => void;
    setUseFilters: (value: boolean) => void;
    resetFilters: () => void;
    applyFilters: (callback?: (filters: FiltersShape) => void) => void;
}

// ----------------------------------------------------------
// ---------------------- default state ---------------------
// ----------------------------------------------------------

export const createDefaultFilters = (): FiltersShape => ({
    inPorts: [],
    outPorts: [],
    alertTypes: [],
    activeAlertsOnly: false,
    transitNumber: null,
    transitSeqNumber: null,
    driverName: null,
    driverNationality: null,
    driverMobileNo: null,
    driverPassportNumber: null,
    plateNumber: null,
    trackerNumber: null,
    tipCode: null,
    tripCategory: null,
    tripLocations: [],
    activeTripsOnly: null,
    tripStartDate: null,
    tripEndDate: null,
    transDate: null,
    orderBy: null,
    orderDir: null,
    alertAcknowledgement: AlertAcknowledgement.ALL,
    pageSize: 1000,
    pageNumber: 1,
    // Extra filter state
    useFilters: true,
});

// ---------------------------------------------------
// ---------------------- helpers ----------------------
// ---------------------------------------------------

/** Helper: update list by ID toggle */
const getUpdatedList = (list: number[], id: number, checked: boolean) => {
    if (checked) {
        return list.includes(id) ? list : [...list, id];
    }
    return list.filter((x) => x !== id);
};

/**
 * Count the number of applied filters by comparing `filters` with `defaults`.
 * - Excludes pagination/sort keys (pageSize, pageNumber, orderBy, orderDir).
 * - If useFilters === false, returns 0 (no filters applied).
 */
const getAppliedFiltersCount = (
    filters: FiltersShape,
    defaults: FiltersShape,
    excludeKeys: (keyof FiltersShape)[] = ['pageSize', 'pageNumber', 'useFilters'],
): number => {
    let count = 0;

    for (const key in filters) {
        const k = key as keyof FiltersShape;
        if (excludeKeys.includes(k)) continue;

        const value = filters[k];
        const def = defaults[k];

        // Arrays: applied if length differs (default arrays are empty)
        if (Array.isArray(value) && Array.isArray(def)) {
            if (value.length !== def.length) count++;
            continue;
        }

        // For primitives / nullables: applied if not strictly equal to default
        if (value !== def) count++;
    }

    return count;
};

// ---------------------------------------------------
// ---------------------- store ----------------------
// ---------------------------------------------------

const createTripFiltersState =
    (instanceId = 'trip-filters'): StateCreator<TripFiltersStoreType> =>
    (set, get) =>
        ({
            // Committed filters (used by the app/API)
            filters: createDefaultFilters(),

            // Draft filters (used by UI while editing)
            draftFilters: createDefaultFilters(),

            // Count reflects draftFilters so the Apply button shows current draft selection count
            appliedFiltersCount: 0,

            /**
             * Replace committed filters directly (rare). This also updates draftFilters so UI stays in sync.
             */
            setFilters: (filters) => {
                set({
                    filters,
                    draftFilters: filters,
                    appliedFiltersCount: getAppliedFiltersCount(filters, createDefaultFilters()),
                });
                logger.info('setFilters (committed & draft)', { instanceId, filters });
            },

            /**
             * updateListSection now mutates draftFilters only
             */
            updateListSection: (section, id, checked) => {
                set((currentState: TripFiltersState) => {
                    const newDraft = {
                        ...currentState.draftFilters,
                        [section]: getUpdatedList(currentState.draftFilters[section], id, checked),
                    };
                    return {
                        draftFilters: newDraft,
                        appliedFiltersCount: getAppliedFiltersCount(newDraft, createDefaultFilters()),
                    };
                });
                logger.info('updateListSection (draft)', { instanceId, section, id, checked });
            },

            /**
             * setFilter updates draftFilters only (so no API) and recalculates count
             */
            setFilter: (key, value) => {
                set((currentState: TripFiltersState) => {
                    const newDraft = { ...currentState.draftFilters, [key]: value };
                    return {
                        draftFilters: newDraft,
                        appliedFiltersCount: getAppliedFiltersCount(newDraft, createDefaultFilters()),
                    };
                });
                logger.info('setFilter (draft)', { instanceId, key, value });
            },

            /**
             * setUseFilters updates draftFilters.useFilters (UI state)
             */
            setUseFilters: (value) =>
                set((currentState: TripFiltersState) => {
                    const newDraft = {
                        ...currentState.draftFilters,
                        useFilters: value,
                        ...(value ? {} : { tripCategory: null }),
                    };
                    return {
                        draftFilters: newDraft,
                        appliedFiltersCount: getAppliedFiltersCount(newDraft, createDefaultFilters()),
                    };
                }),

            /**
             * reset draft filters (UI). Doesn’t commit unless applyFilters() is called.
             */
            resetFilters: () => {
                const defaults = createDefaultFilters();
                set({ draftFilters: defaults, appliedFiltersCount: 0 });
                logger.info('resetFilters (draft)', { instanceId });
            },

            /**
             * applyFilters: commit draftFilters -> filters and run callback.
             * This is the place to call API (from a component or here inside the callback).
             */
            applyFilters: (callback) => {
                const toCommit = get().draftFilters;
                set({
                    filters: toCommit,
                    // Keep appliedFiltersCount showing current draft count (optional)
                    appliedFiltersCount: getAppliedFiltersCount(toCommit, createDefaultFilters()),
                });
                logger.info('applyFilters (committed)', { instanceId, filters: toCommit });
                callback?.(toCommit);
            },
        }) as TripFiltersStoreType;

// ----------------------------------------------------------
// ---------------------- export store ----------------------
// ----------------------------------------------------------

export const useTripFiltersStore = create<TripFiltersStoreType>()(
    subscribeWithSelector(
        devtools(persist(createTripFiltersState(), { name: LocalStorageKeys.TRIP_FILTERS }), {
            name: 'trip-filters-store',
        }),
    ),
);
