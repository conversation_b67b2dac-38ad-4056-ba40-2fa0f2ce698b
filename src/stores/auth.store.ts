import { create } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';

import { LocalStorage } from '@/infrastructure/local-storage/local-storage';
import { LocalStorageKeys } from '@/infrastructure/local-storage/local-storage-keys.constants';
import { authService } from '@/infrastructure/api/auth/auth.service';
import type { UserProfileResponse } from '@/infrastructure/api/auth/types';
import { logger } from '@/infrastructure/logging';

//---------------- Types ----------------
interface AuthTokens {
    tokenType: string;
    accessToken: string;
    expiresIn: number;
    refreshToken: string;
    expiresAt?: number; // Absolute expiry timestamp (calculated when token is stored)
}

interface AuthState {
    tokens: AuthTokens | null;
    user: UserProfileResponse | null;
    isAuthenticated: boolean;
    isLoading: boolean;
    refreshTimer: NodeJS.Timeout | null;
}

interface AuthActions {
    setAuthData: (tokens: AuthTokens, user: UserProfileResponse) => void;
    setUserProfile: (user: UserProfileResponse) => void;
    setAuthTokens: (tokens: AuthTokens) => void;
    clearAuth: () => void;

    logout: () => void;
    refreshToken: () => Promise<boolean>;
    restoreSession: () => Promise<void>;

    fetchUserProfile: () => Promise<UserProfileResponse | null>;

    _scheduleTokenRefresh: (expiresIn: number) => void;
    _clearRefreshTimer: () => void;
}

export type AuthStoreType = AuthState & AuthActions;

//---------------- Store ----------------
export const useAuthStore = create<AuthStoreType>()(
    subscribeWithSelector(
        devtools(
            (set, get) => ({
                tokens: null,
                user: null,
                isAuthenticated: false,
                isLoading: false,
                refreshTimer: null,

                setAuthData: (tokens: AuthTokens, user: UserProfileResponse) => {
                    get().setAuthTokens(tokens);
                    get().setUserProfile(user);
                },

                setUserProfile: (user: UserProfileResponse) => {
                    set({
                        user,
                        isAuthenticated: true,
                    });
                },

                setAuthTokens: (tokens: AuthTokens) => {
                    const now = Math.floor(Date.now() / 1000);
                    const tokensWithExpiry = {
                        ...tokens,
                        expiresAt: now + tokens.expiresIn,
                    };

                    LocalStorage.set(LocalStorageKeys.AUTH_TOKEN, tokensWithExpiry);
                    get()._scheduleTokenRefresh(tokens.expiresIn);
                },

                clearAuth: () => {
                    LocalStorage.remove(LocalStorageKeys.AUTH_TOKEN);

                    get()._clearRefreshTimer();

                    set({
                        tokens: null,
                        user: null,
                        isAuthenticated: false,
                        isLoading: false,
                    });
                },

                logout: () => {
                    get().clearAuth();
                },

                refreshToken: async (): Promise<boolean> => {
                    const { tokens } = get();
                    if (!tokens?.refreshToken) {
                        return false;
                    }

                    try {
                        set({ isLoading: true });

                        const newTokens = await authService.refreshToken({
                            refreshToken: tokens.refreshToken,
                        });

                        const user = await get().fetchUserProfile();
                        if (!user) {
                            throw new Error('Failed to fetch user profile after refresh');
                        }

                        get().setAuthData(newTokens, user);

                        return true;
                    } catch (error) {
                        logger.error('[AuthStore] Token refresh failed:', error as Error);
                        get().clearAuth();
                        set({ isLoading: false });
                        return false;
                    }
                },

                restoreSession: async () => {
                    try {
                        set({ isLoading: true });

                        const storedTokens = LocalStorage.get(LocalStorageKeys.AUTH_TOKEN) as AuthTokens | null;

                        if (!storedTokens) {
                            set({ isLoading: false });
                            return;
                        }

                        const now = Math.floor(Date.now() / 1000);
                        const tokenExpiry = storedTokens.expiresAt || now + storedTokens.expiresIn;

                        if (now >= tokenExpiry) {
                            const refreshSuccess = await get().refreshToken();
                            if (!refreshSuccess) {
                                get().clearAuth();
                            }
                            set({ isLoading: false });
                        } else {
                            const user = await get().fetchUserProfile();
                            if (user) {
                                get().setAuthData(storedTokens, user);
                            } else {
                                get().clearAuth();
                            }
                            set({ isLoading: false });
                        }
                    } catch (error) {
                        logger.error('[AuthStore] Session restoration failed:', error as Error);
                        get().clearAuth();
                    }
                },

                fetchUserProfile: async (): Promise<UserProfileResponse | null> => {
                    try {
                        const user = await authService.getUserProfile();
                        set({ user });
                        return user;
                    } catch (error) {
                        logger.error('[AuthStore] Failed to fetch user profile:', error as Error);
                        return null;
                    }
                },

                _scheduleTokenRefresh: (expiresIn: number) => {
                    get()._clearRefreshTimer();

                    // Schedule refresh 5 minutes before expiry
                    const refreshTime = (expiresIn - 300) * 1000;

                    if (refreshTime > 0) {
                        const timer = setTimeout(() => {
                            get().refreshToken();
                        }, refreshTime);

                        set({ refreshTimer: timer });
                    }
                },

                _clearRefreshTimer: () => {
                    const { refreshTimer } = get();
                    if (refreshTimer) {
                        clearTimeout(refreshTimer);
                        set({ refreshTimer: null });
                    }
                },
            }),
            { name: 'auth-store' },
        ),
    ),
);
