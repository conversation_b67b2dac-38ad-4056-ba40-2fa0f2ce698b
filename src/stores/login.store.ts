import { type NavigateFunction } from 'react-router-dom';
import { create } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';
import i18n from 'i18next';

import { loginCredentialsSchema, type LoginCredentials } from '@/infrastructure/validation/schemas/login.schema';
import { valy } from '@/shared/lib/Valy';
import { authService } from '@/infrastructure/api/auth/auth.service';
import { logger } from '@/infrastructure/logging';
import { showSuccess, showError } from '@/components/common/ui/Toast';

import { useAuthStore } from './auth.store';

//---------------- Types ----------------
type LoginCredentialsState = {
    formDetails: LoginCredentials;
};

type Loader = { isDataLoading: boolean };

type LoginActions = {
    setUsername: (username: string) => void;
    setPassword: (password: string) => void;
    submit: (navigate: NavigateFunction) => void;
};

export type LoginStoreType = LoginCredentialsState & Loader & LoginActions;

//---------------- Store ----------------

export const useLoginStore = create<LoginStoreType>()(
    subscribeWithSelector(
        devtools(
            (set) => ({
                formDetails: { username: '', password: '' },
                isDataLoading: false,
                setUsername: (username: string) =>
                    set((state) => ({ formDetails: { ...state.formDetails, username } })),
                setPassword: (password: string) =>
                    set((state) => ({ formDetails: { ...state.formDetails, password } })),
                submit: (navigate: NavigateFunction) => FireSubmit(navigate),
            }),
            { name: 'login-store' },
        ),
    ),
);

//---------------- Factory Functions --------------

const FireSubmit = async (navigate: NavigateFunction) => {
    const loginStore = useLoginStore.getState();

    const validLoginForm = valy.validate<LoginCredentials>(
        loginCredentialsSchema,
        loginStore.formDetails,
        'LoginCredentials',
    );

    if (!validLoginForm.success) return;

    fireLoader(true);

    try {
        const authResponse = await authService.login({
            email: loginStore.formDetails.username,
            password: loginStore.formDetails.password,
        });

        useAuthStore.getState().setAuthTokens(authResponse);

        const userProfile = await authService.getUserProfile();
        useAuthStore.getState().setAuthData(authResponse, userProfile);

        navigate('/monitor-board');
        showSuccess(i18n.t('common.successfulOperation'), i18n.t('login.loginSuccessful'));
    } catch (error: unknown) {
        logger.error('[LoginStore] Login failed:', error as Error);
        showError(i18n.t('common.failedOperation'), i18n.t('login.loginFailed'));
    } finally {
        fireLoader(false);
    }
};

const fireLoader = (isDataLoading: boolean) => {
    useLoginStore.setState({ isDataLoading });
};
