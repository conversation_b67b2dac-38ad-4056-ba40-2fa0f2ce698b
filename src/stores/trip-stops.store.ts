import { create, type StateCreator } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';

import { logger } from '@/infrastructure/logging';
import { tripStopsService } from '@/infrastructure/api/trip-stops/trip-stops.service';
import type { TripStop, GetTripStopsQueryParams } from '@/infrastructure/api/trip-stops/types';
import { DEFAULT_PAGINATION, type Pagination } from '@/infrastructure/validation/schemas/pagination.schema';

// ---------------------------------------------------
// ---------------------- types ----------------------
// ---------------------------------------------------
export type TripStopsStoreType = TripStopsState & TripStopsActions;

// ---------------------------------------------------
// ---------------------- state ----------------------
// ---------------------------------------------------
interface TripStopsState {
    tripStops: TripStop[];
    pagination: Pagination;
    isLoading: boolean;
    currentTripId: number | null;
}

// ----------------------------------------------------------
// ---------------------- default state ---------------------
// ----------------------------------------------------------
const DEFAULT_STATE: TripStopsState = {
    tripStops: [],
    pagination: DEFAULT_PAGINATION,
    isLoading: false,
    currentTripId: null,
};

// -----------------------------------------------------
// ---------------------- actions ----------------------
// -----------------------------------------------------
interface TripStopsActions {
    loadTripStops: (params: GetTripStopsQueryParams) => Promise<void>;
    clearTripStops: () => void;
    reset: () => void;
}

// ---------------------------------------------------
// ---------------------- store ----------------------
// ---------------------------------------------------
const _tripStopsStore = (instanceId: string): StateCreator<TripStopsStoreType> => {
    return (set, get): TripStopsStoreType => ({
        ...DEFAULT_STATE,

        loadTripStops: async (params: GetTripStopsQueryParams) => {
            const { currentTripId, isLoading } = get();

            // Prevent duplicate loads for same trip
            if (isLoading && currentTripId === params.tripId) {
                return;
            }

            set((state: TripStopsState) => ({ ...state, isLoading: true }));

            try {
                const response = await tripStopsService.getTripStops(params);

                set((state: TripStopsState) => ({
                    ...state,
                    tripStops: response.data,
                    pagination: response.pagination,
                    currentTripId: params.tripId,
                    isLoading: false,
                }));

                logger.info(`tripStopsStore(${instanceId}): loadTripStops: status update`, {
                    tripId: params.tripId,
                    count: response.data.length,
                });
            } catch (error) {
                logger.error(`tripStopsStore(${instanceId}): loadTripStops: error`, error as Error);
                set((state: TripStopsState) => ({
                    ...state,
                    isLoading: false,
                    tripStops: [],
                    pagination: DEFAULT_PAGINATION,
                }));
            }
        },

        clearTripStops: () => {
            set(() => ({
                ...DEFAULT_STATE,
            }));
            logger.info(`tripStopsStore(${instanceId}): clearTripStops`);
        },

        reset: () => {
            set(DEFAULT_STATE);
            logger.info(`tripStopsStore(${instanceId}): reset to default state`);
        },
    });
};

// ----------------------------------------------------------
// ---------------------- export store ----------------------
// ----------------------------------------------------------
export const useTripStopsStore = create<TripStopsStoreType>()(
    subscribeWithSelector(devtools(_tripStopsStore('main'), { name: 'trip-stops-store' })),
);

// ------------------------------------------------------------
// ---------------------- export lookups ----------------------
// ------------------------------------------------------------
export const useTripStopsLookups = () =>
    useTripStopsStore((state) => state.tripStops).map((stop) => ({
        id: stop.id,
        address: stop.address,
        period: `${stop.period.hours}h ${stop.period.minutes}m`,
    }));
