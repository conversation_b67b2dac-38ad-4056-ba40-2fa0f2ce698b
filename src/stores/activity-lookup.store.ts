import { create, type StateCreator } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';

import { logger } from '@/infrastructure/logging';
import { activityLookupsService } from '@/infrastructure/api/trip-activities/activity-lookups.service';
import type { ActivityStatus, ActivityAction } from '@/infrastructure/api/trip-activities/types';

type LookupsState = {
    statuses: ActivityStatus[];
    actions: ActivityAction[];
    isLoading: boolean;
    loaded: boolean;
    error?: string | null;
};

type LookupsActions = {
    loadLookups: () => Promise<void>;
    clear: () => void;
};

export type ActivityLookupsStoreType = LookupsState & LookupsActions;

const DEFAULT_STATE: LookupsState = {
    statuses: [],
    actions: [],
    isLoading: false,
    loaded: false,
    error: null,
};

const _lookupsStore: StateCreator<ActivityLookupsStoreType> = (set) => ({
    ...DEFAULT_STATE,

    loadLookups: async () => {
        set((s) => ({ ...s, isLoading: true, error: null }));
        try {
            const [statuses, actions] = await Promise.all([
                activityLookupsService.getStatuses(),
                activityLookupsService.getActions(),
            ]);
            set((s) => ({ ...s, statuses, actions, isLoading: false, loaded: true }));
            logger.info('[ActivityLookupsStore] loaded', { statuses: statuses.length, actions: actions.length });
        } catch (e) {
            logger.error('[ActivityLookupsStore] load error', e as Error);
            set((s) => ({ ...s, isLoading: false, error: 'Failed to load lookups' }));
        }
    },

    clear: () => set(() => ({ ...DEFAULT_STATE })),
});

export const useActivityLookupsStore = create<ActivityLookupsStoreType>()(
    subscribeWithSelector(devtools(_lookupsStore, { name: 'activity-lookups-store' })),
);
