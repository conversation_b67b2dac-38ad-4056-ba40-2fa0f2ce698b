import { create, type StateCreator } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';

import { DEFAULT_PAGINATION, type Pagination } from '@/infrastructure/validation/schemas/pagination.schema';
import { logger } from '@/infrastructure/logging';
import { tripAlertsService } from '@/infrastructure/api/trip-alerts/trip-alerts.service';
import type { TripAlert, TripAlertsQueryParams } from '@/infrastructure/api/trip-alerts/types';
import { getMapPointIcon } from '@/shared/utils/map.utils';
import type { AlertTypeId } from '@/shared/utils/alerts.utils';
import { appConfig } from '@/shared/config/app-settings.config';
import type { GroupName, SubscriptionId } from '@/shared/lib/SignalR';
import { tripHub } from '@/infrastructure/signalr/trip/trip.hub';
import type { AlertMessage, AlertMessagePayload } from '@/infrastructure/signalr/trip/types';
import { HubMessageEventType } from '@/infrastructure/signalr/hub-message/types';

// ---------------------------------------------------
// ---------------------- types ----------------------
// ---------------------------------------------------
export type TripAlertsStoreType = TripAlertsState & TripAlertsActions;
const subscriptionKeys: Record<GroupName, SubscriptionId> = {};
export type Alert = AlertMessagePayload & {
    isAcknowledged: boolean;
};

export type AlertMarkerViewMode = 'single-point' | 'range';
export interface ViewOptions {
    mode?: AlertMarkerViewMode | undefined;
    isOpen?: boolean | undefined;
}

export type AlertWithViewOptions = TripAlert & {
    viewOptions: ViewOptions;
};

// ---------------------- helper types ----------------------
type CurrentUser = {
    id: string;
    name: string;
};

// ---------------------------------------------------
// ---------------------- state ----------------------
// ---------------------------------------------------
interface TripAlertsState {
    tripAlerts: TripAlert[];
    tripAlertsMapperByLocation: Map<string, AlertWithViewOptions[]>;
    selectedTripAlerts: Set<AlertWithViewOptions>;
    pagination: Pagination;
    isLoading: boolean;
    isConnectingToHub: boolean;
    highlightedTripAlertId: string | null;
    ackLoadingIds: string[]; // IDs under acknowledgment
}

// ----------------------------------------------------------
// ---------------------- default state ---------------------
// ----------------------------------------------------------
const DEFAULT_STATE: TripAlertsState = {
    tripAlerts: [],
    tripAlertsMapperByLocation: new Map([]),
    selectedTripAlerts: new Set(),
    pagination: DEFAULT_PAGINATION,
    isLoading: false,
    isConnectingToHub: false,
    highlightedTripAlertId: null,
    ackLoadingIds: [],
};
export const DEFAULT_VIEW_OPTIONS: ViewOptions = { mode: 'single-point', isOpen: false };

// -----------------------------------------------------
// ---------------------- actions ----------------------
// -----------------------------------------------------
interface TripAlertsActions {
    loadTripAlerts: (params?: Partial<TripAlertsQueryParams>) => Promise<void>;
    loadMoreTripAlerts: (params?: Partial<TripAlertsQueryParams>) => Promise<void>;
    setHighlightedTripAlert: (tripAlertId: string | null) => void;
    acknowledgeTripAlert: (tripId: string, alertId: string | number, currentUser?: CurrentUser) => Promise<void>;
    acknowledgeAllTripAlerts: (tripId: string, currentUser?: CurrentUser) => Promise<void>;
    selectTripAlerts: (tripAlerts: TripAlert[]) => void;
    clearSelectedTripAlerts: () => void;
    selectAllTripAlerts: () => void;
    selectTripAlertsByType: (typeId: AlertTypeId, options?: ViewOptions) => void;
    selectTripAlertById: (alertId: number, options?: ViewOptions) => TripAlert;
    clearSelectedTripAlertsByType: (typeId: AlertTypeId) => void;
    deselectTripAlertById: (alertId: number) => void; // Remove specific alert from selection
    listenToAlerts: (tripId: number) => void;
    unListenToAlerts: () => void;
    reset: () => void;
}

// Default values for missing fields when mapping from AlertMessage to TripAlert
const DEFAULT_ALERT_ORIGIN = 1; // SYSTEM origin
const DEFAULT_STATE_ID = 0;
const DEFAULT_LOCATION = { lat: 0, long: 0 };

/**
 * Maps an Alert (AlertMessage + isAcknowledged) to TripAlert format
 * Handles the structural differences between SignalR messages and API responses
 */
const tripAlertMapper = (alert: Alert): TripAlert => ({
    id: alert.alertId,
    alertType: {
        id: alert.alertType.id,
        name: {
            english: alert.alertType.name.english,
            arabic: alert.alertType.name.arabic,
        },
        origin: DEFAULT_ALERT_ORIGIN,
    },
    fromState: {
        stateId: DEFAULT_STATE_ID,
        trackerDateTime: '2025-10-09T04:30:23.9418464+00:00',
        locationSource: 0,
        batteryLevelPercentage: 0,
        chargerStatus: 0,
        gpsSignalStrength: 0,
        gsmSignalStrength: 0,
        currentSpeed: 0,
        routeZone: 0,
        isWithinRouteGeofence: false,
        timeElapsedSinceTripStartInMinutes: 0,
        remainingDistanceInMeters: 0,
        completeDistanceInMeters: 0,
        isWithinSuspiciousZone: false,
        createdAt: '2025-10-09T04:30:23.9418464+00:00',
        ...DEFAULT_LOCATION,
        address: {
            arabic: '',
            english: '',
        },
    },
    toState: null,
    acknowledgedAt: alert.isAcknowledged ? new Date() : null,
    acknowledgedBy: null,
});

//--- Group alerts by the stringified lat,lng key
const alertsMapperByLocation = (data: TripAlert[]): Map<string, AlertWithViewOptions[]> => {
    const alertsMapperByLocation = new Map<string, AlertWithViewOptions[]>();
    data.forEach((alert: TripAlert) => {
        const mapperKey = `${alert.fromState.lat},${alert.fromState.long}`; //--- Note that no space before or after the ',' => (lat,lng)
        const values: AlertWithViewOptions[] = alertsMapperByLocation.get(mapperKey) ?? [];
        values.push({ ...alert, viewOptions: DEFAULT_VIEW_OPTIONS });
        alertsMapperByLocation.set(mapperKey, values);
    });

    return alertsMapperByLocation;
};

// ---------------------------------------------------
// ---------------------- store ----------------------
// ---------------------------------------------------
const _tripAlertsStore = (instanceId: string): StateCreator<TripAlertsStoreType> => {
    return (set, get): TripAlertsStoreType => {
        const _addAlert = (alert: Alert) => {
            set((state: TripAlertsState) => ({
                ...state,
                tripAlerts: [tripAlertMapper(alert), ...state.tripAlerts],
            }));
            // get().loadTripAlerts({ tripId: alert.tripId.toString(), PageNumber: 1, PageSize: 1000 });
            logger.info(`${instanceId}: addAlert: alert added`, { alert });
        };

        return {
            ...DEFAULT_STATE,

            async loadTripAlerts(params: Partial<TripAlertsQueryParams> = {}): Promise<void> {
                // Prevent duplicate loads
                if (get().isLoading) return;

                set({ isLoading: true });
                try {
                    const response = await tripAlertsService.getTripAlerts(params);
                    logger.info(`${instanceId}: loadTripAlerts updating state`, { response });

                    //--- Set alerts mapper by the location (stringified lat,lng key)
                    set({ tripAlertsMapperByLocation: alertsMapperByLocation(response.data) });

                    set(() => ({
                        tripAlerts: response.data,
                        pagination: response.pagination,
                        isLoading: false,
                    }));
                } catch (error: unknown) {
                    logger.error(`${instanceId}: loadTripAlerts error`, error as Error);
                    set({ isLoading: false });
                }
            },

            async loadMoreTripAlerts(params: Partial<TripAlertsQueryParams> = {}) {
                // Prevent duplicate loads
                if (get().isLoading) return;
                set({ isLoading: true });
                const currentPagination = get().pagination;

                // If we know we've fetched all pages, skip
                if (currentPagination.totalCount > 0 && currentPagination.currentPage >= currentPagination.totalPages) {
                    return;
                }

                try {
                    // default next page if not provided
                    params.PageNumber = params.PageNumber ?? currentPagination.currentPage + 1;

                    const response = await tripAlertsService.getTripAlerts(params);
                    logger.info(`${instanceId}: loadMoreTripAlerts updating state`, { response });

                    set((state: TripAlertsState) => ({
                        tripAlerts:
                            response.pagination.currentPage > 1
                                ? [...state.tripAlerts, ...response.data]
                                : response.data,
                        pagination: response.pagination,
                        isLoading: false,
                    }));
                } catch (error: unknown) {
                    logger.error(`${instanceId}: loadMoreTripAlerts error`, error as Error);
                    set({ isLoading: false });
                }
            },

            setHighlightedTripAlert: (tripAlertId) => {
                set({ highlightedTripAlertId: tripAlertId });
                logger.info(`${instanceId}: setHighlightedTripAlert updated`, { tripAlertId });
            },

            async acknowledgeTripAlert(
                tripId: string,
                alertId: string | number,
                currentUser?: CurrentUser,
            ): Promise<void> {
                const alertKey = String(alertId);
                try {
                    set((state) => ({ ackLoadingIds: [...state.ackLoadingIds, alertKey] }));

                    logger.info(`${instanceId}: acknowledging alert ${alertKey} for trip ${tripId}`);

                    await tripAlertsService.acknowledgeTripAlert(tripId, alertKey);

                    const acknowledgmentTimestamp = new Date();
                    const acknowledgmentBy = currentUser ?? null;

                    set((state: TripAlertsState) => ({
                        tripAlerts: state.tripAlerts.map((a) =>
                            String(a.id) === alertKey
                                ? { ...a, acknowledgedAt: acknowledgmentTimestamp, acknowledgedBy: acknowledgmentBy }
                                : a,
                        ),
                    }));

                    logger.info(`${instanceId}: acknowledgeTripAlert updated`, { alertId: alertKey });
                } catch (error: unknown) {
                    logger.error(`${instanceId}: acknowledgeTripAlert error`, error as Error);
                    throw error;
                } finally {
                    set((state) => ({
                        ackLoadingIds: state.ackLoadingIds.filter((id) => id !== alertKey),
                    }));
                }
            },

            async acknowledgeAllTripAlerts(tripId: string, currentUser?: CurrentUser): Promise<void> {
                const user = currentUser ?? null;
                // Collect all IDs that are not acknowledged
                const alertKeys: string[] = get()
                    .tripAlerts.filter((a) => a.acknowledgedAt === null)
                    .map((a) => String(a.id));

                // If there are no alerts, still call API for idempotency, but return early after API.
                if (alertKeys.length === 0) {
                    try {
                        await tripAlertsService.acknowledgeAllTripAlerts(tripId);
                        logger.info(
                            `${instanceId}: acknowledgeAllTripAlerts - no local alerts, API called successfully for trip ${tripId}`,
                        );
                        return;
                    } catch (error: unknown) {
                        logger.error(
                            `${instanceId}: acknowledgeAllTripAlerts error when no alerts present`,
                            error as Error,
                        );
                        throw error;
                    }
                }

                try {
                    // Mark all those IDs as loading
                    set((state) => ({
                        ackLoadingIds: Array.from(new Set([...state.ackLoadingIds, ...alertKeys])),
                    }));

                    logger.info(`${instanceId}: acknowledging ALL alerts for trip ${tripId}`, {
                        alertCount: alertKeys.length,
                    });

                    // Call backend service
                    await tripAlertsService.acknowledgeAllTripAlerts(tripId);

                    const acknowledgmentTimestamp = new Date();

                    // Update all alerts that are not acknowledged locally: set acknowledgedAt and acknowledgedBy
                    set((state: TripAlertsState) => ({
                        tripAlerts: state.tripAlerts.map((a) =>
                            alertKeys.includes(String(a.id))
                                ? {
                                      ...a,
                                      acknowledgedAt: acknowledgmentTimestamp,
                                      acknowledgedBy: user ?? a.acknowledgedBy ?? null,
                                  }
                                : a,
                        ),
                    }));

                    logger.info(`${instanceId}: acknowledgeAllTripAlerts updated locally for trip ${tripId}`, {
                        acknowledgedCount: alertKeys.length,
                    });
                } catch (error: unknown) {
                    logger.error(`${instanceId}: acknowledgeAllTripAlerts error`, error as Error);
                    throw error;
                } finally {
                    // Always remove these IDs from ackLoadingIds
                    set((state) => ({
                        ackLoadingIds: state.ackLoadingIds.filter((id) => !alertKeys.includes(id)),
                    }));
                }
            },

            selectTripAlerts: (tripAlerts: TripAlert[]) => {
                set({
                    selectedTripAlerts: new Set(tripAlerts.map((a) => ({ ...a, viewOptions: DEFAULT_VIEW_OPTIONS }))),
                });
            },
            clearSelectedTripAlerts: () => {
                set({ selectedTripAlerts: new Set() });
            },
            selectAllTripAlerts: () => {
                set({
                    selectedTripAlerts: new Set(
                        get().tripAlerts.map((a) => ({ ...a, viewOptions: DEFAULT_VIEW_OPTIONS })),
                    ),
                });
            },
            selectTripAlertsByType: (typeId: AlertTypeId, options?: ViewOptions) => {
                set({
                    selectedTripAlerts: new Set([
                        ...get().selectedTripAlerts,
                        ...get()
                            .tripAlerts.filter((a) => a.alertType.id === typeId)
                            .map((a) => ({ ...a, viewOptions: options ?? DEFAULT_VIEW_OPTIONS })),
                    ]),
                });
            },
            selectTripAlertById: (alertId: number, options?: ViewOptions) => {
                const alert = get().tripAlerts.find((a) => a.id === alertId);
                if (alert) {
                    set({ selectedTripAlerts: new Set([{ ...alert, viewOptions: options ?? DEFAULT_VIEW_OPTIONS }]) });
                }
                return alert as TripAlert;
            },
            clearSelectedTripAlertsByType: (typeId: AlertTypeId) => {
                set({
                    selectedTripAlerts: new Set([...get().selectedTripAlerts].filter((a) => a.alertType.id !== typeId)),
                });
            },
            deselectTripAlertById: (alertId: number) => {
                set({
                    selectedTripAlerts: new Set([...get().selectedTripAlerts].filter((a) => a.id !== alertId)),
                });
            },

            listenToAlerts: async (tripId: number) => {
                if (appConfig.get('disableAlertHub')) return;

                if (get().isConnectingToHub) return;
                set(() => ({ isConnectingToHub: true }));

                try {
                    await tripHub.connect();
                    logger.info(`${instanceId}: listenToAlerts: connected to hub for trip ${tripId}`);

                    await tripHub.joinTripAlertsGroup(tripId);
                    logger.info(`${instanceId}: listenToAlerts: joined alerts group for trip ${tripId}`);

                    if (subscriptionKeys['ReceiveTripAlert']) {
                        get().unListenToAlerts();
                    }

                    const subscriptionKey = tripHub.subscribe('ReceiveTripAlert', (alert: AlertMessage) => {
                        logger.info(`${instanceId}: listenToAlerts: alert received for trip ${tripId}`, { alert });
                        if (alert.eventType == HubMessageEventType.NewAlert) {
                            _addAlert({ ...alert.payload, isAcknowledged: false });
                        } else if (alert.eventType == HubMessageEventType.AlertAcknowledged) {
                            set((state: TripAlertsState) => ({
                                ...state,
                                tripAlerts: state.tripAlerts.map((a) =>
                                    a.id === alert.payload.alertId ? { ...a, isAcknowledged: true } : a,
                                ),
                            }));
                        }
                    });
                    logger.info(`${instanceId}: listenToAlerts: subscribed to alerts for trip ${tripId}`, {
                        subscriptionKey,
                    });
                    subscriptionKeys['ReceiveTripAlert'] = subscriptionKey;
                } catch (error) {
                    logger.error(`${instanceId}: listenToAlerts: failed to connect for trip ${tripId}`, error as Error);
                } finally {
                    set(() => ({ isConnectingToHub: false }));
                }
            },

            unListenToAlerts: () => {
                if (!subscriptionKeys['ReceiveTripAlert']) return;

                tripHub.unsubscribe('ReceiveTripAlert', subscriptionKeys['ReceiveTripAlert']);
                logger.info(`${instanceId}: unListenToAlerts: alert unListened`, {});
            },

            reset: () => {
                // Clean up SignalR subscriptions
                get().unListenToAlerts();
                set(DEFAULT_STATE);
                logger.info(`${instanceId}: reset to default state`);
            },
        };
    };
};

// ----------------------------------------------------------
// ---------------------- export store ----------------------
// ----------------------------------------------------------
export const useTripAlertsStore = create<TripAlertsStoreType>()(
    subscribeWithSelector(devtools(_tripAlertsStore('main'), { name: 'trip-alerts-store' })),
);

// ------------------------------------------------------------
// ---------------------- export lookups ----------------------
// ------------------------------------------------------------
export const useTripAlertsLookups = () =>
    useTripAlertsStore((state) => state.tripAlerts).map((item) => ({
        ...item,
        icon: getMapPointIcon('alert', 'trip'),
    }));
