// features/activity-logs/activity-logs.store.ts
import { create, type StateCreator } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';

import { DEFAULT_PAGINATION, type Pagination } from '@/infrastructure/validation/schemas/pagination.schema';
import { logger } from '@/infrastructure/logging';
import { activityLogsService } from '@/infrastructure/api/activities-logs/activities-logs.services';
import type { ActivityLogItem, GetActivityLogsQueryParams } from '@/infrastructure/api/activities-logs/types';

// ---------------------------------------------------
// ---------------------- types ----------------------
// ---------------------------------------------------
export type ActivityLogsStoreType = ActivityLogsState & ActivityLogsActions;

// ---------------------------------------------------
// ---------------------- state ----------------------
// ---------------------------------------------------
interface ActivityLogsState {
    logs: ActivityLogItem[];
    pagination: Pagination;
    isLoading: boolean;
}

// ----------------------------------------------------------
// ---------------------- default state ---------------------
// ----------------------------------------------------------
const DEFAULT_STATE: ActivityLogsState = {
    logs: [],
    pagination: DEFAULT_PAGINATION,
    isLoading: false,
};

// -----------------------------------------------------
// ---------------------- actions ----------------------
// -----------------------------------------------------
interface ActivityLogsActions {
    loadActivityLogs: (params?: Partial<GetActivityLogsQueryParams>) => Promise<void>;
    loadMoreActivityLogs: (params?: Partial<GetActivityLogsQueryParams>) => Promise<void>;
}

// ---------------------------------------------------
// ---------------------- store ----------------------
// ---------------------------------------------------
const _ActivityLogsStore = (instanceId: string): StateCreator<ActivityLogsStoreType> => {
    return (set, get): ActivityLogsStoreType => ({
        ...DEFAULT_STATE,

        /** First page / refresh */
        async loadActivityLogs(params: Partial<GetActivityLogsQueryParams> = { pageNumber: 1, pageSize: 20 }) {
            // Prevent duplicate loads
            if (get().isLoading) return;

            set({ isLoading: true });
            try {
                const response = await activityLogsService.getActivityLogs({
                    ...params,
                } as GetActivityLogsQueryParams);

                set(() => ({
                    logs: response.data,
                    pagination: response.pagination,
                    isLoading: false,
                }));

                logger.info(`${instanceId}: loadActivityLogs updating state`, { response });
            } catch (error: unknown) {
                logger.error(`${instanceId}: loadActivityLogs error`, error as Error);
                set({ isLoading: false });
            }
        },

        /** Infinite scroll / next page */
        async loadMoreActivityLogs(options: Partial<GetActivityLogsQueryParams> = {}) {
            // Prevent duplicate loads
            if (get().isLoading) return;

            const currentPagination = get().pagination;

            // If we know we've fetched all pages, skip
            if (currentPagination.totalCount > 0 && currentPagination.currentPage >= currentPagination.totalPages) {
                return;
            }

            set({ isLoading: true });
            try {
                // default next page if not provided
                options.pageNumber = options.pageNumber ?? currentPagination.currentPage + 1;

                const response = await activityLogsService.getActivityLogs({
                    ...options,
                } as GetActivityLogsQueryParams);
                logger.info(`${instanceId}: loadMoreActivityLogs updating state`, { response });

                set((state: ActivityLogsState) => ({
                    logs: response.pagination.currentPage > 1 ? [...state.logs, ...response.data] : response.data,
                    pagination: response.pagination,
                    isLoading: false,
                }));
            } catch (error: unknown) {
                logger.error(`${instanceId}: loadMoreActivityLogs error`, error as Error);
                set({ isLoading: false });
            }
        },
    });
};

// ----------------------------------------------------------
// ---------------------- export store ----------------------
// ----------------------------------------------------------
export const useActivityLogsStore = create<ActivityLogsStoreType>()(
    subscribeWithSelector(devtools(_ActivityLogsStore('activity-logs'), { name: 'ActivityLogs-store' })),
);

// ------------------------------------------------------------
// ---------------------- export lookups ----------------------
// ------------------------------------------------------------
/**
 * Lightweight projection you can use in lists.
 * (Keeps the raw object too so the view can drill into before/after when needed.)
 */
export const useActivityLogsList = () =>
    useActivityLogsStore((state) => state.logs).map((item) => ({
        id: item.id,
        actionType: item.actionType,
        userName: item.user?.name ?? '',
        isDeleted: item.isDeleted,
        timestamp: item.timestamp,
        tripId: item.tripId,
        raw: item,
    }));
