import { create, type StateCreator } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';

import { logger } from '@/infrastructure/logging';
import type { GetUsersQueryParams, User } from '@/infrastructure/api/users/types';
import { usersService } from '@/infrastructure/api/users/users.service';
import { DEFAULT_PAGINATION, type Pagination } from '@/infrastructure/validation/schemas/pagination.schema';

// ---------------------------------------------------
// ---------------------- types ----------------------
// ---------------------------------------------------
export type UsersFilters = GetUsersQueryParams;
export type UsersStoreType = UsersState & UsersActions;

// ---------------------------------------------------
// ---------------------- state ----------------------
// ---------------------------------------------------

interface UsersState {
    users: User[];
    isLoading: boolean;
    pagination: Pagination;
}

// ----------------------------------------------------------
// ---------------------- default state ---------------------
// ----------------------------------------------------------

const DEFAULT_STATE: UsersState = {
    users: [],
    isLoading: false,
    pagination: DEFAULT_PAGINATION,
};

// -----------------------------------------------------
// ---------------------- actions ----------------------
// -----------------------------------------------------

interface UsersActions {
    loadUsers: (params?: Partial<UsersFilters>) => Promise<void>;
    loadMoreUsers: (params?: Partial<UsersFilters>) => Promise<void>;
}

// ---------------------------------------------------
// ---------------------- store ----------------------
// ---------------------------------------------------
const _usersStore = (instanceId: string): StateCreator<UsersStoreType> => {
    return (set, get): UsersStoreType => {
        return {
            ...DEFAULT_STATE,
            loadUsers: async (params?: Partial<UsersFilters>) => {
                // Prevent duplicate loads
                if (get().isLoading) return;

                try {
                    set({ isLoading: true });
                    const response = await usersService.getUsers(params);
                    set({ users: response.data, pagination: response.pagination, isLoading: false });
                } catch (error: unknown) {
                    logger.error(`${instanceId}: loadUsers error`, error as Error);
                    set({ isLoading: false });
                }
            },
            loadMoreUsers: async (params?: Partial<UsersFilters>) => {
                if (get().isLoading) return;

                try {
                    set({ isLoading: true });
                    const response = await usersService.getUsers(params);
                    set({
                        users: [...get().users, ...response.data],
                        pagination: response.pagination,
                        isLoading: false,
                    });
                } catch (error: unknown) {
                    logger.error(`${instanceId}: loadMoreUsers error`, error as Error);
                    set({ isLoading: false });
                }
            },
        };
    };
};

// ----------------------------------------------------------
// ---------------------- export store ----------------------
// ----------------------------------------------------------
export const useUsersStore = create<UsersStoreType>()(
    subscribeWithSelector(devtools(_usersStore('main'), { name: 'users-store' })),
);

// ------------------------------------------------------------
// ---------------------- export lookups ----------------------
// ------------------------------------------------------------
