import { create, type StateCreator } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';

import { logger } from '@/infrastructure/logging';
import type { AlertMessage, AlertMessagePayload } from '@/infrastructure/signalr/trips/types';
import { tripsHub } from '@/infrastructure/signalr/trips/trips.hub';
import type { GroupName, SubscriptionId } from '@/shared/lib/SignalR';
import { appConfig } from '@/shared/config/app-settings.config';
import type {
    Alert as ApiAlertType,
    GetAlertStatsSummaryResponse,
    AlertsQueryParams,
} from '@/infrastructure/api/alerts/types';
import { alertsService } from '@/infrastructure/api/alerts/alerts.service';
import { DEFAULT_PAGINATION, type Pagination } from '@/infrastructure/validation/schemas/pagination.schema';
import { HubMessageEventType } from '@/infrastructure/signalr/hub-message/types';

// ---------------------------------------------------
// ---------------------- types ----------------------
// ---------------------------------------------------

export type Alert = AlertMessagePayload & {
    isAcknowledged: boolean;
};

export type ApiAlert = ApiAlertType;

export type AlertStatsSummary = GetAlertStatsSummaryResponse;
export type AlertFilters = AlertsQueryParams;
export type AlertStoreType = AlertState & AlertActions;

// ---------------------------------------------------
// ---------------------- state ----------------------
// ---------------------------------------------------

interface AlertState {
    alerts: Alert[];
    apiAlerts: ApiAlert[]; // TODO:: this should be changed and merged with alerts
    selectedAlert: Alert | null;
    filteredAlerts: Alert[];
    isLoading: boolean;
    pagination: Pagination;
}

// ----------------------------------------------------------
// ---------------------- default state ---------------------
// ----------------------------------------------------------

const DEFAULT_STATE: AlertState = {
    alerts: [],
    apiAlerts: [],
    isLoading: false,
    pagination: DEFAULT_PAGINATION,
    selectedAlert: null,
    filteredAlerts: [],
};

// -----------------------------------------------------
// ---------------------- actions ----------------------
// -----------------------------------------------------

const subscriptionKeys: Record<GroupName, SubscriptionId> = {};

interface AlertActions {
    acknowledgeAlert: (alert: Alert) => void;
    setIsAcknowledged: (alert: Alert) => void;
    setSelectedAlert: (alert: Alert) => void;
    listenToAlerts: () => void;
    unListenToAlerts: () => void;
    filterAlerts: (filter: (alert: Alert) => boolean) => void;

    loadAlerts: (params?: Partial<AlertFilters>) => Promise<void>;
    loadMoreAlerts: (params?: Partial<AlertFilters>) => Promise<void>;
}

// ---------------------------------------------------
// ---------------------- store ----------------------
// ---------------------------------------------------
const _alertStore = (instanceId: string): StateCreator<AlertStoreType> => {
    return (set, get): AlertStoreType => {
        // ------------------------------------------------------
        // ---------------------- private actions ---------------
        // ------------------------------------------------------
        const _addAlert = (alert: Alert) => {
            set((state: AlertState) => {
                const alertExists = state.alerts.some(
                    (existingAlert) => existingAlert.currentState.id === alert.currentState.id,
                );

                if (alertExists) {
                    logger.info(`${instanceId}: addAlert: alert already exists, skipping`, {
                        alertId: alert.currentState.id,
                    });
                    return state;
                }

                return {
                    ...state,
                    alerts: [...state.alerts, alert].slice(-100),
                };
            });
            logger.info(`${instanceId}: addAlert: alert added`, { alert });
        };

        // ------------------------------------------------------

        return {
            ...DEFAULT_STATE,

            acknowledgeAlert: (alert: Alert) => {
                set((state: AlertState) => ({
                    ...state,
                    alerts: state.alerts.filter((a) => a !== alert),
                }));
                logger.info(`${instanceId}: acknowledgeAlert: alert acknowledged`, { alert });
            },
            setIsAcknowledged: (alert: Alert) => {
                set((state: AlertState) => ({
                    ...state,
                    alerts: state.alerts.map((a) => (a.alertId === alert.alertId ? { ...a, isAcknowledged: true } : a)),
                }));
                logger.info(`${instanceId}: setIsAcknowledged: alert marked as acknowledged`, { alert });
            },

            setSelectedAlert: (alert: Alert) => {
                set((state: AlertState) => ({
                    ...state,
                    selectedAlert: alert,
                }));
                logger.info(`${instanceId}: setSelectedAlert: alert updated`, { alert });
            },

            listenToAlerts: async () => {
                if (appConfig.get('disableAlertHub')) return; // TODO:: this env key is not correct, there are some miss here need to be cleaned and fixed
                await tripsHub.connect();
                logger.info(`${instanceId}: listenToAlerts: connecting to hub`);
                tripsHub.joinAllAlertsGroup();
                logger.info(`${instanceId}: listenToAlerts: joined all trips group`);

                if (subscriptionKeys['ReceiveAllAlerts']) {
                    get().unListenToAlerts();
                }

                const subscriptionKey = tripsHub.subscribe('ReceiveAllAlerts', (alert: AlertMessage) => {
                    logger.info(`${instanceId}: listenToAlerts: alert received`, { alert });
                    if (alert.eventType == HubMessageEventType.NewAlert) {
                        _addAlert({ ...alert.payload, isAcknowledged: false });
                    } else if (alert.eventType == HubMessageEventType.AlertAcknowledged) {
                        set((state: AlertState) => ({
                            ...state,
                            alerts: state.alerts.map((a) =>
                                a.currentState.id === alert.payload.currentState.id
                                    ? { ...a, isAcknowledged: true }
                                    : a,
                            ),
                        }));
                    }
                });
                logger.info(`${instanceId}: listenToAlerts: subscribed to all trips group`, { subscriptionKey });
                subscriptionKeys['ReceiveAllAlerts'] = subscriptionKey;
            },

            unListenToAlerts: () => {
                if (!subscriptionKeys['ReceiveAllAlerts']) return;

                tripsHub.unsubscribe('ReceiveAllAlerts', subscriptionKeys['ReceiveAllAlerts']);
                logger.info(`${instanceId}: unListenToAlerts: alert unListened`, {});
            },

            filterAlerts: (filter: (alert: Alert) => boolean) => {
                set((state: AlertState) => ({
                    ...state,
                    filteredAlerts: state.alerts.filter(filter),
                }));
            },
            loadAlerts: async (params?: Partial<AlertFilters>) => {
                // Prevent duplicate loads
                if (get().isLoading) return;

                try {
                    set({ isLoading: true });
                    const response = await alertsService.getAlerts(params);
                    set({ apiAlerts: response.data, pagination: response.pagination, isLoading: false });
                } catch (error: unknown) {
                    logger.error(`${instanceId}: loadAlerts error`, error as Error);
                    set({ isLoading: false });
                }
            },
            loadMoreAlerts: async (params?: Partial<AlertFilters>) => {
                // Prevent duplicate loads
                if (get().isLoading) return;

                try {
                    set({ isLoading: true });
                    const response = await alertsService.getAlerts(params);
                    set({
                        apiAlerts: [...get().apiAlerts, ...response.data],
                        pagination: response.pagination,
                        isLoading: false,
                    });
                } catch (error: unknown) {
                    logger.error(`${instanceId}: loadMoreAlerts error`, error as Error);
                    set({ isLoading: false });
                }
            },
        };
    };
};

// ----------------------------------------------------------
// ---------------------- export store ----------------------
// ----------------------------------------------------------
export const useAlertStore = create<AlertStoreType>()(
    subscribeWithSelector(devtools(_alertStore('main'), { name: 'alert-store' })),
);

// ------------------------------------------------------------
// ---------------------- export lookups ----------------------
// ------------------------------------------------------------
