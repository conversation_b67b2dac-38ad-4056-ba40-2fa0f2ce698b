import { create, type StateCreator } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';

import { logger } from '@/infrastructure/logging';
import type { GetAlertStatsSummaryResponse } from '@/infrastructure/api/alerts/types';
import { alertsService } from '@/infrastructure/api/alerts/alerts.service';

// ---------------------------------------------------
// ---------------------- types ----------------------
// ---------------------------------------------------

export type AlertStatsSummary = GetAlertStatsSummaryResponse;
export type AlertStatsStoreType = AlertStatsState & AlertStatsActions;

// ---------------------------------------------------
// ---------------------- state ----------------------
// ---------------------------------------------------

interface AlertStatsState {
    stats: AlertStatsSummary;
    isLoading: boolean;
}

// ----------------------------------------------------------
// ---------------------- default state ---------------------
// ----------------------------------------------------------

const DEFAULT_STATE: AlertStatsState = {
    stats: {
        totalAcknowledgedAlerts: 0,
        totalUnacknowledgedAlerts: 0,
        totalActiveTrips: 0,
        totalInActiveTrips: 0,
    },
    isLoading: false,
};

// -----------------------------------------------------
// ---------------------- actions ----------------------
// -----------------------------------------------------

interface AlertStatsActions {
    loadAlertStatsSummary: () => Promise<void>;
}

// ---------------------------------------------------
// ---------------------- store ----------------------
// ---------------------------------------------------
const _alertStatsStore = (instanceId: string): StateCreator<AlertStatsStoreType> => {
    return (set, _get): AlertStatsStoreType => {
        return {
            ...DEFAULT_STATE,
            loadAlertStatsSummary: async () => {
                try {
                    set({ isLoading: true });
                    const response = await alertsService.getAlertStatsSummary();
                    set({ stats: response, isLoading: false });
                } catch (error: unknown) {
                    logger.error(`${instanceId}: loadAlertStatsSummary error`, error as Error);
                    set({ isLoading: false });
                }
            },
        };
    };
};

// ----------------------------------------------------------
// ---------------------- export store ----------------------
// ----------------------------------------------------------
export const useAlertStatsStore = create<AlertStatsStoreType>()(
    subscribeWithSelector(devtools(_alertStatsStore('main'), { name: 'alert-stats-store' })),
);

// ------------------------------------------------------------
// ---------------------- export lookups ----------------------
// ------------------------------------------------------------
