import { create, type StateCreator } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';

import { logger } from '@/infrastructure/logging';
import type { UsersStatsResponse } from '@/infrastructure/api/users/types';
import { usersService } from '@/infrastructure/api/users/users.service';

// ---------------------------------------------------
// ---------------------- types ----------------------
// ---------------------------------------------------
export type UsersStats = UsersStatsResponse;
export type UsersStatsStoreType = UsersStatsState & UsersStatsActions;

// ---------------------------------------------------
// ---------------------- state ----------------------
// ---------------------------------------------------

interface UsersStatsState {
    stats: UsersStats;
    isLoading: boolean;
}

// ----------------------------------------------------------
// ---------------------- default state ---------------------
// ----------------------------------------------------------

const DEFAULT_STATE: UsersStatsState = {
    stats: {
        totalUsers: 0,
        totalActiveUsers: 0,
        totalInActiveUsers: 0,
    },
    isLoading: false,
};

// -----------------------------------------------------
// ---------------------- actions ----------------------
// -----------------------------------------------------

interface UsersStatsActions {
    loadUsersStats: () => Promise<void>;
}

// ---------------------------------------------------
// ---------------------- store ----------------------
// ---------------------------------------------------
const _usersStatsStore = (instanceId: string): StateCreator<UsersStatsStoreType> => {
    return (set): UsersStatsStoreType => {
        return {
            ...DEFAULT_STATE,
            loadUsersStats: async () => {
                try {
                    set({ isLoading: true });
                    const response = await usersService.getStats();
                    set({ stats: response, isLoading: false });
                } catch (error: unknown) {
                    logger.error(`${instanceId}: loadUsersStats error`, error as Error);
                    set({ isLoading: false });
                }
            },
        };
    };
};

// ----------------------------------------------------------
// ---------------------- export store ----------------------
// ----------------------------------------------------------
export const useUsersStatsStore = create<UsersStatsStoreType>()(
    subscribeWithSelector(devtools(_usersStatsStore('main'), { name: 'users-stats-store' })),
);

// ------------------------------------------------------------
// ---------------------- export lookups ----------------------
// ------------------------------------------------------------
