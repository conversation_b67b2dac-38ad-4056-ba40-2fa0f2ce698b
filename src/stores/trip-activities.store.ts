import { create, type StateCreator } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';

import { logger } from '@/infrastructure/logging';
import { tripActivitiesService } from '@/infrastructure/api/trip-activities/trip-activities.service';
// eslint-disable-next-line import/order
import type {
    TripActivity,
    GetTripActivitiesQueryParams,
    CreateTripActivityRequest,
    DeleteTripActivityRequest,
    EditTripActivityRequest,
} from '@/infrastructure/api/trip-activities/types';
// If it's NOT exported from types.ts, use this instead:
// import type { DeleteTripActivityRequest } from '@/infrastructure/api/trip-activities/schemas';

import { DEFAULT_PAGINATION, type Pagination } from '@/infrastructure/validation/schemas/pagination.schema';

export type TripActivitiesStoreType = TripActivitiesState & TripActivitiesActions;

interface TripActivitiesState {
    activities: TripActivity[];
    selectedActivities: Set<TripActivity>;
    pagination: Pagination;
    isLoading: boolean;
    isCreating: boolean;
    isDeleting: boolean; // NEW
    currentTripId: number | null;
    lastCreatedId?: number | null;
    lastDeletedId?: number | null;
    isEditing: boolean; // NEW
    lastEditedId?: number | null;
}

const DEFAULT_STATE: TripActivitiesState = {
    activities: [],
    selectedActivities: new Set(),
    pagination: DEFAULT_PAGINATION,
    isLoading: false,
    isCreating: false,
    isDeleting: false, // NEW
    currentTripId: null,
    lastCreatedId: null,
    lastDeletedId: null, // NEW
    isEditing: false,
    lastEditedId: null,
};

interface TripActivitiesActions {
    loadTripActivities: (params: GetTripActivitiesQueryParams) => Promise<void>;
    clearTripActivities: () => void;
    createTripActivity: (tripId: number, payload: CreateTripActivityRequest) => Promise<number>;
    deleteTripActivity: (tripId: number, activityId: number, payload: DeleteTripActivityRequest) => Promise<void>; // NEW
    editTripActivity: (tripId: number, activityId: number, payload: EditTripActivityRequest) => Promise<void>;
    selectTripActivities: (activities: TripActivity[]) => void;
    selectTripActivityById: (activityId: number) => TripActivity;
    clearSelectedTripActivities: () => void;
    selectAllTripActivities: () => void;
    reset: () => void;
}

const _tripActivitiesStore = (instanceId: string): StateCreator<TripActivitiesStoreType> => {
    return (set, get): TripActivitiesStoreType => ({
        ...DEFAULT_STATE,

        loadTripActivities: async (params: GetTripActivitiesQueryParams) => {
            const { currentTripId, isLoading } = get();
            if (isLoading && currentTripId === params.tripId) return;

            set((s) => ({ ...s, isLoading: true }));

            try {
                const response = await tripActivitiesService.getTripActivities(params);
                set((s) => ({
                    ...s,
                    activities: response.data,
                    pagination: response.pagination,
                    currentTripId: params.tripId,
                    isLoading: false,
                }));
                logger.info(`tripActivitiesStore(${instanceId}): loaded`, {
                    tripId: params.tripId,
                    count: response.data.length,
                });
            } catch (error) {
                logger.error(`tripActivitiesStore(${instanceId}): load error`, error as Error);
                set(() => ({ ...DEFAULT_STATE }));
            }
        },

        clearTripActivities: () => {
            set(() => ({ ...DEFAULT_STATE }));
            logger.info(`tripActivitiesStore(${instanceId}): cleared`);
        },

        createTripActivity: async (tripId: number, payload: CreateTripActivityRequest): Promise<number> => {
            set((s) => ({ ...s, isCreating: true }));
            try {
                const { id } = await tripActivitiesService.createTripActivity(tripId, payload);

                // Refresh first page to include new item
                await get().loadTripActivities({ tripId, pageNumber: 1, pageSize: 10 });

                set((s) => ({ ...s, isCreating: false, lastCreatedId: id, currentTripId: tripId }));
                logger.info(`tripActivitiesStore(${instanceId}): created`, { tripId, id });
                return id;
            } catch (e) {
                logger.error(`tripActivitiesStore(${instanceId}): create error`, e as Error);
                set((s) => ({ ...s, isCreating: false }));
                throw e;
            }
        },

        // NEW: delete with optimistic update
        deleteTripActivity: async (
            tripId: number,
            activityId: number,
            payload: DeleteTripActivityRequest,
        ): Promise<void> => {
            const prev = get(); // snapshot to rollback if needed
            const prevActivities = prev.activities;
            const prevPagination = prev.pagination;

            // Optimistically remove the item locally
            const nextActivities = prevActivities.filter((a) => a.id !== activityId);
            const nextPagination: Pagination = {
                ...prevPagination,
                totalCount: Math.max(0, (prevPagination.totalCount || 0) - 1),
            };

            set((s) => ({
                ...s,
                isDeleting: true,
                activities: nextActivities,
                pagination: nextPagination,
                lastDeletedId: activityId,
            }));

            try {
                await tripActivitiesService.deleteTripActivity(tripId, activityId, payload);
                logger.info(`tripActivitiesStore(${instanceId}): deleted`, { tripId, activityId });
                set((s) => ({ ...s, isDeleting: false }));
                // If you prefer a hard refresh instead of optimistic update, uncomment:
                // await get().loadTripActivities({ tripId, pageNumber: 1, pageSize: 10 });
            } catch (e) {
                // rollback on failure
                logger.error(`tripActivitiesStore(${instanceId}): delete error`, e as Error);
                set((s) => ({
                    ...s,
                    isDeleting: false,
                    activities: prevActivities,
                    pagination: prevPagination,
                    lastDeletedId: null,
                }));
                throw e;
            }
        },
        editTripActivity: async (tripId, activityId, payload) => {
            set((s) => ({ ...s, isEditing: true }));
            try {
                await tripActivitiesService.editTripActivity(tripId, activityId, payload);

                // Refresh activities to reflect changes
                await get().loadTripActivities({ tripId, pageNumber: 1, pageSize: 10 });

                set((s) => ({ ...s, isEditing: false, lastEditedId: activityId }));
            } catch (e) {
                set((s) => ({ ...s, isEditing: false }));
                throw e;
            }
        },
        selectTripActivities: (activities: TripActivity[]) => {
            set({ selectedActivities: new Set(activities) });
        },
        clearSelectedTripActivities: () => {
            set({ selectedActivities: new Set() });
        },
        selectAllTripActivities: () => {
            set({ selectedActivities: new Set(get().activities) });
        },
        selectTripActivityById: (activityId: number) => {
            const activity = get().activities.find((a) => a.id === activityId);
            if (activity) {
                set({ selectedActivities: new Set([activity]) });
            }
            return activity as TripActivity;
        },

        reset: () => {
            set(DEFAULT_STATE);
            logger.info(`tripActivitiesStore(${instanceId}): reset to default state`);
        },
    });
};

export const useTripActivitiesStore = create<TripActivitiesStoreType>()(
    subscribeWithSelector(devtools(_tripActivitiesStore('main'), { name: 'trip-activities-store' })),
);

export const useTripActivitiesLookups = () =>
    useTripActivitiesStore((state) => state.activities).map((activity) => ({
        id: activity.id,
        note: activity.note,
        details: activity.details,
        status: activity.status.name,
        action: activity.action.name,
        createdAt: activity.createdAt,
        origin: activity.origin,
    }));
