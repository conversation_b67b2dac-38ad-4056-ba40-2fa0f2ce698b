import { http, HttpResponse } from 'msw';

import { appConfig } from '@/shared/config/app-settings.config';

import { dummyAlertTypesResponse } from './alert-types.mock';
import { MapPointData } from './map-points.mock';
import { PortsData } from './ports.mock';
import { mockPortStatisticsData } from './port-statistics.mock';
import { TripActivitiesData } from './trip-activities.mock';
import { TripAlertsData } from './trip-alerts.mock';
import { TripDetailMock } from './trip-detail.mock';
import { TripLogsData } from './trip-logs.mock';
import { TripStopsData } from './trip-stops.mock';
import { TripsStats } from './tripsStats.mock';
// import { TripLocationsData } from './trip-location.mock';
// import { TripsData } from './trips';

const apiUrl = appConfig.get('apiUrl');
export const handlers = [
    // Alert Types
    http.get(`${apiUrl}/alert-types`, () => {
        return HttpResponse.json(dummyAlertTypesResponse);
    }),

    // Trips
    // http.post(`${apiUrl}/trips`, async ({ request }) => {
    //     const body = await request.json();
    //     return HttpResponse.json(TripsData);
    // }),

    http.get(`${apiUrl}/trips/stats`, () => {
        return HttpResponse.json(TripsStats);
    }),
    http.get(`${apiUrl}/trips/:id`, () => {
        return HttpResponse.json(TripDetailMock);
    }),
    // http.post(`${apiUrl}/trips/location`, async ({ request }) => {
    //     const body = await request.json();
    //     return HttpResponse.json(TripLocationsData);
    // }),

    // Trip Activities
    http.get(`${apiUrl}/trips/:tripId/activities`, () => {
        return HttpResponse.json(TripActivitiesData);
    }),
    // http.post(`${apiUrl}/trips/:tripId/activities`, () => {
    // return HttpResponse.json({ success: true });
    // }),
    http.delete(`${apiUrl}/trips/:tripId/activities/:activityId`, () => {
        return HttpResponse.json({ success: true });
    }),

    // Trip Alerts
    http.get(`${apiUrl}/trips/:tripId/alerts`, () => {
        return HttpResponse.json(TripAlertsData);
    }),
    http.put(`${apiUrl}/trips/:tripId/alerts/:alertId/acknowledge`, () => {
        return HttpResponse.json({ success: true });
    }),
    http.put(`${apiUrl}/trips/:tripId/alerts/acknowledge`, () => {
        return HttpResponse.json({ success: true });
    }),

    // Trip Logs
    http.get(`${apiUrl}/trips/:tripId/logs`, () => {
        return HttpResponse.json(TripLogsData);
    }),

    // Trip Pings
    http.get(`${apiUrl}/trips/:tripId/pings`, () => {
        return HttpResponse.json({
            data: [
                { id: 1, lat: 24.7136, long: 46.6753, time: '2025-09-23T08:00:00Z' },
                { id: 2, lat: 24.7142, long: 46.676, time: '2025-09-23T08:05:00Z' },
                { id: 3, lat: 24.715, long: 46.6765, time: '2025-09-23T08:10:00Z' },
            ],
        });
    }),

    // Trip Stops
    http.get(`${apiUrl}/trips/:tripId/stops`, () => {
        return HttpResponse.json(TripStopsData);
    }),

    // Map Points
    http.get(`${apiUrl}/map-points`, () => {
        return HttpResponse.json(MapPointData);
    }),

    // Ports
    http.get(`${apiUrl}/ports`, () => {
        return HttpResponse.json(PortsData);
    }),
    http.get(`${apiUrl}/ports/:portId`, () => {
        return HttpResponse.json(PortsData.data[0]);
    }),

    // Port Statistics
    http.get(`${apiUrl}/ports/statistics`, () => {
        return HttpResponse.json(mockPortStatisticsData);
    }),

    // Activity Lookups
    http.get(`${apiUrl}/activities/statuses`, () => {
        return HttpResponse.json({
            data: [
                { id: 1, name: { arabic: 'مكتمل', english: 'Completed' } },
                { id: 2, name: { arabic: 'قيد التنفيذ', english: 'In Progress' } },
                { id: 3, name: { arabic: 'معلق', english: 'Pending' } },
            ],
        });
    }),
    http.get(`${apiUrl}/activities/actions`, () => {
        return HttpResponse.json({
            data: [
                { id: 1, name: { arabic: 'بدء', english: 'Start' } },
                { id: 2, name: { arabic: 'إنهاء', english: 'End' } },
                { id: 3, name: { arabic: 'تحديث', english: 'Update' } },
            ],
        });
    }),
];
