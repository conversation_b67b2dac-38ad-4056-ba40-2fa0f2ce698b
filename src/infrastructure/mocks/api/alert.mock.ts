import type { Alert } from '@/stores/alert.store';
import { LocationSource, ChargerStatus, TripLocation } from '@/shared/enums';

export function generateMockAlerts(count: number): Alert[] {
    const alerts: Alert[] = [];
    const chargerStatuses = [ChargerStatus.CONNECTED, ChargerStatus.DISCONNECTED];
    const locationSources = [LocationSource.EXACT, LocationSource.LAST_KNOWN];
    const tripZones = [TripLocation.ENTRY, TripLocation.ON_ROUTE, TripLocation.EXIT];

    for (let i = 0; i < count; i++) {
        const alertTypeId = (i % 10) + 1; // 🔥 يوزع الأرقام بين 1 و 10
        const baseTime = new Date('2025-08-19T06:30:00Z');
        const fromTime = new Date(baseTime.getTime() + i * 60_000); // دقيقة لكل alert
        const toTime = i % 3 === 0 ? null : new Date(fromTime.getTime() + 30_000);

        alerts.push({
            alertId: i + 1,
            tripId: (i % 40) + 1, // ✅ محصور بين 1 و 40
            alertType: {
                id: alertTypeId,
                name: {
                    english: 'Alert Type ' + alertTypeId,
                    arabic: 'نوع التنبيه ' + alertTypeId,
                },
            },
            transitNumber: `TR-${String(i + 1).padStart(6, '0')}`,
            entryPort: {
                id: (i % 5) + 1,
                name: {
                    english: `Port ${(i % 5) + 1}`,
                    arabic: `ميناء ${(i % 5) + 1}`,
                },
            },
            exitPort: {
                id: ((i + 2) % 5) + 1,
                name: {
                    english: `Port ${((i + 2) % 5) + 1}`,
                    arabic: `ميناء ${((i + 2) % 5) + 1}`,
                },
            },
            fromStateDateTime: fromTime,
            toStateDateTime: toTime,
            currentState: {
                id: 100 + i,
                dateTime: new Date(fromTime.getTime() + 5_000),
                locationSource: locationSources[i % locationSources.length],
                batteryLevelPercentage: Math.floor(Math.random() * 101), // 0–100
                chargerStatus: chargerStatuses[i % chargerStatuses.length],
                gpsSignalStrength: Math.floor(Math.random() * 101),
                gsmSignalStrength: Math.floor(Math.random() * 101),
                currentSpeed: Math.floor(Math.random() * 150),
                routeZone: tripZones[i % tripZones.length],
                isWithinRouteGeofence: i % 2 === 0,
                timeElapsedSinceTripStartInMinutes: i * 5,
                remainingDistanceInMeters: Math.floor(Math.random() * 200_000),
                isWithinSuspiciousZone: i % 4 === 0,
                trackerDateTime: new Date(fromTime.getTime() - 10_000),
            },
            isAcknowledged: i % 5 === 0, // كل 5 alerts يكون معترف بيه
        });
    }

    return alerts;
}

export const mockTripAlerts: Alert[] = generateMockAlerts(150); // 🔥 أكتر من 150
