import type { GetTripAlertsResponse } from '../../api/trip-alerts/types';

/**
 * Mock trip alerts data.
 * 10 alerts happening along a realistic trip route in Saudi Arabia.
 * All alerts are within max 3 hours duration except one that is still ongoing.
 */
export const TripAlertsData: GetTripAlertsResponse = {
    data: [
        // 1. Tracker <PERSON> (still ongoing) - near Jeddah
        {
            id: 1,
            alertType: {
                id: 10000,
                name: { arabic: 'العبث بجهاز التتبع', english: 'Tracker Tamper' },
                origin: 2,
            },
            fromState: {
                stateId: 101,
                trackerDateTime: new Date('2025-09-30T08:00:00.000Z').toISOString(),
                locationSource: 1,
                batteryLevelPercentage: 92,
                chargerStatus: 1,
                gpsSignalStrength: 50,
                gsmSignalStrength: 35,
                currentSpeed: 0,
                routeZone: 1,
                isWithinRouteGeofence: true,
                timeElapsedSinceTripStartInMinutes: 0,
                remainingDistanceInMeters: 1100000,
                completeDistanceInMeters: 0,
                isWithinSuspiciousZone: false,
                createdAt: new Date('2025-09-30T08:00:00.000Z').toISOString(),
                lat: 21.4858,
                long: 39.1432,
                address: {
                    arabic: 'Jeddah Islamic Port - ميناء جدة الإسلامي',
                    english: 'Jeddah Islamic Port - ميناء جدة الإسلامي',
                },
            },
            toState: null, // ongoing
            acknowledgedAt: null,
            acknowledgedBy: null,
        },

        // 2. Tracker Dropped (duration ~20 mins)
        {
            id: 10001,
            alertType: {
                id: 3,
                name: { arabic: 'سقوط جهاز التتبع', english: 'Tracker Dropped' },
                origin: 2,
            },
            fromState: {
                stateId: 102,
                trackerDateTime: new Date('2025-09-30T10:00:00.000Z').toISOString(),
                locationSource: 1,
                batteryLevelPercentage: 88,
                chargerStatus: 0,
                gpsSignalStrength: 42,
                gsmSignalStrength: 30,
                currentSpeed: 60,
                routeZone: 1,
                isWithinRouteGeofence: true,
                createdAt: new Date('2025-09-30T10:00:01.000Z').toISOString(),
                lat: 21.3,
                long: 40.4,
                address: {
                    arabic: 'Highway near Taif - طريق الطائف',
                    english: 'Highway near Taif - طريق الطائف',
                },
                timeElapsedSinceTripStartInMinutes: 45,
                remainingDistanceInMeters: 12500,
                completeDistanceInMeters: 32000,
                isWithinSuspiciousZone: false,
            },
            toState: {
                stateId: 103,
                trackerDateTime: new Date('2025-09-30T10:20:00.000Z').toISOString(),
                locationSource: 1,
                batteryLevelPercentage: 87,
                chargerStatus: 0,
                gpsSignalStrength: 40,
                gsmSignalStrength: 28,
                currentSpeed: 0,
                routeZone: 1,
                isWithinRouteGeofence: true,
                createdAt: new Date('2025-09-30T10:20:01.000Z').toISOString(),
                lat: 21.31,
                long: 40.41,
                address: {
                    arabic: 'Taif checkpoint',
                    english: 'Taif checkpoint',
                },
                timeElapsedSinceTripStartInMinutes: 45,
                remainingDistanceInMeters: 0,
                completeDistanceInMeters: 32000,
                isWithinSuspiciousZone: false,
            },
            acknowledgedAt: new Date('2025-09-30T10:25:00.000Z'),
            acknowledgedBy: { id: '111', name: 'Admin A' },
        },

        // 3. Lock Tamper (duration ~1 hour) - near Riyadh
        {
            id: 3,
            alertType: {
                id: 10002,
                name: { arabic: 'العبث في القفل', english: 'Lock Tamper' },
                origin: 2,
            },
            fromState: {
                stateId: 104,
                trackerDateTime: new Date('2025-09-30T15:00:00.000Z').toISOString(),
                locationSource: 1,
                batteryLevelPercentage: 75,
                chargerStatus: 1,
                gpsSignalStrength: 45,
                gsmSignalStrength: 32,
                currentSpeed: 30,
                routeZone: 2,
                isWithinRouteGeofence: false,
                createdAt: new Date('2025-09-30T15:00:01.000Z').toISOString(),
                lat: 24.71,
                long: 46.67,
                address: {
                    arabic: 'Riyadh outskirts - ضواحي الرياض',
                    english: 'Riyadh outskirts - ضواحي الرياض',
                },
                timeElapsedSinceTripStartInMinutes: 45,
                remainingDistanceInMeters: 12500,
                completeDistanceInMeters: 32000,
                isWithinSuspiciousZone: false,
            },
            toState: {
                stateId: 105,
                trackerDateTime: new Date('2025-09-30T16:00:00.000Z').toISOString(),
                locationSource: 1,
                batteryLevelPercentage: 74,
                chargerStatus: 1,
                gpsSignalStrength: 44,
                gsmSignalStrength: 31,
                currentSpeed: 0,
                routeZone: 2,
                isWithinRouteGeofence: true,
                createdAt: new Date('2025-09-30T16:00:01.000Z').toISOString(),
                lat: 24.72,
                long: 46.68,
                address: {
                    arabic: 'Riyadh checkpoint',
                    english: 'Riyadh checkpoint',
                },
                timeElapsedSinceTripStartInMinutes: 45,
                remainingDistanceInMeters: 0,
                completeDistanceInMeters: 32000,
                isWithinSuspiciousZone: false,
            },
            acknowledgedAt: null,
            acknowledgedBy: null,
        },

        // 4. Lock Open (duration ~1.5 hours) - Al-Ahsa
        {
            id: 4,
            alertType: {
                id: 10003,
                name: { arabic: 'تم فتح القفل', english: 'Lock Open' },
                origin: 2,
            },
            fromState: {
                stateId: 106,
                trackerDateTime: new Date('2025-09-30T18:00:00.000Z').toISOString(),
                locationSource: 1,
                batteryLevelPercentage: 65,
                chargerStatus: 0,
                gpsSignalStrength: 48,
                gsmSignalStrength: 36,
                currentSpeed: 20,
                routeZone: 3,
                isWithinRouteGeofence: true,
                createdAt: new Date('2025-09-30T18:00:01.000Z').toISOString(),
                lat: 25.35,
                long: 49.57,
                address: {
                    arabic: 'Al-Ahsa highway - الأحساء',
                    english: 'Al-Ahsa highway - الأحساء',
                },
                timeElapsedSinceTripStartInMinutes: 45,
                remainingDistanceInMeters: 12500,
                completeDistanceInMeters: 32000,
                isWithinSuspiciousZone: false,
            },
            toState: {
                stateId: 107,
                trackerDateTime: new Date('2025-09-30T19:30:00.000Z').toISOString(),
                locationSource: 1,
                batteryLevelPercentage: 64,
                chargerStatus: 0,
                gpsSignalStrength: 47,
                gsmSignalStrength: 34,
                currentSpeed: 0,
                routeZone: 3,
                isWithinRouteGeofence: true,
                createdAt: new Date('2025-09-30T19:30:01.000Z').toISOString(),
                lat: 25.36,
                long: 49.58,
                address: {
                    arabic: 'Al-Ahsa checkpoint',
                    english: 'Al-Ahsa checkpoint',
                },
                timeElapsedSinceTripStartInMinutes: 45,
                remainingDistanceInMeters: 0,
                completeDistanceInMeters: 32000,
                isWithinSuspiciousZone: false,
            },
            acknowledgedAt: new Date('2025-09-30T20:00:00.000Z'),
            acknowledgedBy: { id: '222', name: 'Operator B' },
        },

        // 5. Lock Connection Lost (~45 mins) - Al-Ahsa desert
        {
            id: 5,
            alertType: {
                id: 10004,
                name: { arabic: 'فقدان الإتصال مع القفل', english: 'Lock Connection Lost' },
                origin: 2,
            },
            fromState: {
                stateId: 108,
                trackerDateTime: new Date('2025-09-30T21:00:00.000Z').toISOString(),
                locationSource: 2,
                batteryLevelPercentage: 55,
                chargerStatus: 0,
                gpsSignalStrength: 0,
                gsmSignalStrength: 0,
                currentSpeed: 0,
                routeZone: 3,
                isWithinRouteGeofence: false,
                createdAt: new Date('2025-09-30T21:00:01.000Z').toISOString(),
                lat: 25.39,
                long: 49.58,
                address: {
                    arabic: 'Al-Ahsa desert road',
                    english: 'Al-Ahsa desert road',
                },
                timeElapsedSinceTripStartInMinutes: 45,
                remainingDistanceInMeters: 12500,
                completeDistanceInMeters: 32000,
                isWithinSuspiciousZone: false,
            },
            toState: {
                stateId: 109,
                trackerDateTime: new Date('2025-09-30T21:45:00.000Z').toISOString(),
                locationSource: 2,
                batteryLevelPercentage: 54,
                chargerStatus: 0,
                gpsSignalStrength: 20,
                gsmSignalStrength: 10,
                currentSpeed: 0,
                routeZone: 3,
                isWithinRouteGeofence: true,
                createdAt: new Date('2025-09-30T21:45:01.000Z').toISOString(),
                lat: 25.4,
                long: 49.59,
                address: {
                    arabic: 'Al-Ahsa connection restored',
                    english: 'Al-Ahsa connection restored',
                },
                timeElapsedSinceTripStartInMinutes: 45,
                remainingDistanceInMeters: 0,
                completeDistanceInMeters: 32000,
                isWithinSuspiciousZone: false,
            },
            acknowledgedAt: null,
            acknowledgedBy: null,
        },

        // 6. Tracker Battery Low (~2 hours) - east of Riyadh
        {
            id: 6,
            alertType: {
                id: 10005,
                name: { arabic: 'بطارية جهاز التتبع ضعيفة', english: 'Tracker Battery Low' },
                origin: 2,
            },
            fromState: {
                stateId: 110,
                trackerDateTime: new Date('2025-09-30T23:00:00.000Z').toISOString(),
                locationSource: 2,
                batteryLevelPercentage: 18,
                chargerStatus: 0,
                gpsSignalStrength: 28,
                gsmSignalStrength: 14,
                currentSpeed: 50,
                routeZone: 3,
                isWithinRouteGeofence: true,
                createdAt: new Date('2025-09-30T23:00:01.000Z').toISOString(),
                lat: 26.0,
                long: 48.0,
                address: {
                    arabic: 'Eastern highway near Qassim',
                    english: 'Eastern highway near Qassim',
                },
                timeElapsedSinceTripStartInMinutes: 45,
                remainingDistanceInMeters: 0,
                completeDistanceInMeters: 32000,
                isWithinSuspiciousZone: false,
            },
            toState: {
                stateId: 111,
                trackerDateTime: new Date('2025-10-01T01:00:00.000Z').toISOString(),
                locationSource: 2,
                batteryLevelPercentage: 15,
                chargerStatus: 0,
                gpsSignalStrength: 27,
                gsmSignalStrength: 13,
                currentSpeed: 0,
                routeZone: 3,
                isWithinRouteGeofence: true,
                createdAt: new Date('2025-10-01T01:00:01.000Z').toISOString(),
                lat: 26.01,
                long: 48.01,
                address: {
                    arabic: 'Eastern highway checkpoint',
                    english: 'Eastern highway checkpoint',
                },
                timeElapsedSinceTripStartInMinutes: 45,
                remainingDistanceInMeters: 0,
                completeDistanceInMeters: 32000,
                isWithinSuspiciousZone: false,
            },
            acknowledgedAt: new Date('2025-10-01T01:15:00.000Z'),
            acknowledgedBy: { id: '333', name: 'System Bot' },
        },

        // 7. Lock Low Battery (~1 hour) - Jubail
        {
            id: 7,
            alertType: {
                id: 10007,
                name: { arabic: 'بطارية القفل ضعيفة', english: 'Lock Low Battery' },
                origin: 2,
            },
            fromState: {
                stateId: 112,
                trackerDateTime: new Date('2025-10-01T03:00:00.000Z').toISOString(),
                locationSource: 2,
                batteryLevelPercentage: 14,
                chargerStatus: 0,
                gpsSignalStrength: 22,
                gsmSignalStrength: 11,
                currentSpeed: 15,
                routeZone: 4,
                isWithinRouteGeofence: false,
                createdAt: new Date('2025-10-01T03:00:01.000Z').toISOString(),
                lat: 27.0,
                long: 49.65,
                address: {
                    arabic: 'Jubail Industrial City',
                    english: 'Jubail Industrial City',
                },
                timeElapsedSinceTripStartInMinutes: 45,
                remainingDistanceInMeters: 0,
                completeDistanceInMeters: 32000,
                isWithinSuspiciousZone: false,
            },
            toState: {
                stateId: 113,
                trackerDateTime: new Date('2025-10-01T04:00:00.000Z').toISOString(),
                locationSource: 2,
                batteryLevelPercentage: 13,
                chargerStatus: 0,
                gpsSignalStrength: 20,
                gsmSignalStrength: 10,
                currentSpeed: 0,
                routeZone: 4,
                isWithinRouteGeofence: true,
                createdAt: new Date('2025-10-01T04:00:01.000Z').toISOString(),
                lat: 27.01,
                long: 49.66,
                address: {
                    arabic: 'Jubail checkpoint',
                    english: 'Jubail checkpoint',
                },
                timeElapsedSinceTripStartInMinutes: 45,
                remainingDistanceInMeters: 0,
                completeDistanceInMeters: 32000,
                isWithinSuspiciousZone: false,
            },
            acknowledgedAt: null,
            acknowledgedBy: null,
        },

        // 8. Lock Very Low Battery (~30 mins) - Dammam approach
        {
            id: 8,
            alertType: {
                id: 10060,
                name: { arabic: 'بطارية القفل منخفضة جداً', english: 'Lock Very Low Battery' },
                origin: 2,
            },
            fromState: {
                stateId: 114,
                trackerDateTime: new Date('2025-10-01T05:00:00.000Z').toISOString(),
                locationSource: 1,
                batteryLevelPercentage: 5,
                chargerStatus: 0,
                gpsSignalStrength: 10,
                gsmSignalStrength: 5,
                currentSpeed: 0,
                routeZone: 4,
                isWithinRouteGeofence: false,
                createdAt: new Date('2025-10-01T05:00:01.000Z').toISOString(),
                lat: 26.32,
                long: 50.0,
                address: {
                    arabic: 'Dammam approach road',
                    english: 'Dammam approach road',
                },
                timeElapsedSinceTripStartInMinutes: 45,
                remainingDistanceInMeters: 0,
                completeDistanceInMeters: 32000,
                isWithinSuspiciousZone: false,
            },
            toState: {
                stateId: 115,
                trackerDateTime: new Date('2025-10-01T05:30:00.000Z').toISOString(),
                locationSource: 1,
                batteryLevelPercentage: 4,
                chargerStatus: 0,
                gpsSignalStrength: 9,
                gsmSignalStrength: 4,
                currentSpeed: 0,
                routeZone: 4,
                isWithinRouteGeofence: true,
                createdAt: new Date('2025-10-01T05:30:01.000Z').toISOString(),
                lat: 26.33,
                long: 50.01,
                address: {
                    arabic: 'Dammam checkpoint',
                    english: 'Dammam checkpoint',
                },
                timeElapsedSinceTripStartInMinutes: 45,
                remainingDistanceInMeters: 0,
                completeDistanceInMeters: 32000,
                isWithinSuspiciousZone: false,
            },
            acknowledgedAt: new Date('2025-10-01T06:00:00.000Z'),
            acknowledgedBy: { id: '444', name: 'Supervisor' },
        },

        // 9. GSM Signal Lost (~2 hours) - near King Abdulaziz Port
        {
            id: 9,
            alertType: {
                id: 10008,
                name: { arabic: 'فقدان إشارة شبكة الاتصال', english: 'GSM Signal Lost' },
                origin: 2,
            },
            fromState: {
                stateId: 116,
                trackerDateTime: new Date('2025-10-01T07:00:00.000Z').toISOString(),
                locationSource: 1,
                batteryLevelPercentage: 40,
                chargerStatus: 1,
                gpsSignalStrength: 30,
                gsmSignalStrength: 0,
                currentSpeed: 5,
                routeZone: 5,
                isWithinRouteGeofence: false,
                createdAt: new Date('2025-10-01T07:00:01.000Z').toISOString(),
                lat: 26.3927,
                long: 49.9777,
                address: {
                    arabic: 'Near King Abdulaziz Port',
                    english: 'Near King Abdulaziz Port',
                },
                timeElapsedSinceTripStartInMinutes: 45,
                remainingDistanceInMeters: 12500,
                completeDistanceInMeters: 32000,
                isWithinSuspiciousZone: false,
            },
            toState: {
                stateId: 117,
                trackerDateTime: new Date('2025-10-01T09:00:00.000Z').toISOString(),
                locationSource: 1,
                batteryLevelPercentage: 39,
                chargerStatus: 1,
                gpsSignalStrength: 28,
                gsmSignalStrength: 10,
                currentSpeed: 0,
                routeZone: 5,
                isWithinRouteGeofence: true,
                createdAt: new Date('2025-10-01T09:00:01.000Z').toISOString(),
                lat: 26.4,
                long: 50.0,
                address: {
                    arabic: 'Dammam port entry',
                    english: 'Dammam port entry',
                },
                timeElapsedSinceTripStartInMinutes: 45,
                remainingDistanceInMeters: 0,
                completeDistanceInMeters: 32000,
                isWithinSuspiciousZone: false,
            },
            acknowledgedAt: null,
            acknowledgedBy: null,
        },

        // 10. GPS Signal Lost (~1 hour) - final point (port)
        {
            id: 10,
            alertType: {
                id: 1,
                name: { arabic: 'فقدان إشارة تحديد الموقع', english: 'GPS Signal Lost' },
                origin: 2,
            },
            fromState: {
                stateId: 10009,
                trackerDateTime: new Date('2025-10-01T10:00:00.000Z').toISOString(),
                locationSource: 1,
                batteryLevelPercentage: 35,
                chargerStatus: 0,
                gpsSignalStrength: 0,
                gsmSignalStrength: 20,
                currentSpeed: 0,
                routeZone: 5,
                isWithinRouteGeofence: true,
                createdAt: new Date('2025-10-01T10:00:01.000Z').toISOString(),
                lat: 26.405,
                long: 50.06,
                address: {
                    arabic: 'King Abdulaziz Port - الدمام',
                    english: 'King Abdulaziz Port - الدمام',
                },
                timeElapsedSinceTripStartInMinutes: 45,
                remainingDistanceInMeters: 12500,
                completeDistanceInMeters: 32000,
                isWithinSuspiciousZone: false,
            },
            toState: {
                stateId: 119,
                trackerDateTime: new Date('2025-10-01T11:00:00.000Z').toISOString(),
                locationSource: 1,
                batteryLevelPercentage: 34,
                chargerStatus: 0,
                gpsSignalStrength: 5,
                gsmSignalStrength: 22,
                currentSpeed: 0,
                routeZone: 5,
                isWithinRouteGeofence: true,
                createdAt: new Date('2025-10-01T11:00:01.000Z').toISOString(),
                lat: 26.406,
                long: 50.07,
                address: {
                    arabic: 'King Abdulaziz Port checkpoint',
                    english: 'King Abdulaziz Port checkpoint',
                },
                timeElapsedSinceTripStartInMinutes: 45,
                remainingDistanceInMeters: 0,
                completeDistanceInMeters: 32000,
                isWithinSuspiciousZone: false,
            },
            acknowledgedAt: new Date('2025-10-01T11:30:00.000Z'),
            acknowledgedBy: { id: '555', name: 'Control Center' },
        },
    ],
    pagination: {
        pageSize: 10,
        currentPage: 1,
        totalPages: 1,
        totalCount: 10,
        hasPrevious: false,
        hasNext: false,
    },
};
