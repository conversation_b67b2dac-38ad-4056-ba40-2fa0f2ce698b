import type { TripLocationResponse } from '../../api/trips/types';

export const TripLocationData: TripLocationResponse = {
    data: [
        // Riyadh
        { id: 123, location: { latitude: 24.7136, longitude: 46.6753 }, bearing: 90 },
        { id: 194941, location: { latitude: 24.7743, longitude: 46.7386 }, bearing: 180 },
        { id: 1235, location: { latitude: 24.6426, longitude: 46.7706 }, bearing: 270 },

        // Jeddah
        { id: 1236, location: { latitude: 21.3891, longitude: 39.8579 }, bearing: 120 },
        { id: 194940, location: { latitude: 21.5433, longitude: 39.1728 }, bearing: 200 },
        { id: 6, location: { latitude: 21.2854, longitude: 39.2376 }, bearing: 300 },

        // <PERSON>kkah
        { id: 7, location: { latitude: 21.3891, longitude: 39.8579 }, bearing: 45 },
        { id: 8, location: { latitude: 21.4266, longitude: 39.8256 }, bearing: 135 },

        // Mad<PERSON>h
        { id: 9, location: { latitude: 24.5247, longitude: 39.5692 }, bearing: 225 },
        { id: 10, location: { latitude: 24.4709, longitude: 39.6122 }, bearing: 315 },

        // Dammam
        { id: 11, location: { latitude: 26.3927, longitude: 49.9777 }, bearing: 60 },
        { id: 12, location: { latitude: 26.4344, longitude: 50.1033 }, bearing: 150 },

        // Khobar
        { id: 13, location: { latitude: 26.2172, longitude: 50.1971 }, bearing: 210 },
        { id: 14, location: { latitude: 26.3024, longitude: 50.1961 }, bearing: 20 },

        // Taif
        { id: 15, location: { latitude: 21.2703, longitude: 40.4158 }, bearing: 80 },
        { id: 16, location: { latitude: 21.3222, longitude: 40.436 }, bearing: 190 },

        // Abha
        { id: 17, location: { latitude: 18.2465, longitude: 42.5117 }, bearing: 250 },
        { id: 18, location: { latitude: 18.307, longitude: 42.5053 }, bearing: 320 },

        // Jazan
        { id: 19, location: { latitude: 16.889, longitude: 42.551 }, bearing: 30 },
        { id: 20, location: { latitude: 16.9264, longitude: 42.5663 }, bearing: 100 },

        // Tabuk
        { id: 21, location: { latitude: 28.3834, longitude: 36.5662 }, bearing: 170 },
        { id: 22, location: { latitude: 28.3998, longitude: 36.5715 }, bearing: 240 },

        // Hail
        { id: 23, location: { latitude: 27.5219, longitude: 41.6907 }, bearing: 310 },
        { id: 24, location: { latitude: 27.5141, longitude: 41.72 }, bearing: 15 },

        // Buraidah
        { id: 25, location: { latitude: 26.359, longitude: 43.9819 }, bearing: 75 },
        { id: 26, location: { latitude: 26.3556, longitude: 44.0031 }, bearing: 145 },

        // Najran
        { id: 27, location: { latitude: 17.565, longitude: 44.2289 }, bearing: 215 },
        { id: 28, location: { latitude: 17.619, longitude: 44.2292 }, bearing: 285 },

        // Al-Ula
        { id: 29, location: { latitude: 26.6186, longitude: 37.9232 }, bearing: 355 },
        { id: 30, location: { latitude: 26.6065, longitude: 37.9241 }, bearing: 65 },

        // Yanbu
        { id: 31, location: { latitude: 24.0889, longitude: 38.0637 }, bearing: 135 },
        { id: 32, location: { latitude: 24.0895, longitude: 38.085 }, bearing: 205 },

        // Qassim Region
        { id: 33, location: { latitude: 25.8694, longitude: 43.4973 }, bearing: 275 },
        { id: 34, location: { latitude: 25.8777, longitude: 43.5156 }, bearing: 345 },

        // Eastern Province (Jubail)
        { id: 35, location: { latitude: 27.0046, longitude: 49.6608 }, bearing: 55 },
        { id: 36, location: { latitude: 27.02, longitude: 49.655 }, bearing: 125 },

        // Northern Borders (Arar)
        { id: 37, location: { latitude: 30.9753, longitude: 41.0381 }, bearing: 195 },
        { id: 38, location: { latitude: 31.01, longitude: 41.02 }, bearing: 265 },

        // Sakaka (Al Jouf)
        { id: 39, location: { latitude: 29.9697, longitude: 40.2064 }, bearing: 335 },
        { id: 40, location: { latitude: 29.981, longitude: 40.215 }, bearing: 25 },
    ],
    pagination: {
        pageSize: 20,
        currentPage: 1,
        totalPages: 2,
        totalCount: 40,
        hasPrevious: false,
        hasNext: true,
    },
};
