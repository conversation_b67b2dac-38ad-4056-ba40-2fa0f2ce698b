import type { TripDetail } from '../../api/trips/types';

export const TripDetailMock: TripDetail = {
    id: 194940,
    transitNumber: 123,
    status: 'Active',
    trackingStatus: 'Started',
    vehicle: { id: 42, plateNo: 'ABC121559699' },
    driver: {
        id: 42,
        name: 'dimo',
        mobileNo: '1234968863',
        passportCountry: 'Saudi Arabia',
        passportNumber: '********',
    },
    routeId: 21,
    entryPort: { id: 1, name: { arabic: 'جدة', english: 'Jeddah' } },
    exitPort: { id: 2, name: { arabic: ' الرياض', english: 'Riyadh' } },
    startDate: '2025-08-18T07:46:49.5847239',
    endDate: null,
    tracker: { id: 2, serialNumber: '7798EA46' },
    eLocks: [{ deviceId: 24, serialNumber: 'ELock-E1001' }],
    trackingPriority: 'Medium',
    transitDate: '30-1-1444',
    transSeq: 'V123',
    shipmentDescription: 'ShipmentDescription',
    newShipmentDescription: null,
    transitTypeName: 'TransitTypeName',
    ownerDesc: 'OwnerDescription',
    ownerType: 'Company',
    activeAlerts: [
        {
            alertStateChangeId: 5001,
            alertType: {
                id: 1,
                name: { arabic: 'تنبيه السرعة', english: 'Overspeed Alert' },
                priority: 1,
                origin: 1,
                code: 1001,
            },
            fromState: { stateId: 10, timestamp: '2025-08-28T12:05:00Z' },
            toState: { stateId: 20, timestamp: '2025-08-28T12:06:00Z' },
        },
        {
            alertStateChangeId: 5002,
            alertType: {
                id: 2,
                name: { arabic: 'تنبيه جغرافي', english: 'Geofence Alert' },
                priority: 2,
                origin: 2,
                code: 2001,
            },
            fromState: { stateId: 30, timestamp: '2025-08-28T13:00:00Z' },
            toState: null, // allowed
        },
    ],
    currentState: {
        trackerDateTime: '2025-08-19T01:07:02.910231+03:00',
        locationSource: 0,
        batteryLevelPercentage: 85,
        chargerStatus: 0,
        gpsSignalStrength: 4,
        gsmSignalStrength: 4,
        currentSpeed: 60,
        routeZone: 1,
        isWithinRouteGeofence: true,
        timeElapsedSinceTripStartInMinutes: 15,
        remainingDistanceInMeters: 12000,
        completeDistanceInMeters: 50000,
        isWithinSuspiciousZone: false,
        createdAt: '2025-08-19T01:07:02.910231+03:00',
        lat: 26.406,
        long: 50.07,
        address: {
            arabic: 'King Abdulaziz Port checkpoint',
            english: 'King Abdulaziz Port checkpoint',
        },
    },
    securityNotes: 'No security concerns',
    isFocused: true,
    isSuspicious: true,
};

// ✅ Another mock trip with active alerts
export const TripDetailWithAlertsMock: TripDetail = {
    id: 194941,
    transitNumber: 124,
    status: 'Active',
    trackingStatus: 'Active',
    vehicle: { id: 43, plateNo: 'XYZ998877' },
    driver: {
        id: 43,
        name: 'Sara Ali',
        mobileNo: '9876543210',
        passportCountry: 'Saudi Arabia',
        passportNumber: '********',
    },
    routeId: 22,
    entryPort: { id: 1, name: { arabic: 'جدة', english: 'Jeddah' } },
    exitPort: { id: 2, name: { arabic: ' الرياض', english: 'Riyadh' } },
    startDate: '2025-08-19T09:30:00.000Z',
    endDate: null,
    tracker: { id: 3, serialNumber: 'AA12BB34' },
    eLocks: [
        { deviceId: 25, serialNumber: 'ELock-E1001' },
        { deviceId: 26, serialNumber: 'ELock-E1002' },
    ],
    trackingPriority: 'High',
    transitDate: '1-2-1446',
    transSeq: 'V124',
    shipmentDescription: 'Sensitive goods',
    newShipmentDescription: 'Updated goods description',
    transitTypeName: 'International',
    ownerDesc: 'Owner XYZ',
    ownerType: 'Individual',
    activeAlerts: [
        {
            alertStateChangeId: 5001,
            alertType: {
                id: 1,
                name: { arabic: 'تنبيه السرعة', english: 'Overspeed Alert' },
                priority: 1,
                origin: 1,
                code: 1001,
            },
            fromState: { stateId: 10, timestamp: '2025-08-28T12:05:00Z' },
            toState: { stateId: 20, timestamp: '2025-08-28T12:06:00Z' },
        },
        {
            alertStateChangeId: 5002,
            alertType: {
                id: 2,
                name: { arabic: 'تنبيه جغرافي', english: 'Geofence Alert' },
                priority: 2,
                origin: 2,
                code: 2001,
            },
            fromState: { stateId: 30, timestamp: '2025-08-28T13:00:00Z' },
            toState: null, // allowed
        },
    ],
    currentState: {
        trackerDateTime: '2025-08-19T10:10:00.000Z',
        locationSource: 0,
        batteryLevelPercentage: 60,
        chargerStatus: 1,
        gpsSignalStrength: 3,
        gsmSignalStrength: 2,
        currentSpeed: 45,
        routeZone: 0,
        isWithinRouteGeofence: false,
        timeElapsedSinceTripStartInMinutes: 35,
        remainingDistanceInMeters: 50000,
        completeDistanceInMeters: 75000,
        isWithinSuspiciousZone: true,
        createdAt: '2025-08-19T10:10:00.000Z',
        lat: 26.406,
        long: 50.07,
        address: {
            arabic: 'King Abdulaziz Port checkpoint',
            english: 'King Abdulaziz Port checkpoint',
        },
    },
    securityNotes: 'High priority shipment',
    isFocused: true,
    isSuspicious: true,
};
