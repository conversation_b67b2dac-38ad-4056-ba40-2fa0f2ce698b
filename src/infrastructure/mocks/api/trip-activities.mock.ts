import type { TripActivitiesResponse } from '../../api/trip-activities/types';

export const TripActivitiesData: TripActivitiesResponse = {
    pagination: {
        currentPage: 1,
        totalPages: 1,
        pageSize: 20,
        totalCount: 3,
        hasPrevious: false,
        hasNext: false,
    },
    data: [
        {
            id: 1,
            location: { latitude: 24.7136, longitude: 46.6753 },
            note: 'Trip started automatically by system',
            details: 'Trip initiated at origin port',
            createdAt: '2025-09-23T11:07:59.7117172+00:00',
            updatedAt: null,
            address: {
                arabic: '',
                english: '',
            },
            status: { id: 1, name: { arabic: 'مكتمل', english: 'Completed' } },
            action: { id: 1, name: { arabic: 'الاتصال بالشرطة', english: 'Contact Police' } },
            origin: 'System',
        },
        {
            id: 2,
            location: { latitude: 24.75, longitude: 46.7 },
            note: 'Location update received',
            details: 'Vehicle location updated via GPS tracker',
            createdAt: '2025-09-23T12:07:59.7117601+00:00',
            updatedAt: null,
            address: {
                arabic: '',
                english: '',
            },
            status: { id: 1, name: { arabic: 'مكتمل', english: 'Completed' } },
            action: { id: 7, name: { arabic: 'حريق', english: 'Fire' } },
            origin: 'System',
        },
        {
            id: 3,
            location: { latitude: 24.8, longitude: 46.8 },
            note: 'Arrived at destination port',
            details: 'Driver confirmed arrival at destination port',
            createdAt: '2025-09-23T12:37:59.7117605+00:00',
            updatedAt: null,
            address: {
                arabic: '',
                english: '',
            },
            status: { id: 2, name: { arabic: 'قيد التنفيذ', english: 'In Progress' } },
            action: { id: 2, name: { arabic: 'لا يوجد إجراء مطلوب', english: 'No Action Required' } },
            origin: 'User',
        },
    ],
};
