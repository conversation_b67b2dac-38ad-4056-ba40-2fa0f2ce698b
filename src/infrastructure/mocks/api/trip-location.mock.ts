import type { TripLocationsResponse } from '../../api/trip-pings/types';

export const TripLocationsData: TripLocationsResponse = {
    pagination: {
        currentPage: 1,
        totalPages: 2,
        pageSize: 5,
        totalCount: 8,
        hasPrevious: false,
        hasNext: true,
    },
    data: [
        {
            id: 60,
            trackerDateTime: '2025-09-23T13:07:59.688898+00:00',
            serverDateTime: '2025-09-23T13:07:59.6888982+00:00',
            density: 8,
            location: {
                latitude: 26.15,
                longitude: 45.3667,
            },
            address: {
                arabic: 'Mock Address 1',
                english: 'Mock Address 1',
            },
            bearing: 315,
            speed: 65,
            batteryLevelPercentage: 85,
            chargerStatus: 0,
            gpsSignalStrength: 4,
            gsmSignalStrength: 3,
            routeZone: 1,
            isWithinRouteGeofence: true,
            timeElapsedSinceTripStartInMinutes: 45,
            remainingDistanceInMeters: 2500,
            completeDistanceInMeters: 5000,
            isWithinSuspiciousZone: false,
        },
        {
            id: 38,
            trackerDateTime: '2025-09-23T13:07:59.5389346+00:00',
            serverDateTime: '2025-09-23T13:07:59.5389348+00:00',
            density: 9,
            location: {
                latitude: 31.6667,
                longitude: 38.6667,
            },
            address: {
                arabic: 'Mock Address 2',
                english: 'Mock Address 2',
            },
            bearing: 315,
            speed: 50,
            batteryLevelPercentage: 72,
            chargerStatus: 1,
            gpsSignalStrength: 3,
            gsmSignalStrength: 4,
            routeZone: 2,
            isWithinRouteGeofence: true,
            timeElapsedSinceTripStartInMinutes: 38,
            remainingDistanceInMeters: 3200,
            completeDistanceInMeters: 5000,
            isWithinSuspiciousZone: false,
        },
        {
            id: 37,
            trackerDateTime: '2025-09-23T13:07:59.4889343+00:00',
            serverDateTime: '2025-09-23T13:07:59.4889345+00:00',
            density: 10,
            location: {
                latitude: 19.1167,
                longitude: 41.0833,
            },
            address: {
                arabic: 'Mock Address 3',
                english: 'Mock Address 3',
            },
            bearing: 225,
            speed: 0,
            batteryLevelPercentage: 95,
            chargerStatus: 1,
            gpsSignalStrength: 5,
            gsmSignalStrength: 5,
            routeZone: 0,
            isWithinRouteGeofence: false,
            timeElapsedSinceTripStartInMinutes: 25,
            remainingDistanceInMeters: 1800,
            completeDistanceInMeters: 5000,
            isWithinSuspiciousZone: true,
        },
        {
            id: 36,
            trackerDateTime: '2025-09-23T13:07:59.3889337+00:00',
            serverDateTime: '2025-09-23T13:07:59.3889341+00:00',
            density: 8,
            location: {
                latitude: 26.2172,
                longitude: 50.2081,
            },
            address: {
                arabic: 'Mock Address 4',
                english: 'Mock Address 4',
            },
            bearing: 90,
            speed: 70,
            batteryLevelPercentage: 68,
            chargerStatus: 0,
            gpsSignalStrength: 4,
            gsmSignalStrength: 3,
            routeZone: 1,
            isWithinRouteGeofence: true,
            timeElapsedSinceTripStartInMinutes: 52,
            remainingDistanceInMeters: 1200,
            completeDistanceInMeters: 5000,
            isWithinSuspiciousZone: false,
        },
        {
            id: 35,
            trackerDateTime: '2025-09-23T13:07:59.2889252+00:00',
            serverDateTime: '2025-09-23T13:07:59.2889265+00:00',
            density: 10,
            location: {
                latitude: 25.9,
                longitude: 45.3333,
            },
            address: {
                arabic: 'Mock Address 5',
                english: 'Mock Address 5',
            },
            bearing: 135,
            speed: 0,
            batteryLevelPercentage: 90,
            chargerStatus: 1,
            gpsSignalStrength: 3,
            gsmSignalStrength: 2,
            routeZone: 0,
            isWithinRouteGeofence: false,
            timeElapsedSinceTripStartInMinutes: 15,
            remainingDistanceInMeters: 4500,
            completeDistanceInMeters: 5000,
            isWithinSuspiciousZone: false,
        },
    ],
};
