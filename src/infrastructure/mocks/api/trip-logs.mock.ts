// src/modules/trip-logs/mock-data/trip-logs.mock.ts
import type { TripLogsResponse } from '../../api/trip-logs/types';

export const TripLogsData: TripLogsResponse = {
    pagination: {
        currentPage: 1,
        totalPages: 1,
        pageSize: 20,
        totalCount: 3,
        hasPrevious: false,
        hasNext: false,
    },
    data: [
        {
            id: 40,
            log: {
                arabic: 'تم الsurveillance لرحلة 11',
                english: 'Monitoring trip 11 for potential issues',
            },
            action: 1, // Watch
            scope: 2, // TripAlert
            user: {
                id: 1,
                name: 'Dummy User',
            },
            createdAt: '2025-09-21T21:07:59.7181008+00:00',
        },
        {
            id: 23,
            log: {
                arabic: 'تم تشغيل الsurveillance لرحلة 11',
                english: 'Alert monitoring started for trip 11',
            },
            action: 1, // Watch
            scope: 2, // TripAlert
            user: {
                id: 1,
                name: 'Dummy User',
            },
            createdAt: '2025-09-15T00:07:59.718075+00:00',
        },
        {
            id: 15,
            log: {
                arabic: 'تم التحقق من حالة الرحلة 11',
                english: 'Trip 11 status acknowledged',
            },
            action: 2, // Acknowledge
            scope: 1, // Trip
            user: {
                id: 1,
                name: 'Dummy User',
            },
            createdAt: '2025-09-10T06:07:59.7180622+00:00',
        },
    ],
};
