import type { TripsResponse } from '../../api/trips/types';

export const TripsData: TripsResponse = {
    data: [
        // Riyadh group
        {
            id: 1,
            transitNumber: 1,
            status: 1, // TripStatus.Active
            trackingStatus: 1, // TrackingStatus.Started
            vehicle: {
                id: 101,
                plateNo: 'HJA-1001',
                model: 'Toyota Dyna',
                type: 'Truck',
                color: 'White',
                plateCountryName: 'Saudi Arabia',
                plateCountryCode: 'SA',
            },
            driver: {
                id: 201,
                name: '<PERSON>',
                mobileNo: '0501000101',
                passportCountry: 'Saudi Arabia',
                passportNumber: '********',
            },
            routeId: 1001,
            entryPort: { id: 401, type: 1, name: { arabic: 'الرياض - مدخل', english: 'Riyadh Entry' } },
            exitPort: { id: 411, type: 2, name: { arabic: 'الرياض - مخرج', english: 'Riyadh Exit' } },
            startDate: '2025-08-20T06:30:00Z',
            endDate: null,
            tracker: { id: 301, serialNumber: 'TRK-A1001' },
            transitDate: null,
            transSeq: null,
            shipmentDescription: 'Building materials',
            newShipmentDescription: null,
            transitTypeName: 'Domestic',
            ownerDesc: 'Al-Rashid Co',
            activeAlerts: [],
            currentState: {
                trackerDateTime: '2025-08-20T08:00:00Z',
                locationSource: 1,
                batteryLevelPercentage: 88,
                chargerStatus: 1,
                gpsSignalStrength: 4,
                gsmSignalStrength: 4,
                currentSpeed: 60,
                routeZone: 2, // OnRoute
                isWithinRouteGeofence: true,
                timeElapsedSinceTripStartInMinutes: 90,
                remainingDistanceInMeters: 45000,
                completeDistanceInMeters: 50000,
                isWithinSuspiciousZone: false,
                createdAt: '2025-08-20T08:00:00Z',
                lat: 26.406,
                long: 50.07,
                address: {
                    arabic: 'King Abdulaziz Port checkpoint',
                    english: 'King Abdulaziz Port checkpoint',
                },
            },
            eLocks: [
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1001',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1002',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1003',
                },
            ],
            securityNotes: null,
            isFocused: true,
            isSuspicious: true,
        },
        {
            id: 2,
            transitNumber: 2,
            status: 1,
            trackingStatus: 1,
            vehicle: {
                id: 102,
                plateNo: 'HJB-1002',
                model: 'Isuzu ELF',
                type: 'Truck',
                color: 'Blue',
                plateCountryName: 'Saudi Arabia',
                plateCountryCode: 'SA',
            },
            driver: {
                id: 202,
                name: 'Ali Al-Obeid',
                mobileNo: '0501000102',
                passportCountry: 'Saudi Arabia',
                passportNumber: '********',
            },
            routeId: 1002,
            entryPort: { id: 402, type: 3, name: { arabic: 'الرياض - مدخل', english: 'Riyadh Entry' } },
            exitPort: { id: 412, type: 1, name: { arabic: 'الرياض - مخرج', english: 'Riyadh Exit' } },
            startDate: '2025-08-20T05:00:00Z',
            endDate: null,
            tracker: { id: 302, serialNumber: 'TRK-A1002' },
            transitDate: null,
            transSeq: null,
            shipmentDescription: 'Food containers',
            newShipmentDescription: null,
            transitTypeName: 'Domestic',
            ownerDesc: 'Al-Saleh Trading',
            activeAlerts: [],
            currentState: {
                trackerDateTime: '2025-08-20T07:40:00Z',
                locationSource: 1,
                batteryLevelPercentage: 65,
                chargerStatus: 1,
                gpsSignalStrength: 4,
                gsmSignalStrength: 3,
                currentSpeed: 55,
                routeZone: 2,
                isWithinRouteGeofence: true,
                timeElapsedSinceTripStartInMinutes: 160,
                remainingDistanceInMeters: 80000,
                completeDistanceInMeters: 100000,
                isWithinSuspiciousZone: false,
                createdAt: '2025-08-20T07:40:00Z',
                lat: 26.406,
                long: 50.07,
                address: {
                    arabic: 'King Abdulaziz Port checkpoint',
                    english: 'King Abdulaziz Port checkpoint',
                },
            },
            eLocks: [
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1003',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1004',
                },
            ],
            securityNotes: null,
            isFocused: true,
            isSuspicious: true,
        },
        {
            id: 3,
            transitNumber: 3,
            status: 1,
            trackingStatus: 1,
            vehicle: {
                id: 103,
                plateNo: 'HJC-1003',
                model: 'Mitsubishi Fuso',
                type: 'Truck',
                color: 'Green',
                plateCountryName: 'Saudi Arabia',
                plateCountryCode: 'SA',
            },
            driver: {
                id: 203,
                name: 'Khaled Al-Harbi',
                mobileNo: '0501000103',
                passportCountry: 'Saudi Arabia',
                passportNumber: '********',
            },
            routeId: 1003,
            entryPort: { id: 403, type: 2, name: { arabic: 'الرياض - مدخل', english: 'Riyadh Entry' } },
            exitPort: { id: 413, type: 1, name: { arabic: 'الرياض - مخرج', english: 'Riyadh Exit' } },
            startDate: '2025-08-20T04:00:00Z',
            endDate: null,
            tracker: { id: 303, serialNumber: 'TRK-A1003' },
            transitDate: null,
            transSeq: null,
            shipmentDescription: 'Textiles',
            newShipmentDescription: null,
            transitTypeName: 'Domestic',
            ownerDesc: 'Al-Madina Logistics',
            activeAlerts: [],
            currentState: {
                trackerDateTime: '2025-08-20T06:30:00Z',
                locationSource: 1,
                batteryLevelPercentage: 72,
                chargerStatus: 1,
                gpsSignalStrength: 4,
                gsmSignalStrength: 4,
                currentSpeed: 40,
                routeZone: 2,
                isWithinRouteGeofence: true,
                timeElapsedSinceTripStartInMinutes: 180,
                remainingDistanceInMeters: 120000,
                completeDistanceInMeters: 120000,
                isWithinSuspiciousZone: false,
                createdAt: '2025-08-20T06:30:00Z',
                lat: 26.406,
                long: 50.07,
                address: {
                    arabic: 'King Abdulaziz Port checkpoint',
                    english: 'King Abdulaziz Port checkpoint',
                },
            },
            eLocks: [
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1001',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1002',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1003',
                },
            ],
            securityNotes: null,
            isFocused: true,
            isSuspicious: true,
        },

        // Jeddah group
        {
            id: 4,
            transitNumber: 4,
            status: 1,
            trackingStatus: 1,
            vehicle: {
                id: 104,
                plateNo: 'JKD-1004',
                model: 'Volvo FM',
                type: 'Truck',
                color: 'Grey',
                plateCountryName: 'Saudi Arabia',
                plateCountryCode: 'SA',
            },
            driver: {
                id: 204,
                name: 'Abdullah Al-Yami',
                mobileNo: '0502000104',
                passportCountry: 'Saudi Arabia',
                passportNumber: '********',
            },
            routeId: 2001,
            entryPort: { id: 404, type: 2, name: { arabic: 'جدة - مدخل', english: 'Jeddah Entry' } },
            exitPort: { id: 414, type: 3, name: { arabic: 'جدة - مخرج', english: 'Jeddah Exit' } },
            startDate: '2025-08-19T22:30:00Z',
            endDate: null,
            tracker: { id: 304, serialNumber: 'TRK-J1004' },
            transitDate: null,
            transSeq: null,
            shipmentDescription: 'Electronics',
            newShipmentDescription: null,
            transitTypeName: 'Export',
            ownerDesc: 'Red Sea Exports',
            activeAlerts: [
                {
                    id: 10000,
                    type: {
                        id: 10000,
                        name: { arabic: 'البطارية ضعيفة', english: 'Battery Low' },
                    },
                },
                {
                    id: 10000,
                    type: {
                        id: 10001,
                        name: { arabic: 'البطارية ضعيفة', english: 'Battery Low' },
                    },
                },
                {
                    id: 10002,
                    type: {
                        id: 10003,
                        name: { arabic: 'البطارية ضعيفة', english: 'Battery Low' },
                    },
                },
                {
                    id: 10000,
                    type: {
                        id: 10004,
                        name: { arabic: 'البطارية ضعيفة', english: 'Battery Low' },
                    },
                },
                {
                    id: 10000,
                    type: {
                        id: 10005,
                        name: { arabic: 'البطارية ضعيفة', english: 'Battery Low' },
                    },
                },
                {
                    id: 10000,
                    type: {
                        id: 10006,
                        name: { arabic: 'البطارية ضعيفة', english: 'Battery Low' },
                    },
                },
            ],
            currentState: {
                trackerDateTime: '2025-08-20T00:10:00Z',
                locationSource: 1,
                batteryLevelPercentage: 55,
                chargerStatus: 1,
                gpsSignalStrength: 3,
                gsmSignalStrength: 3,
                currentSpeed: 0,
                routeZone: 3, // Exit
                isWithinRouteGeofence: true,
                timeElapsedSinceTripStartInMinutes: 10,
                remainingDistanceInMeters: 5000,
                completeDistanceInMeters: 5000,
                isWithinSuspiciousZone: false,
                createdAt: '2025-08-20T00:10:00Z',
                lat: 26.406,
                long: 50.07,
                address: {
                    arabic: 'King Abdulaziz Port checkpoint',
                    english: 'King Abdulaziz Port checkpoint',
                },
            },
            eLocks: [
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1001',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1002',
                },
            ],
            securityNotes: null,
            isFocused: true,
            isSuspicious: true,
        },
        {
            id: 5,
            transitNumber: 5,
            status: 2, // Ended
            trackingStatus: 2, // Ended
            vehicle: {
                id: 105,
                plateNo: 'JED-1005',
                model: 'Hino 500',
                type: 'Truck',
                color: 'White',
                plateCountryName: 'Saudi Arabia',
                plateCountryCode: 'SA',
            },
            driver: {
                id: 205,
                name: 'Saeed Al-Ghamdi',
                mobileNo: '0502000105',
                passportCountry: 'Saudi Arabia',
                passportNumber: '********',
            },
            routeId: 2002,
            entryPort: { id: 405, type: 1, name: { arabic: 'جازان - مدخل', english: 'Jazan Entry' } },
            exitPort: { id: 415, type: 1, name: { arabic: 'جازان - مخرج', english: 'Jazan Exit' } },
            startDate: '2025-08-15T08:00:00Z',
            endDate: '2025-08-16T10:00:00Z',
            tracker: { id: 305, serialNumber: 'TRK-J1005' },
            transitDate: '2025-08-16',
            transSeq: 'S-505',
            shipmentDescription: 'Fresh produce',
            newShipmentDescription: null,
            transitTypeName: 'Domestic',
            ownerDesc: 'Green Farms',
            activeAlerts: [
                {
                    id: 10000,
                    type: {
                        id: 10000,
                        name: { arabic: 'البطارية ضعيفة', english: 'Battery Low' },
                    },
                },
            ],
            currentState: {
                trackerDateTime: '2025-08-16T10:00:00Z',
                locationSource: 1,
                batteryLevelPercentage: 45,
                chargerStatus: 1,
                gpsSignalStrength: 4,
                gsmSignalStrength: 3,
                currentSpeed: 0,
                routeZone: 3,
                isWithinRouteGeofence: true,
                timeElapsedSinceTripStartInMinutes: 0,
                remainingDistanceInMeters: 0,
                completeDistanceInMeters: 0,
                isWithinSuspiciousZone: false,
                createdAt: '2025-08-16T10:00:00Z',
                lat: 26.406,
                long: 50.07,
                address: {
                    arabic: 'King Abdulaziz Port checkpoint',
                    english: 'King Abdulaziz Port checkpoint',
                },
            },
            eLocks: [
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1001',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1002',
                },
            ],
            securityNotes: null,
            isFocused: true,
            isSuspicious: true,
        },
        {
            id: 6,
            transitNumber: 6,
            status: 1,
            trackingStatus: 1,
            vehicle: {
                id: 106,
                plateNo: 'JED-1006',
                model: 'Scania R-series',
                type: 'Truck',
                color: 'Blue',
                plateCountryName: 'Saudi Arabia',
                plateCountryCode: 'SA',
            },
            driver: {
                id: 206,
                name: 'Nasser Al-Shammari',
                mobileNo: '0502000106',
                passportCountry: 'Saudi Arabia',
                passportNumber: '********',
            },
            routeId: 2003,
            entryPort: { id: 406, type: 1, name: { arabic: 'جدة - مدخل', english: 'Jeddah Entry' } },
            exitPort: { id: 416, type: 3, name: { arabic: 'جدة - مخرج', english: 'Jeddah Exit' } },
            startDate: '2025-08-18T03:00:00Z',
            endDate: null,
            tracker: { id: 306, serialNumber: 'TRK-J1006' },
            transitDate: null,
            transSeq: null,
            shipmentDescription: 'Automotive parts',
            newShipmentDescription: null,
            transitTypeName: 'Import',
            ownerDesc: 'Gulf Parts',
            activeAlerts: [
                {
                    id: 10001,
                    type: {
                        id: 10001,
                        name: { arabic: 'البطارية ضعيفة', english: 'Battery Low' },
                    },
                },
            ],
            currentState: {
                trackerDateTime: '2025-08-18T05:30:00Z',
                locationSource: 1,
                batteryLevelPercentage: 68,
                chargerStatus: 1,
                gpsSignalStrength: 4,
                gsmSignalStrength: 4,
                currentSpeed: 70,
                routeZone: 2,
                isWithinRouteGeofence: true,
                timeElapsedSinceTripStartInMinutes: 220,
                remainingDistanceInMeters: 300000,
                completeDistanceInMeters: 300000,
                isWithinSuspiciousZone: false,
                createdAt: '2025-08-18T05:30:00Z',
                lat: 26.406,
                long: 50.07,
                address: {
                    arabic: 'King Abdulaziz Port checkpoint',
                    english: 'King Abdulaziz Port checkpoint',
                },
            },
            eLocks: [
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1001',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1004',
                },
            ],
            securityNotes: null,
            isFocused: true,
            isSuspicious: true,
        },

        // Makkah group
        {
            id: 7,
            transitNumber: 7,
            status: 1,
            trackingStatus: 1,
            vehicle: {
                id: 107,
                plateNo: 'MAK-1007',
                model: 'Mercedes Atego',
                type: 'Truck',
                color: 'Red',
                plateCountryName: 'Saudi Arabia',
                plateCountryCode: 'SA',
            },
            driver: {
                id: 207,
                name: 'Yousef Al-Qahtani',
                mobileNo: '0503000107',
                passportCountry: 'Saudi Arabia',
                passportNumber: '********',
            },
            routeId: 3001,
            entryPort: { id: 407, type: 1, name: { arabic: 'مكة - مدخل', english: 'Makkah Entry' } },
            exitPort: { id: 417, type: 1, name: { arabic: 'مكة - مخرج', english: 'Makkah Exit' } },
            startDate: '2025-08-20T03:30:00Z',
            endDate: null,
            tracker: { id: 307, serialNumber: 'TRK-M1007' },
            transitDate: null,
            transSeq: null,
            shipmentDescription: 'Ceramics',
            newShipmentDescription: null,
            transitTypeName: 'Domestic',
            ownerDesc: 'Al-Hijaz Traders',
            activeAlerts: [
                {
                    id: 10002,
                    type: {
                        id: 10002,
                        name: { arabic: 'البطارية ضعيفة', english: 'Battery Low' },
                    },
                },
            ],
            currentState: {
                trackerDateTime: '2025-08-20T05:10:00Z',
                locationSource: 1,
                batteryLevelPercentage: 62,
                chargerStatus: 1,
                gpsSignalStrength: 4,
                gsmSignalStrength: 3,
                currentSpeed: 50,
                routeZone: 2,
                isWithinRouteGeofence: true,
                timeElapsedSinceTripStartInMinutes: 110,
                remainingDistanceInMeters: 60000,
                completeDistanceInMeters: 60000,
                isWithinSuspiciousZone: false,
                createdAt: '2025-08-20T05:10:00Z',
                lat: 26.406,
                long: 50.07,
                address: {
                    arabic: 'King Abdulaziz Port checkpoint',
                    english: 'King Abdulaziz Port checkpoint',
                },
            },
            eLocks: [
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1001',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1002',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1003',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1004',
                },
            ],
            securityNotes: null,
            isFocused: true,
            isSuspicious: true,
        },
        {
            id: 8,
            transitNumber: 8,
            status: 1,
            trackingStatus: 1,
            vehicle: {
                id: 108,
                plateNo: 'MAK-1008',
                model: 'Isuzu Giga',
                type: 'Truck',
                color: 'Green',
                plateCountryName: 'Saudi Arabia',
                plateCountryCode: 'SA',
            },
            driver: {
                id: 208,
                name: 'Omar Al-Mutairi',
                mobileNo: '0503000108',
                passportCountry: 'Saudi Arabia',
                passportNumber: '********',
            },
            routeId: 3002,
            entryPort: { id: 408, type: 1, name: { arabic: 'مكة - مدخل', english: 'Makkah Entry' } },
            exitPort: { id: 418, type: 1, name: { arabic: 'مكة - مخرج', english: 'Makkah Exit' } },
            startDate: '2025-08-19T23:00:00Z',
            endDate: null,
            tracker: { id: 308, serialNumber: 'TRK-M1008' },
            transitDate: null,
            transSeq: null,
            shipmentDescription: 'Construction tools',
            newShipmentDescription: null,
            transitTypeName: 'Domestic',
            ownerDesc: 'Hira Supply',
            activeAlerts: [
                {
                    id: 10003,
                    type: {
                        id: 10003,
                        name: { arabic: 'البطارية ضعيفة', english: 'Battery Low' },
                    },
                },
            ],
            currentState: {
                trackerDateTime: '2025-08-19T23:50:00Z',
                locationSource: 1,
                batteryLevelPercentage: 58,
                chargerStatus: 1,
                gpsSignalStrength: 3,
                gsmSignalStrength: 3,
                currentSpeed: 20,
                routeZone: 3,
                isWithinRouteGeofence: true,
                timeElapsedSinceTripStartInMinutes: 15,
                remainingDistanceInMeters: 8000,
                completeDistanceInMeters: 8000,
                isWithinSuspiciousZone: false,
                createdAt: '2025-08-19T23:50:00Z',
                lat: 26.406,
                long: 50.07,
                address: {
                    arabic: 'King Abdulaziz Port checkpoint',
                    english: 'King Abdulaziz Port checkpoint',
                },
            },
            eLocks: [
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1001',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1002',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1003',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1004',
                },
            ],
            securityNotes: null,
            isFocused: true,
            isSuspicious: true,
        },

        // Madinah group
        {
            id: 9,
            transitNumber: 9,
            status: 1,
            trackingStatus: 1,
            vehicle: {
                id: 109,
                plateNo: 'MAD-1009',
                model: 'Hino 700',
                type: 'Truck',
                color: 'Grey',
                plateCountryName: 'Saudi Arabia',
                plateCountryCode: 'SA',
            },
            driver: {
                id: 209,
                name: 'Fahad Al-Shehri',
                mobileNo: '0504000109',
                passportCountry: 'Saudi Arabia',
                passportNumber: '********',
            },
            routeId: 4001,
            entryPort: { id: 409, type: 2, name: { arabic: 'المدينة - مدخل', english: 'Madinah Entry' } },
            exitPort: { id: 419, type: 1, name: { arabic: 'المدينة - مخرج', english: 'Madinah Exit' } },
            startDate: '2025-08-18T21:00:00Z',
            endDate: null,
            tracker: { id: 309, serialNumber: 'TRK-MD1009' },
            transitDate: null,
            transSeq: null,
            shipmentDescription: 'Pharmaceuticals',
            newShipmentDescription: null,
            transitTypeName: 'Domestic',
            ownerDesc: 'Med Supply',
            activeAlerts: [
                {
                    id: 10005,
                    type: {
                        id: 10005,
                        name: { arabic: 'البطارية ضعيفة', english: 'Battery Low' },
                    },
                },
            ],
            currentState: {
                trackerDateTime: '2025-08-18T22:40:00Z',
                locationSource: 1,
                batteryLevelPercentage: 80,
                chargerStatus: 1,
                gpsSignalStrength: 4,
                gsmSignalStrength: 4,
                currentSpeed: 65,
                routeZone: 2,
                isWithinRouteGeofence: true,
                timeElapsedSinceTripStartInMinutes: 260,
                remainingDistanceInMeters: 150000,
                completeDistanceInMeters: 150000,
                isWithinSuspiciousZone: false,
                createdAt: '2025-08-18T22:40:00Z',
                lat: 26.406,
                long: 50.07,
                address: {
                    arabic: 'King Abdulaziz Port checkpoint',
                    english: 'King Abdulaziz Port checkpoint',
                },
            },
            eLocks: [
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1001',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1002',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1003',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1004',
                },
            ],
            securityNotes: null,
            isFocused: true,
            isSuspicious: true,
        },
        {
            id: 10,
            transitNumber: 10,
            status: 2,
            trackingStatus: 2,
            vehicle: {
                id: 110,
                plateNo: 'MAD-1010',
                model: 'Volvo FH',
                type: 'Truck',
                color: 'White',
                plateCountryName: 'Saudi Arabia',
                plateCountryCode: 'SA',
            },
            driver: {
                id: 210,
                name: 'Mahmoud Al-Zahrani',
                mobileNo: '0504000110',
                passportCountry: 'Saudi Arabia',
                passportNumber: '********',
            },
            routeId: 4002,
            entryPort: { id: 410, type: 3, name: { arabic: 'المدينة - مدخل', english: 'Madinah Entry' } },
            exitPort: { id: 420, type: 1, name: { arabic: 'المدينة - مخرج', english: 'Madinah Exit' } },
            startDate: '2025-08-17T18:00:00Z',
            endDate: '2025-08-18T06:30:00Z',
            tracker: { id: 310, serialNumber: 'TRK-MD1010' },
            transitDate: '2025-08-18',
            transSeq: 'S-1010',
            shipmentDescription: 'Textile rolls',
            newShipmentDescription: null,
            transitTypeName: 'Domestic',
            ownerDesc: 'Northern Traders',
            activeAlerts: [
                {
                    id: 1,
                    type: {
                        id: 10006,
                        name: { arabic: 'البطارية ضعيفة', english: 'Battery Low' },
                    },
                },
            ],
            currentState: {
                trackerDateTime: '2025-08-18T06:30:00Z',
                locationSource: 1,
                batteryLevelPercentage: 50,
                chargerStatus: 1,
                gpsSignalStrength: 4,
                gsmSignalStrength: 3,
                currentSpeed: 0,
                routeZone: 3,
                isWithinRouteGeofence: true,
                timeElapsedSinceTripStartInMinutes: 0,
                remainingDistanceInMeters: 0,
                completeDistanceInMeters: 0,
                isWithinSuspiciousZone: false,
                createdAt: '2025-08-18T06:30:00Z',
                lat: 26.406,
                long: 50.07,
                address: {
                    arabic: 'King Abdulaziz Port checkpoint',
                    english: 'King Abdulaziz Port checkpoint',
                },
            },
            eLocks: [
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1001',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1002',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1003',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1004',
                },
            ],
            securityNotes: null,
            isFocused: true,
            isSuspicious: true,
        },

        // Dammam / Khobar / Jubail group
        {
            id: 11,
            transitNumber: 11,
            status: 1,
            trackingStatus: 1,
            vehicle: {
                id: 111,
                plateNo: 'DMM-1011',
                model: 'MAN TGX',
                type: 'Truck',
                color: 'Black',
                plateCountryName: 'Saudi Arabia',
                plateCountryCode: 'SA',
            },
            driver: {
                id: 211,
                name: 'Riyadh Al-Mutlaq',
                mobileNo: '0505000111',
                passportCountry: 'Saudi Arabia',
                passportNumber: '********',
            },
            routeId: 5001,
            entryPort: { id: 411, type: 2, name: { arabic: 'الدمام - مدخل', english: 'Dammam Entry' } },
            exitPort: { id: 421, type: 3, name: { arabic: 'الخبر - مخرج', english: 'Al Khobar Exit' } },
            startDate: '2025-08-20T02:00:00Z',
            endDate: null,
            tracker: { id: 311, serialNumber: 'TRK-D1011' },
            transitDate: null,
            transSeq: null,
            shipmentDescription: 'Steel pipes',
            newShipmentDescription: null,
            transitTypeName: 'Industrial',
            ownerDesc: 'GCC Steel',
            activeAlerts: [
                {
                    id: 1,
                    type: {
                        id: 10008,
                        name: { arabic: 'البطارية ضعيفة', english: 'Battery Low' },
                    },
                },
            ],
            currentState: {
                trackerDateTime: '2025-08-20T03:40:00Z',
                locationSource: 1,
                batteryLevelPercentage: 83,
                chargerStatus: 1,
                gpsSignalStrength: 4,
                gsmSignalStrength: 4,
                currentSpeed: 75,
                routeZone: 2,
                isWithinRouteGeofence: true,
                timeElapsedSinceTripStartInMinutes: 40,
                remainingDistanceInMeters: 90000,
                completeDistanceInMeters: 90000,
                isWithinSuspiciousZone: false,
                createdAt: '2025-08-20T03:40:00Z',
                lat: 26.406,
                long: 50.07,
                address: {
                    arabic: 'King Abdulaziz Port checkpoint',
                    english: 'King Abdulaziz Port checkpoint',
                },
            },
            eLocks: [
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1001',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1002',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1003',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1004',
                },
            ],
            securityNotes: null,
            isFocused: true,
            isSuspicious: true,
        },
        {
            id: 12,
            transitNumber: 12,
            status: 1,
            trackingStatus: 1,
            vehicle: {
                id: 112,
                plateNo: 'KHB-1012',
                model: 'FAW J6',
                type: 'Truck',
                color: 'Blue',
                plateCountryName: 'Saudi Arabia',
                plateCountryCode: 'SA',
            },
            driver: {
                id: 212,
                name: 'Sultan Al-Harbi',
                mobileNo: '0505000112',
                passportCountry: 'Saudi Arabia',
                passportNumber: '********',
            },
            routeId: 5002,
            entryPort: { id: 412, type: 3, name: { arabic: 'الخبر - مدخل', english: 'Khobar Entry' } },
            exitPort: { id: 422, type: 1, name: { arabic: 'جبيل - مخرج', english: 'Jubail Exit' } },
            startDate: '2025-08-19T12:00:00Z',
            endDate: null,
            tracker: { id: 312, serialNumber: 'TRK-K1012' },
            transitDate: null,
            transSeq: null,
            shipmentDescription: 'Chemicals (packed)',
            newShipmentDescription: null,
            transitTypeName: 'Industrial',
            ownerDesc: 'Eastern Chemicals',
            activeAlerts: [
                {
                    id: 1,
                    type: {
                        id: 10009,
                        name: { arabic: 'البطارية ضعيفة', english: 'Battery Low' },
                    },
                },
            ],
            currentState: {
                trackerDateTime: '2025-08-19T13:30:00Z',
                locationSource: 1,
                batteryLevelPercentage: 60,
                chargerStatus: 1,
                gpsSignalStrength: 3,
                gsmSignalStrength: 3,
                currentSpeed: 60,
                routeZone: 2,
                isWithinRouteGeofence: true,
                timeElapsedSinceTripStartInMinutes: 120,
                remainingDistanceInMeters: 70000,
                completeDistanceInMeters: 70000,
                isWithinSuspiciousZone: false,
                createdAt: '2025-08-19T13:30:00Z',
                lat: 26.406,
                long: 50.07,
                address: {
                    arabic: 'King Abdulaziz Port checkpoint',
                    english: 'King Abdulaziz Port checkpoint',
                },
            },
            eLocks: [
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1001',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1002',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1003',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1004',
                },
            ],
            securityNotes: null,
            isFocused: true,
            isSuspicious: true,
        },

        // Taif / Abha / Jazan / Najran
        {
            id: 13,
            transitNumber: 13,
            status: 1,
            trackingStatus: 1,
            vehicle: {
                id: 113,
                plateNo: 'TAF-1013',
                model: 'Mitsubishi Canter',
                type: 'Truck',
                color: 'White',
                plateCountryName: 'Saudi Arabia',
                plateCountryCode: 'SA',
            },
            driver: {
                id: 213,
                name: 'Hamad Al-Qahtani',
                mobileNo: '0506000113',
                passportCountry: 'Saudi Arabia',
                passportNumber: '********',
            },
            routeId: 6001,
            entryPort: { id: 413, type: 1, name: { arabic: 'الطائف - مدخل', english: 'Taif Entry' } },
            exitPort: { id: 423, type: 2, name: { arabic: 'الطائف - مخرج', english: 'Taif Exit' } },
            startDate: '2025-08-19T02:00:00Z',
            endDate: null,
            tracker: { id: 313, serialNumber: 'TRK-T1013' },
            transitDate: null,
            transSeq: null,
            shipmentDescription: 'Furniture',
            newShipmentDescription: null,
            transitTypeName: 'Domestic',
            ownerDesc: 'Al-Taif Movers',
            activeAlerts: [
                {
                    id: 1,
                    type: {
                        id: 10005,
                        name: { arabic: 'البطارية ضعيفة', english: 'Battery Low' },
                    },
                },
            ],
            currentState: {
                trackerDateTime: '2025-08-19T03:20:00Z',
                locationSource: 1,
                batteryLevelPercentage: 77,
                chargerStatus: 1,
                gpsSignalStrength: 4,
                gsmSignalStrength: 4,
                currentSpeed: 50,
                routeZone: 2,
                isWithinRouteGeofence: true,
                timeElapsedSinceTripStartInMinutes: 90,
                remainingDistanceInMeters: 55000,
                completeDistanceInMeters: 55000,
                isWithinSuspiciousZone: false,
                createdAt: '2025-08-19T03:20:00Z',
                lat: 26.406,
                long: 50.07,
                address: {
                    arabic: 'King Abdulaziz Port checkpoint',
                    english: 'King Abdulaziz Port checkpoint',
                },
            },
            eLocks: [
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1001',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1002',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1003',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1004',
                },
            ],
            securityNotes: null,
            isFocused: true,
            isSuspicious: true,
        },
        {
            id: 14,
            transitNumber: 14,
            status: 1,
            trackingStatus: 1,
            vehicle: {
                id: 114,
                plateNo: 'ABH-1014',
                model: 'Volvo FMX',
                type: 'Truck',
                color: 'Black',
                plateCountryName: 'Saudi Arabia',
                plateCountryCode: 'SA',
            },
            driver: {
                id: 214,
                name: 'Ibrahim Al-Harbi',
                mobileNo: '0506000114',
                passportCountry: 'Saudi Arabia',
                passportNumber: '********',
            },
            routeId: 6002,
            entryPort: { id: 414, type: 2, name: { arabic: 'أبها - مدخل', english: 'Abha Entry' } },
            exitPort: { id: 424, type: 1, name: { arabic: 'أبها - مخرج', english: 'Abha Exit' } },
            startDate: '2025-08-18T10:00:00Z',
            endDate: null,
            tracker: { id: 314, serialNumber: 'TRK-AH1014' },
            transitDate: null,
            transSeq: null,
            shipmentDescription: 'Canned food',
            newShipmentDescription: null,
            transitTypeName: 'Domestic',
            ownerDesc: 'Southern Foods',
            activeAlerts: [
                {
                    id: 1,
                    type: {
                        id: 10005,
                        name: { arabic: 'البطارية ضعيفة', english: 'Battery Low' },
                    },
                },
            ],
            currentState: {
                trackerDateTime: '2025-08-18T11:45:00Z',
                locationSource: 1,
                batteryLevelPercentage: 66,
                chargerStatus: 1,
                gpsSignalStrength: 3,
                gsmSignalStrength: 3,
                currentSpeed: 30,
                routeZone: 2,
                isWithinRouteGeofence: true,
                timeElapsedSinceTripStartInMinutes: 45,
                remainingDistanceInMeters: 25000,
                completeDistanceInMeters: 25000,
                isWithinSuspiciousZone: false,
                createdAt: '2025-08-18T11:45:00Z',
                lat: 26.406,
                long: 50.07,
                address: {
                    arabic: 'King Abdulaziz Port checkpoint',
                    english: 'King Abdulaziz Port checkpoint',
                },
            },
            eLocks: [
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1001',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1002',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1003',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1004',
                },
            ],
            securityNotes: null,
            isFocused: true,
            isSuspicious: true,
        },
        {
            id: 15,
            transitNumber: 15,
            status: 1,
            trackingStatus: 1,
            vehicle: {
                id: 115,
                plateNo: 'JAZ-1015',
                model: 'Isuzu NQR',
                type: 'Truck',
                color: 'Blue',
                plateCountryName: 'Saudi Arabia',
                plateCountryCode: 'SA',
            },
            driver: {
                id: 215,
                name: 'Talal Al-Muqbil',
                mobileNo: '0507000115',
                passportCountry: 'Saudi Arabia',
                passportNumber: '********',
            },
            routeId: 7001,
            entryPort: { id: 415, type: 1, name: { arabic: 'جازان - مدخل', english: 'Jazan Entry' } },
            exitPort: { id: 425, type: 3, name: { arabic: 'جازان - مخرج', english: 'Jazan Exit' } },
            startDate: '2025-08-17T05:00:00Z',
            endDate: null,
            tracker: { id: 315, serialNumber: 'TRK-JZ1015' },
            transitDate: null,
            transSeq: null,
            shipmentDescription: 'Local produce',
            newShipmentDescription: null,
            transitTypeName: 'Domestic',
            ownerDesc: 'Red Coast Farms',
            activeAlerts: [
                {
                    id: 1,
                    type: {
                        id: 10005,
                        name: { arabic: 'البطارية ضعيفة', english: 'Battery Low' },
                    },
                },
            ],
            currentState: {
                trackerDateTime: '2025-08-17T06:20:00Z',
                locationSource: 1,
                batteryLevelPercentage: 71,
                chargerStatus: 1,
                gpsSignalStrength: 4,
                gsmSignalStrength: 4,
                currentSpeed: 35,
                routeZone: 2,
                isWithinRouteGeofence: true,
                timeElapsedSinceTripStartInMinutes: 120,
                remainingDistanceInMeters: 48000,
                completeDistanceInMeters: 48000,
                isWithinSuspiciousZone: false,
                createdAt: '2025-08-17T06:20:00Z',
                lat: 26.406,
                long: 50.07,
                address: {
                    arabic: 'King Abdulaziz Port checkpoint',
                    english: 'King Abdulaziz Port checkpoint',
                },
            },
            eLocks: [
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1001',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1002',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1003',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1004',
                },
            ],
            securityNotes: null,
            isFocused: true,
            isSuspicious: true,
        },

        // Tabuk / Northern
        {
            id: 16,
            transitNumber: 16,
            status: 1,
            trackingStatus: 1,
            vehicle: {
                id: 116,
                plateNo: 'TAB-1016',
                model: 'Iveco Stralis',
                type: 'Truck',
                color: 'White',
                plateCountryName: 'Saudi Arabia',
                plateCountryCode: 'SA',
            },
            driver: {
                id: 216,
                name: 'Faisal Al-Anazi',
                mobileNo: '0508000116',
                passportCountry: 'Saudi Arabia',
                passportNumber: '********',
            },
            routeId: 8001,
            entryPort: { id: 416, type: 1, name: { arabic: 'تبوك - مدخل', english: 'Tabuk Entry' } },
            exitPort: { id: 426, type: 1, name: { arabic: 'تبوك - مخرج', english: 'Tabuk Exit' } },
            startDate: '2025-08-16T01:00:00Z',
            endDate: null,
            tracker: { id: 316, serialNumber: 'TRK-TB1016' },
            transitDate: null,
            transSeq: null,
            shipmentDescription: 'Spare parts',
            newShipmentDescription: null,
            transitTypeName: 'Domestic',
            ownerDesc: 'Northern Parts',
            activeAlerts: [
                {
                    id: 1,
                    type: {
                        id: 10005,
                        name: { arabic: 'البطارية ضعيفة', english: 'Battery Low' },
                    },
                },
            ],
            currentState: {
                trackerDateTime: '2025-08-16T02:40:00Z',
                locationSource: 1,
                batteryLevelPercentage: 69,
                chargerStatus: 1,
                gpsSignalStrength: 4,
                gsmSignalStrength: 4,
                currentSpeed: 85,
                routeZone: 2,
                isWithinRouteGeofence: true,
                timeElapsedSinceTripStartInMinutes: 40,
                remainingDistanceInMeters: 220000,
                completeDistanceInMeters: 220000,
                isWithinSuspiciousZone: false,
                createdAt: '2025-08-16T02:40:00Z',
                lat: 26.406,
                long: 50.07,
                address: {
                    arabic: 'King Abdulaziz Port checkpoint',
                    english: 'King Abdulaziz Port checkpoint',
                },
            },
            eLocks: [
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1001',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1002',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1003',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1004',
                },
            ],
            securityNotes: null,
            isFocused: true,
            isSuspicious: true,
        },
        {
            id: 17,
            transitNumber: 17,
            status: 1,
            trackingStatus: 1,
            vehicle: {
                id: 117,
                plateNo: 'ARR-1017',
                model: 'DAF XF',
                type: 'Truck',
                color: 'Silver',
                plateCountryName: 'Saudi Arabia',
                plateCountryCode: 'SA',
            },
            driver: {
                id: 217,
                name: 'Abdulrahman Al-Otaibi',
                mobileNo: '0508000117',
                passportCountry: 'Saudi Arabia',
                passportNumber: '********',
            },
            routeId: 8002,
            entryPort: { id: 417, type: 1, name: { arabic: 'عرعر - مدخل', english: 'Arar Entry' } },
            exitPort: { id: 427, type: 1, name: { arabic: 'عرعر - مخرج', english: 'Arar Exit' } },
            startDate: '2025-08-15T07:00:00Z',
            endDate: null,
            tracker: { id: 317, serialNumber: 'TRK-AR1017' },
            transitDate: null,
            transSeq: null,
            shipmentDescription: 'Electronics boxes',
            newShipmentDescription: null,
            transitTypeName: 'Domestic',
            ownerDesc: 'Border Logistics',
            activeAlerts: [
                {
                    id: 1,
                    type: {
                        id: 10005,
                        name: { arabic: 'البطارية ضعيفة', english: 'Battery Low' },
                    },
                },
            ],
            currentState: {
                trackerDateTime: '2025-08-15T08:30:00Z',
                locationSource: 1,
                batteryLevelPercentage: 56,
                chargerStatus: 1,
                gpsSignalStrength: 3,
                gsmSignalStrength: 3,
                currentSpeed: 60,
                routeZone: 2,
                isWithinRouteGeofence: true,
                timeElapsedSinceTripStartInMinutes: 70,
                remainingDistanceInMeters: 180000,
                completeDistanceInMeters: 180000,
                isWithinSuspiciousZone: false,
                createdAt: '2025-08-15T08:30:00Z',
                lat: 26.406,
                long: 50.07,
                address: {
                    arabic: 'King Abdulaziz Port checkpoint',
                    english: 'King Abdulaziz Port checkpoint',
                },
            },
            eLocks: [
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1001',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1002',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1003',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1004',
                },
            ],
            securityNotes: null,
            isFocused: true,
            isSuspicious: true,
        },

        // Qassim / Buraidah
        {
            id: 18,
            transitNumber: 18,
            status: 1,
            trackingStatus: 1,
            vehicle: {
                id: 118,
                plateNo: 'QSM-1018',
                model: 'Mercedes Actros',
                type: 'Truck',
                color: 'White',
                plateCountryName: 'Saudi Arabia',
                plateCountryCode: 'SA',
            },
            driver: {
                id: 218,
                name: 'Saleh Al-Baqami',
                mobileNo: '0509000118',
                passportCountry: 'Saudi Arabia',
                passportNumber: '********',
            },
            routeId: 9001,
            entryPort: { id: 418, type: 1, name: { arabic: 'القصيم - مدخل', english: 'Qassim Entry' } },
            exitPort: { id: 428, type: 1, name: { arabic: 'القصيم - مخرج', english: 'Qassim Exit' } },
            startDate: '2025-08-14T09:00:00Z',
            endDate: null,
            tracker: { id: 318, serialNumber: 'TRK-Q1018' },
            transitDate: null,
            transSeq: null,
            shipmentDescription: 'Palletized goods',
            newShipmentDescription: null,
            transitTypeName: 'Domestic',
            ownerDesc: 'Central Distribution',
            activeAlerts: [
                {
                    id: 1,
                    type: {
                        id: 10005,
                        name: { arabic: 'البطارية ضعيفة', english: 'Battery Low' },
                    },
                },
            ],
            currentState: {
                trackerDateTime: '2025-08-14T10:40:00Z',
                locationSource: 1,
                batteryLevelPercentage: 79,
                chargerStatus: 1,
                gpsSignalStrength: 4,
                gsmSignalStrength: 4,
                currentSpeed: 58,
                routeZone: 2,
                isWithinRouteGeofence: true,
                timeElapsedSinceTripStartInMinutes: 25,
                remainingDistanceInMeters: 42000,
                completeDistanceInMeters: 42000,
                isWithinSuspiciousZone: false,
                createdAt: '2025-08-14T10:40:00Z',
                lat: 26.406,
                long: 50.07,
                address: {
                    arabic: 'King Abdulaziz Port checkpoint',
                    english: 'King Abdulaziz Port checkpoint',
                },
            },
            eLocks: [
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1001',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1002',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1003',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1004',
                },
            ],
            securityNotes: null,
            isFocused: true,
            isSuspicious: true,
        },
        {
            id: 19,
            transitNumber: 19,
            status: 1,
            trackingStatus: 1,
            vehicle: {
                id: 119,
                plateNo: 'BUR-1019',
                model: 'Iveco Eurocargo',
                type: 'Truck',
                color: 'Blue',
                plateCountryName: 'Saudi Arabia',
                plateCountryCode: 'SA',
            },
            driver: {
                id: 219,
                name: 'Mansour Al-Qahtani',
                mobileNo: '0509000119',
                passportCountry: 'Saudi Arabia',
                passportNumber: '********',
            },
            routeId: 9002,
            entryPort: { id: 419, type: 1, name: { arabic: 'بريدة - مدخل', english: 'Buraidah Entry' } },
            exitPort: { id: 429, type: 1, name: { arabic: 'بريدة - مخرج', english: 'Buraidah Exit' } },
            startDate: '2025-08-13T04:00:00Z',
            endDate: null,
            tracker: { id: 319, serialNumber: 'TRK-B1019' },
            transitDate: null,
            transSeq: null,
            shipmentDescription: 'Agricultural equipment',
            newShipmentDescription: null,
            transitTypeName: 'Domestic',
            ownerDesc: 'Qassim Agro',
            activeAlerts: [
                {
                    id: 1,
                    type: {
                        id: 1,
                        name: { arabic: 'البطارية ضعيفة', english: 'Battery Low' },
                    },
                },
            ],
            currentState: {
                trackerDateTime: '2025-08-13T05:30:00Z',
                locationSource: 1,
                batteryLevelPercentage: 67,
                chargerStatus: 1,
                gpsSignalStrength: 4,
                gsmSignalStrength: 4,
                currentSpeed: 53,
                routeZone: 2,
                isWithinRouteGeofence: true,
                timeElapsedSinceTripStartInMinutes: 130,
                remainingDistanceInMeters: 90000,
                completeDistanceInMeters: 90000,
                isWithinSuspiciousZone: false,
                createdAt: '2025-08-13T05:30:00Z',
                lat: 26.406,
                long: 50.07,
                address: {
                    arabic: 'King Abdulaziz Port checkpoint',
                    english: 'King Abdulaziz Port checkpoint',
                },
            },
            eLocks: [
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1001',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1002',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1003',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1004',
                },
            ],
            securityNotes: null,
            isFocused: true,
            isSuspicious: true,
        },

        // Al-Ula / Yanbu
        {
            id: 20,
            transitNumber: 20,
            status: 1,
            trackingStatus: 1,
            vehicle: {
                id: 120,
                plateNo: 'UL-1020',
                model: 'Mitsubishi Fuso',
                type: 'Truck',
                color: 'Silver',
                plateCountryName: 'Saudi Arabia',
                plateCountryCode: 'SA',
            },
            driver: {
                id: 220,
                name: 'Abu Bakr Al-Farhan',
                mobileNo: '0510000120',
                passportCountry: 'Saudi Arabia',
                passportNumber: '********',
            },
            routeId: 10001,
            entryPort: { id: 420, type: 1, name: { arabic: 'العلا - مدخل', english: 'Al-Ula Entry' } },
            exitPort: { id: 430, type: 1, name: { arabic: 'العلا - مخرج', english: 'Al-Ula Exit' } },
            startDate: '2025-08-12T06:00:00Z',
            endDate: null,
            tracker: { id: 320, serialNumber: 'TRK-UL1020' },
            transitDate: null,
            transSeq: null,
            shipmentDescription: 'Tourism equipment',
            newShipmentDescription: null,
            transitTypeName: 'Special',
            ownerDesc: 'Heritage Tours',
            activeAlerts: [
                {
                    id: 1,
                    type: {
                        id: 1,
                        name: { arabic: 'البطارية ضعيفة', english: 'Battery Low' },
                    },
                },
            ],
            currentState: {
                trackerDateTime: '2025-08-12T07:20:00Z',
                locationSource: 1,
                batteryLevelPercentage: 73,
                chargerStatus: 1,
                gpsSignalStrength: 3,
                gsmSignalStrength: 3,
                currentSpeed: 20,
                routeZone: 2,
                isWithinRouteGeofence: true,
                timeElapsedSinceTripStartInMinutes: 20,
                remainingDistanceInMeters: 15000,
                completeDistanceInMeters: 15000,
                isWithinSuspiciousZone: false,
                createdAt: '2025-08-12T07:20:00Z',
                lat: 26.406,
                long: 50.07,
                address: {
                    arabic: 'King Abdulaziz Port checkpoint',
                    english: 'King Abdulaziz Port checkpoint',
                },
            },
            eLocks: [
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1001',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1002',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1003',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1004',
                },
            ],
            securityNotes: null,
            isFocused: true,
            isSuspicious: true,
        },
        {
            id: 21,
            transitNumber: 21,
            status: 1,
            trackingStatus: 1,
            vehicle: {
                id: 121,
                plateNo: 'YNB-1021',
                model: 'DAF LF',
                type: 'Truck',
                color: 'White',
                plateCountryName: 'Saudi Arabia',
                plateCountryCode: 'SA',
            },
            driver: {
                id: 221,
                name: 'Ziyad Al-Sabti',
                mobileNo: '0510000121',
                passportCountry: 'Saudi Arabia',
                passportNumber: '********',
            },
            routeId: 10002,
            entryPort: { id: 421, type: 1, name: { arabic: 'ينبع - مدخل', english: 'Yanbu Entry' } },
            exitPort: { id: 431, type: 1, name: { arabic: 'ينبع - مخرج', english: 'Yanbu Exit' } },
            startDate: '2025-08-11T14:00:00Z',
            endDate: null,
            tracker: { id: 321, serialNumber: 'TRK-YN1021' },
            transitDate: null,
            transSeq: null,
            shipmentDescription: 'Oil-field supplies',
            newShipmentDescription: null,
            transitTypeName: 'Industrial',
            ownerDesc: 'Red Sea Energy',
            activeAlerts: [
                {
                    id: 1,
                    type: {
                        id: 1,
                        name: { arabic: 'البطارية ضعيفة', english: 'Battery Low' },
                    },
                },
            ],
            currentState: {
                trackerDateTime: '2025-08-11T15:30:00Z',
                locationSource: 1,
                batteryLevelPercentage: 61,
                chargerStatus: 1,
                gpsSignalStrength: 4,
                gsmSignalStrength: 4,
                currentSpeed: 90,
                routeZone: 2,
                isWithinRouteGeofence: true,
                timeElapsedSinceTripStartInMinutes: 60,
                remainingDistanceInMeters: 300000,
                completeDistanceInMeters: 300000,
                isWithinSuspiciousZone: false,
                createdAt: '2025-08-11T15:30:00Z',
                lat: 26.406,
                long: 50.07,
                address: {
                    arabic: 'King Abdulaziz Port checkpoint',
                    english: 'King Abdulaziz Port checkpoint',
                },
            },
            eLocks: [
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1001',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1002',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1003',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1004',
                },
            ],
            securityNotes: null,
            isFocused: true,
            isSuspicious: true,
        },

        // Northern Borders / Sakaka / Hail
        {
            id: 22,
            transitNumber: 22,
            status: 1,
            trackingStatus: 1,
            vehicle: {
                id: 122,
                plateNo: 'ARR-1022',
                model: 'Renault Trucks T',
                type: 'Truck',
                color: 'Gray',
                plateCountryName: 'Saudi Arabia',
                plateCountryCode: 'SA',
            },
            driver: {
                id: 222,
                name: 'Nawaf Al-Jabri',
                mobileNo: '0511000122',
                passportCountry: 'Saudi Arabia',
                passportNumber: '********',
            },
            routeId: 11001,
            entryPort: { id: 422, type: 1, name: { arabic: 'عرعر - مدخل', english: 'Arar Entry' } },
            exitPort: { id: 432, type: 1, name: { arabic: 'عرعر - مخرج', english: 'Arar Exit' } },
            startDate: '2025-08-10T01:00:00Z',
            endDate: null,
            tracker: { id: 322, serialNumber: 'TRK-AR1022' },
            transitDate: null,
            transSeq: null,
            shipmentDescription: 'Cold-chain medical',
            newShipmentDescription: null,
            transitTypeName: 'Medical',
            ownerDesc: 'Health Logistics',
            activeAlerts: [
                {
                    id: 1,
                    type: {
                        id: 10000,
                        name: { arabic: 'البطارية ضعيفة', english: 'Battery Low' },
                    },
                },
            ],
            currentState: {
                trackerDateTime: '2025-08-10T02:20:00Z',
                locationSource: 1,
                batteryLevelPercentage: 59,
                chargerStatus: 1,
                gpsSignalStrength: 3,
                gsmSignalStrength: 3,
                currentSpeed: 45,
                routeZone: 2,
                isWithinRouteGeofence: true,
                timeElapsedSinceTripStartInMinutes: 180,
                remainingDistanceInMeters: 210000,
                completeDistanceInMeters: 210000,
                isWithinSuspiciousZone: false,
                createdAt: '2025-08-10T02:20:00Z',
                lat: 26.406,
                long: 50.07,
                address: {
                    arabic: 'King Abdulaziz Port checkpoint',
                    english: 'King Abdulaziz Port checkpoint',
                },
            },
            eLocks: [
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1001',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1002',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1003',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1004',
                },
            ],
            securityNotes: null,
            isFocused: true,
            isSuspicious: true,
        },
        {
            id: 23,
            transitNumber: 23,
            status: 1,
            trackingStatus: 1,
            vehicle: {
                id: 123,
                plateNo: 'SAK-1023',
                model: 'MAN TGS',
                type: 'Truck',
                color: 'Blue',
                plateCountryName: 'Saudi Arabia',
                plateCountryCode: 'SA',
            },
            driver: {
                id: 223,
                name: 'Rashed Al-Otaibi',
                mobileNo: '0511000123',
                passportCountry: 'Saudi Arabia',
                passportNumber: '********',
            },
            routeId: 11002,
            entryPort: { id: 423, type: 1, name: { arabic: 'سكاكا - مدخل', english: 'Sakaka Entry' } },
            exitPort: { id: 433, type: 1, name: { arabic: 'سكاكا - مخرج', english: 'Sakaka Exit' } },
            startDate: '2025-08-09T08:00:00Z',
            endDate: null,
            tracker: { id: 323, serialNumber: 'TRK-SK1023' },
            transitDate: null,
            transSeq: null,
            shipmentDescription: 'Livestock feed',
            newShipmentDescription: null,
            transitTypeName: 'Agriculture',
            ownerDesc: 'Jouf Supply',
            activeAlerts: [
                {
                    id: 1,
                    type: {
                        id: 1,
                        name: { arabic: 'البطارية ضعيفة', english: 'Battery Low' },
                    },
                },
            ],
            currentState: {
                trackerDateTime: '2025-08-09T09:30:00Z',
                locationSource: 1,
                batteryLevelPercentage: 64,
                chargerStatus: 1,
                gpsSignalStrength: 4,
                gsmSignalStrength: 4,
                currentSpeed: 52,
                routeZone: 2,
                isWithinRouteGeofence: true,
                timeElapsedSinceTripStartInMinutes: 40,
                remainingDistanceInMeters: 47000,
                completeDistanceInMeters: 47000,
                isWithinSuspiciousZone: false,
                createdAt: '2025-08-09T09:30:00Z',
                lat: 26.406,
                long: 50.07,
                address: {
                    arabic: 'King Abdulaziz Port checkpoint',
                    english: 'King Abdulaziz Port checkpoint',
                },
            },
            eLocks: [
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1001',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1002',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1003',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1004',
                },
            ],
            securityNotes: null,
            isFocused: true,
            isSuspicious: true,
        },
        // Tabuk / Al-Jouf / Hail (continued)
        {
            id: 24,
            transitNumber: 24,
            status: 1,
            trackingStatus: 1,
            vehicle: {
                id: 124,
                plateNo: 'HAI-1024',
                model: 'Mercedes Atego',
                type: 'Truck',
                color: 'White',
                plateCountryName: 'Saudi Arabia',
                plateCountryCode: 'SA',
            },
            driver: {
                id: 224,
                name: 'Zaid Al-Fadhli',
                mobileNo: '0512000124',
                passportCountry: 'Saudi Arabia',
                passportNumber: '********',
            },
            routeId: 12001,
            entryPort: { id: 424, type: 1, name: { arabic: 'حائل - مدخل', english: 'Hail Entry' } },
            exitPort: { id: 434, type: 1, name: { arabic: 'حائل - مخرج', english: 'Hail Exit' } },
            startDate: '2025-08-08T10:00:00Z',
            endDate: null,
            tracker: { id: 324, serialNumber: 'TRK-H1024' },
            transitDate: null,
            transSeq: null,
            shipmentDescription: 'Household goods',
            newShipmentDescription: null,
            transitTypeName: 'Domestic',
            ownerDesc: 'Home Movers',
            activeAlerts: [
                {
                    id: 1,
                    type: {
                        id: 1,
                        name: { arabic: 'البطارية ضعيفة', english: 'Battery Low' },
                    },
                },
            ],
            currentState: {
                trackerDateTime: '2025-08-08T11:40:00Z',
                locationSource: 1,
                batteryLevelPercentage: 70,
                chargerStatus: 1,
                gpsSignalStrength: 4,
                gsmSignalStrength: 4,
                currentSpeed: 48,
                routeZone: 2,
                isWithinRouteGeofence: true,
                timeElapsedSinceTripStartInMinutes: 55,
                remainingDistanceInMeters: 65000,
                completeDistanceInMeters: 65000,
                isWithinSuspiciousZone: false,
                createdAt: '2025-08-08T11:40:00Z',
                lat: 26.406,
                long: 50.07,
                address: {
                    arabic: 'King Abdulaziz Port checkpoint',
                    english: 'King Abdulaziz Port checkpoint',
                },
            },
            eLocks: [
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1001',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1002',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1003',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1004',
                },
            ],
            securityNotes: null,
            isFocused: true,
            isSuspicious: true,
        },

        // Qassim / Northern (continued)
        {
            id: 25,
            transitNumber: 25,
            status: 1,
            trackingStatus: 1,
            vehicle: {
                id: 125,
                plateNo: 'QSM-1025',
                model: 'Iveco Daily',
                type: 'Truck',
                color: 'Yellow',
                plateCountryName: 'Saudi Arabia',
                plateCountryCode: 'SA',
            },
            driver: {
                id: 225,
                name: 'Hassan Al-Manea',
                mobileNo: '0513000125',
                passportCountry: 'Saudi Arabia',
                passportNumber: '********',
            },
            routeId: 13001,
            entryPort: { id: 425, type: 1, name: { arabic: 'القصيم - مدخل', english: 'Qassim Entry' } },
            exitPort: { id: 435, type: 1, name: { arabic: 'القصيم - مخرج', english: 'Qassim Exit' } },
            startDate: '2025-08-07T07:00:00Z',
            endDate: null,
            tracker: { id: 325, serialNumber: 'TRK-Q1025' },
            transitDate: null,
            transSeq: null,
            shipmentDescription: 'Retail stock',
            newShipmentDescription: null,
            transitTypeName: 'Retail',
            ownerDesc: 'Central Retail',
            activeAlerts: [
                {
                    id: 1,
                    type: {
                        id: 1,
                        name: { arabic: 'البطارية ضعيفة', english: 'Battery Low' },
                    },
                },
            ],
            currentState: {
                trackerDateTime: '2025-08-07T08:20:00Z',
                locationSource: 1,
                batteryLevelPercentage: 76,
                chargerStatus: 1,
                gpsSignalStrength: 4,
                gsmSignalStrength: 4,
                currentSpeed: 62,
                routeZone: 2,
                isWithinRouteGeofence: true,
                timeElapsedSinceTripStartInMinutes: 30,
                remainingDistanceInMeters: 52000,
                completeDistanceInMeters: 52000,
                isWithinSuspiciousZone: false,
                createdAt: '2025-08-07T08:20:00Z',
                lat: 26.406,
                long: 50.07,
                address: {
                    arabic: 'King Abdulaziz Port checkpoint',
                    english: 'King Abdulaziz Port checkpoint',
                },
            },
            eLocks: [
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1001',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1002',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1003',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1004',
                },
            ],
            securityNotes: null,
            isFocused: true,
            isSuspicious: true,
        },
        {
            id: 26,
            transitNumber: 26,
            status: 3, // Closed
            trackingStatus: 2,
            vehicle: {
                id: 126,
                plateNo: 'QSM-1026',
                model: 'Hyundai Mighty',
                type: 'Truck',
                color: 'White',
                plateCountryName: 'Saudi Arabia',
                plateCountryCode: 'SA',
            },
            driver: {
                id: 226,
                name: 'Adel Al-Balawi',
                mobileNo: '0513000126',
                passportCountry: 'Saudi Arabia',
                passportNumber: '********',
            },
            routeId: 13002,
            entryPort: { id: 426, type: 1, name: { arabic: 'القصيم - مدخل', english: 'Qassim Entry' } },
            exitPort: { id: 436, type: 1, name: { arabic: 'القصيم - مخرج', english: 'Qassim Exit' } },
            startDate: '2025-07-30T04:00:00Z',
            endDate: '2025-07-31T04:00:00Z',
            tracker: { id: 326, serialNumber: 'TRK-Q1026' },
            transitDate: '2025-07-31',
            transSeq: 'S-13026',
            shipmentDescription: 'Office supplies',
            newShipmentDescription: null,
            transitTypeName: 'Domestic',
            ownerDesc: 'OfficePro',
            activeAlerts: [],
            currentState: {
                trackerDateTime: '2025-07-31T04:00:00Z',
                locationSource: 1,
                batteryLevelPercentage: 40,
                chargerStatus: 1,
                gpsSignalStrength: 3,
                gsmSignalStrength: 3,
                currentSpeed: 0,
                routeZone: 3,
                isWithinRouteGeofence: true,
                timeElapsedSinceTripStartInMinutes: 0,
                remainingDistanceInMeters: 0,
                completeDistanceInMeters: 0,
                isWithinSuspiciousZone: false,
                createdAt: '2025-07-31T04:00:00Z',
                lat: 26.406,
                long: 50.07,
                address: {
                    arabic: 'King Abdulaziz Port checkpoint',
                    english: 'King Abdulaziz Port checkpoint',
                },
            },
            eLocks: [
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1001',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1002',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1003',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1004',
                },
            ],
            securityNotes: null,
            isFocused: true,
            isSuspicious: true,
        },

        // Eastern Province continued
        {
            id: 27,
            transitNumber: 27,
            status: 1,
            trackingStatus: 1,
            vehicle: {
                id: 127,
                plateNo: 'JUB-1027',
                model: 'Iveco Stralis',
                type: 'Truck',
                color: 'Red',
                plateCountryName: 'Saudi Arabia',
                plateCountryCode: 'SA',
            },
            driver: {
                id: 227,
                name: 'Bader Al-Otaibi',
                mobileNo: '0514000127',
                passportCountry: 'Saudi Arabia',
                passportNumber: '********',
            },
            routeId: 14001,
            entryPort: { id: 427, type: 1, name: { arabic: 'جبيل - مدخل', english: 'Jubail Entry' } },
            exitPort: { id: 437, type: 1, name: { arabic: 'جبيل - مخرج', english: 'Jubail Exit' } },
            startDate: '2025-08-05T05:00:00Z',
            endDate: null,
            tracker: { id: 327, serialNumber: 'TRK-J1027' },
            transitDate: null,
            transSeq: null,
            shipmentDescription: 'Refinery parts',
            newShipmentDescription: null,
            transitTypeName: 'Industrial',
            ownerDesc: 'Eastern Refineries',
            activeAlerts: [],
            currentState: {
                trackerDateTime: '2025-08-05T06:20:00Z',
                locationSource: 1,
                batteryLevelPercentage: 82,
                chargerStatus: 1,
                gpsSignalStrength: 4,
                gsmSignalStrength: 4,
                currentSpeed: 72,
                routeZone: 2,
                isWithinRouteGeofence: true,
                timeElapsedSinceTripStartInMinutes: 100,
                remainingDistanceInMeters: 160000,
                completeDistanceInMeters: 160000,
                isWithinSuspiciousZone: false,
                createdAt: '2025-08-05T06:20:00Z',
                lat: 26.406,
                long: 50.07,
                address: {
                    arabic: 'King Abdulaziz Port checkpoint',
                    english: 'King Abdulaziz Port checkpoint',
                },
            },
            eLocks: [
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1001',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1002',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1003',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1004',
                },
            ],
            securityNotes: null,
            isFocused: true,
            isSuspicious: true,
        },

        // Northern / final group
        {
            id: 28,
            transitNumber: 28,
            status: 1,
            trackingStatus: 1,
            vehicle: {
                id: 128,
                plateNo: 'NJR-1028',
                model: 'Volvo FL',
                type: 'Truck',
                color: 'Blue',
                plateCountryName: 'Saudi Arabia',
                plateCountryCode: 'SA',
            },
            driver: {
                id: 228,
                name: 'Yahya Al-Rashed',
                mobileNo: '0515000128',
                passportCountry: 'Saudi Arabia',
                passportNumber: '********',
            },
            routeId: 15001,
            entryPort: { id: 428, type: 1, name: { arabic: 'نجران - مدخل', english: 'Najran Entry' } },
            exitPort: { id: 438, type: 1, name: { arabic: 'نجران - مخرج', english: 'Najran Exit' } },
            startDate: '2025-08-04T03:00:00Z',
            endDate: null,
            tracker: { id: 328, serialNumber: 'TRK-N1028' },
            transitDate: null,
            transSeq: null,
            shipmentDescription: 'Local construction',
            newShipmentDescription: null,
            transitTypeName: 'Domestic',
            ownerDesc: 'Southern Builders',
            activeAlerts: [],
            currentState: {
                trackerDateTime: '2025-08-04T04:30:00Z',
                locationSource: 1,
                batteryLevelPercentage: 74,
                chargerStatus: 1,
                gpsSignalStrength: 4,
                gsmSignalStrength: 4,
                currentSpeed: 66,
                routeZone: 2,
                isWithinRouteGeofence: true,
                timeElapsedSinceTripStartInMinutes: 85,
                remainingDistanceInMeters: 120000,
                completeDistanceInMeters: 120000,
                isWithinSuspiciousZone: false,
                createdAt: '2025-08-04T04:30:00Z',
                lat: 26.406,
                long: 50.07,
                address: {
                    arabic: 'King Abdulaziz Port checkpoint',
                    english: 'King Abdulaziz Port checkpoint',
                },
            },
            eLocks: [
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1001',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1002',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1003',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1004',
                },
            ],
            securityNotes: null,
            isFocused: true,
            isSuspicious: true,
        },
        {
            id: 29,
            transitNumber: 29,
            status: 1,
            trackingStatus: 1,
            vehicle: {
                id: 129,
                plateNo: 'ALU-1029',
                model: 'Scania R450',
                type: 'Truck',
                color: 'White',
                plateCountryName: 'Saudi Arabia',
                plateCountryCode: 'SA',
            },
            driver: {
                id: 229,
                name: 'Kareem Al-Amri',
                mobileNo: '0515000129',
                passportCountry: 'Saudi Arabia',
                passportNumber: '********',
            },
            routeId: 16001,
            entryPort: { id: 429, type: 1, name: { arabic: 'العلا - مدخل', english: 'Al-Ula Entry' } },
            exitPort: { id: 439, type: 1, name: { arabic: 'العلا - مخرج', english: 'Al-Ula Exit' } },
            startDate: '2025-08-03T09:00:00Z',
            endDate: null,
            tracker: { id: 329, serialNumber: 'TRK-AU1029' },
            transitDate: null,
            transSeq: null,
            shipmentDescription: 'Museum materials',
            newShipmentDescription: null,
            transitTypeName: 'Special',
            ownerDesc: 'Heritage Cargo',
            activeAlerts: [],
            currentState: {
                trackerDateTime: '2025-08-03T10:20:00Z',
                locationSource: 1,
                batteryLevelPercentage: 63,
                chargerStatus: 1,
                gpsSignalStrength: 3,
                gsmSignalStrength: 3,
                currentSpeed: 25,
                routeZone: 2,
                isWithinRouteGeofence: true,
                timeElapsedSinceTripStartInMinutes: 30,
                remainingDistanceInMeters: 20000,
                completeDistanceInMeters: 20000,
                isWithinSuspiciousZone: false,
                createdAt: '2025-08-03T10:20:00Z',
                lat: 26.406,
                long: 50.07,
                address: {
                    arabic: 'King Abdulaziz Port checkpoint',
                    english: 'King Abdulaziz Port checkpoint',
                },
            },
            eLocks: [
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1001',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1002',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1003',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1004',
                },
            ],
            securityNotes: null,
            isFocused: true,
            isSuspicious: true,
        },
        {
            id: 30,
            transitNumber: 30,
            status: 1,
            trackingStatus: 1,
            vehicle: {
                id: 130,
                plateNo: 'YNB-1030',
                model: 'Iveco Power',
                type: 'Truck',
                color: 'Gray',
                plateCountryName: 'Saudi Arabia',
                plateCountryCode: 'SA',
            },
            driver: {
                id: 230,
                name: 'Saud Al-Balawi',
                mobileNo: '0516000130',
                passportCountry: 'Saudi Arabia',
                passportNumber: '********',
            },
            routeId: 16002,
            entryPort: { id: 430, type: 1, name: { arabic: 'ينبع - مدخل', english: 'Yanbu Entry' } },
            exitPort: { id: 440, type: 1, name: { arabic: 'ينبع - مخرج', english: 'Yanbu Exit' } },
            startDate: '2025-08-02T12:00:00Z',
            endDate: null,
            tracker: { id: 330, serialNumber: 'TRK-YN1030' },
            transitDate: null,
            transSeq: null,
            shipmentDescription: 'Marine equipment',
            newShipmentDescription: null,
            transitTypeName: 'Industrial',
            ownerDesc: 'Red Sea Marine',
            activeAlerts: [],
            currentState: {
                trackerDateTime: '2025-08-02T13:30:00Z',
                locationSource: 1,
                batteryLevelPercentage: 54,
                chargerStatus: 1,
                gpsSignalStrength: 3,
                gsmSignalStrength: 3,
                currentSpeed: 95,
                routeZone: 2,
                isWithinRouteGeofence: true,
                timeElapsedSinceTripStartInMinutes: 20,
                remainingDistanceInMeters: 320000,
                completeDistanceInMeters: 450000,
                isWithinSuspiciousZone: false,
                createdAt: '2025-08-02T13:30:00Z',
                lat: 26.406,
                long: 50.07,
                address: {
                    arabic: 'King Abdulaziz Port checkpoint',
                    english: 'King Abdulaziz Port checkpoint',
                },
            },
            eLocks: [
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1001',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1002',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1003',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1004',
                },
            ],
            securityNotes: null,
            isFocused: true,
            isSuspicious: true,
        },

        // Remaining items to reach 40 (various regions)
        {
            id: 31,
            transitNumber: 31,
            status: 1,
            trackingStatus: 1,
            vehicle: {
                id: 131,
                plateNo: 'RUH-1031',
                model: 'Hino 300',
                type: 'Truck',
                color: 'Green',
                plateCountryName: 'Saudi Arabia',
                plateCountryCode: 'SA',
            },
            driver: {
                id: 231,
                name: 'Mubarak Al-Hazmi',
                mobileNo: '0517000131',
                passportCountry: 'Saudi Arabia',
                passportNumber: '********',
            },
            routeId: 17001,
            entryPort: { id: 431, type: 1, name: { arabic: 'الرياض - مدخل', english: 'Riyadh Entry' } },
            exitPort: { id: 441, type: 1, name: { arabic: 'الرياض - مخرج', english: 'Riyadh Exit' } },
            startDate: '2025-08-01T06:00:00Z',
            endDate: null,
            tracker: { id: 331, serialNumber: 'TRK-A1031' },
            transitDate: null,
            transSeq: null,
            shipmentDescription: 'Canned goods',
            newShipmentDescription: null,
            transitTypeName: 'Retail',
            ownerDesc: 'Kingdom Foods',
            activeAlerts: [],
            currentState: {
                trackerDateTime: '2025-08-01T07:10:00Z',
                locationSource: 1,
                batteryLevelPercentage: 85,
                chargerStatus: 1,
                gpsSignalStrength: 4,
                gsmSignalStrength: 4,
                currentSpeed: 60,
                routeZone: 2,
                isWithinRouteGeofence: true,
                timeElapsedSinceTripStartInMinutes: 40,
                remainingDistanceInMeters: 50000,
                completeDistanceInMeters: 50000,
                isWithinSuspiciousZone: false,
                createdAt: '2025-08-01T07:10:00Z',
                lat: 26.406,
                long: 50.07,
                address: {
                    arabic: 'King Abdulaziz Port checkpoint',
                    english: 'King Abdulaziz Port checkpoint',
                },
            },
            eLocks: [
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1001',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1002',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1003',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1004',
                },
            ],
            securityNotes: null,
            isFocused: true,
            isSuspicious: true,
        },
        {
            id: 32,
            transitNumber: 32,
            status: 1,
            trackingStatus: 1,
            vehicle: {
                id: 132,
                plateNo: 'JED-1032',
                model: 'Volvo FMX',
                type: 'Truck',
                color: 'Black',
                plateCountryName: 'Saudi Arabia',
                plateCountryCode: 'SA',
            },
            driver: {
                id: 232,
                name: 'Saeed Al-Faraj',
                mobileNo: '0517000132',
                passportCountry: 'Saudi Arabia',
                passportNumber: '********',
            },
            routeId: 17002,
            entryPort: { id: 432, type: 1, name: { arabic: 'جدة - مدخل', english: 'Jeddah Entry' } },
            exitPort: { id: 442, type: 1, name: { arabic: 'جدة - مخرج', english: 'Jeddah Exit' } },
            startDate: '2025-07-31T11:00:00Z',
            endDate: null,
            tracker: { id: 332, serialNumber: 'TRK-J1032' },
            transitDate: null,
            transSeq: null,
            shipmentDescription: 'Clothing',
            newShipmentDescription: null,
            transitTypeName: 'Retail',
            ownerDesc: 'Fashion Hub',
            activeAlerts: [],
            currentState: {
                trackerDateTime: '2025-07-31T12:30:00Z',
                locationSource: 1,
                batteryLevelPercentage: 68,
                chargerStatus: 1,
                gpsSignalStrength: 4,
                gsmSignalStrength: 4,
                currentSpeed: 52,
                routeZone: 2,
                isWithinRouteGeofence: true,
                timeElapsedSinceTripStartInMinutes: 180,
                remainingDistanceInMeters: 110000,
                completeDistanceInMeters: 110000,
                isWithinSuspiciousZone: false,
                createdAt: '2025-07-31T12:30:00Z',
                lat: 26.406,
                long: 50.07,
                address: {
                    arabic: 'King Abdulaziz Port checkpoint',
                    english: 'King Abdulaziz Port checkpoint',
                },
            },
            eLocks: [
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1001',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1002',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1003',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1004',
                },
            ],
            securityNotes: null,
            isFocused: true,
            isSuspicious: true,
        },
        {
            id: 33,
            transitNumber: 33,
            status: 1,
            trackingStatus: 1,
            vehicle: {
                id: 133,
                plateNo: 'MAD-1033',
                model: 'Scania P-series',
                type: 'Truck',
                color: 'Red',
                plateCountryName: 'Saudi Arabia',
                plateCountryCode: 'SA',
            },
            driver: {
                id: 233,
                name: 'Ilyas Al-Mansour',
                mobileNo: '0518000133',
                passportCountry: 'Saudi Arabia',
                passportNumber: '********',
            },
            routeId: 18001,
            entryPort: { id: 433, type: 1, name: { arabic: 'المدينة - مدخل', english: 'Madinah Entry' } },
            exitPort: { id: 443, type: 1, name: { arabic: 'المدينة - مخرج', english: 'Madinah Exit' } },
            startDate: '2025-07-30T09:00:00Z',
            endDate: null,
            tracker: { id: 333, serialNumber: 'TRK-MD1033' },
            transitDate: null,
            transSeq: null,
            shipmentDescription: 'Religious supplies',
            newShipmentDescription: null,
            transitTypeName: 'Special',
            ownerDesc: 'Pilgrim Goods',
            activeAlerts: [],
            currentState: {
                trackerDateTime: '2025-07-30T10:45:00Z',
                locationSource: 1,
                batteryLevelPercentage: 75,
                chargerStatus: 1,
                gpsSignalStrength: 4,
                gsmSignalStrength: 4,
                currentSpeed: 40,
                routeZone: 2,
                isWithinRouteGeofence: true,
                timeElapsedSinceTripStartInMinutes: 260,
                remainingDistanceInMeters: 100000,
                completeDistanceInMeters: 100000,
                isWithinSuspiciousZone: false,
                createdAt: '2025-07-30T10:45:00Z',
                lat: 26.406,
                long: 50.07,
                address: {
                    arabic: 'King Abdulaziz Port checkpoint',
                    english: 'King Abdulaziz Port checkpoint',
                },
            },
            eLocks: [
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1001',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1002',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1003',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1004',
                },
            ],
            securityNotes: null,
            isFocused: true,
            isSuspicious: true,
        },
        {
            id: 34,
            transitNumber: 34,
            status: 1,
            trackingStatus: 1,
            vehicle: {
                id: 134,
                plateNo: 'DMM-1034',
                model: 'Mercedes Actros',
                type: 'Truck',
                color: 'White',
                plateCountryName: 'Saudi Arabia',
                plateCountryCode: 'SA',
            },
            driver: {
                id: 234,
                name: 'Feras Al-Qahtani',
                mobileNo: '0518000134',
                passportCountry: 'Saudi Arabia',
                passportNumber: '********',
            },
            routeId: 18002,
            entryPort: { id: 434, type: 1, name: { arabic: 'الدمام - مدخل', english: 'Dammam Entry' } },
            exitPort: { id: 444, type: 1, name: { arabic: 'الدمام - مخرج', english: 'Dammam Exit' } },
            startDate: '2025-07-29T14:00:00Z',
            endDate: null,
            tracker: { id: 334, serialNumber: 'TRK-D1034' },
            transitDate: null,
            transSeq: null,
            shipmentDescription: 'Spare parts',
            newShipmentDescription: null,
            transitTypeName: 'Industrial',
            ownerDesc: 'Eastern Spares',
            activeAlerts: [],
            currentState: {
                trackerDateTime: '2025-07-29T15:10:00Z',
                locationSource: 1,
                batteryLevelPercentage: 81,
                chargerStatus: 1,
                gpsSignalStrength: 4,
                gsmSignalStrength: 4,
                currentSpeed: 68,
                routeZone: 2,
                isWithinRouteGeofence: true,
                timeElapsedSinceTripStartInMinutes: 30,
                remainingDistanceInMeters: 125000,
                completeDistanceInMeters: 125000,
                isWithinSuspiciousZone: false,
                createdAt: '2025-07-29T15:10:00Z',
                lat: 26.406,
                long: 50.07,
                address: {
                    arabic: 'King Abdulaziz Port checkpoint',
                    english: 'King Abdulaziz Port checkpoint',
                },
            },
            eLocks: [
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1001',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1002',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1003',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1004',
                },
            ],
            securityNotes: null,
            isFocused: true,
            isSuspicious: true,
        },
        {
            id: 35,
            transitNumber: 35,
            status: 1,
            trackingStatus: 1,
            vehicle: {
                id: 135,
                plateNo: 'ALU-1035',
                model: 'Hino 500',
                type: 'Truck',
                color: 'Brown',
                plateCountryName: 'Saudi Arabia',
                plateCountryCode: 'SA',
            },
            driver: {
                id: 235,
                name: 'Nawfal Al-Zahrani',
                mobileNo: '0519000135',
                passportCountry: 'Saudi Arabia',
                passportNumber: '********',
            },
            routeId: 19001,
            entryPort: { id: 435, type: 1, name: { arabic: 'العلا - مدخل', english: 'Al-Ula Entry' } },
            exitPort: { id: 445, type: 1, name: { arabic: 'العلا - مخرج', english: 'Al-Ula Exit' } },
            startDate: '2025-07-28T08:00:00Z',
            endDate: null,
            tracker: { id: 335, serialNumber: 'TRK-AU1035' },
            transitDate: null,
            transSeq: null,
            shipmentDescription: 'Exhibition stands',
            newShipmentDescription: null,
            transitTypeName: 'Special',
            ownerDesc: 'Expo Logistics',
            activeAlerts: [],
            currentState: {
                trackerDateTime: '2025-07-28T09:20:00Z',
                locationSource: 1,
                batteryLevelPercentage: 60,
                chargerStatus: 1,
                gpsSignalStrength: 3,
                gsmSignalStrength: 3,
                currentSpeed: 28,
                routeZone: 2,
                isWithinRouteGeofence: true,
                timeElapsedSinceTripStartInMinutes: 12,
                remainingDistanceInMeters: 12000,
                completeDistanceInMeters: 12000,
                isWithinSuspiciousZone: false,
                createdAt: '2025-07-28T09:20:00Z',
                lat: 26.406,
                long: 50.07,
                address: {
                    arabic: 'King Abdulaziz Port checkpoint',
                    english: 'King Abdulaziz Port checkpoint',
                },
            },
            eLocks: [
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1001',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1002',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1003',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1004',
                },
            ],
            securityNotes: null,
            isFocused: true,
            isSuspicious: true,
        },
        {
            id: 36,
            transitNumber: 36,
            status: 1,
            trackingStatus: 1,
            vehicle: {
                id: 136,
                plateNo: 'YNB-1036',
                model: 'Scania G-series',
                type: 'Truck',
                color: 'White',
                plateCountryName: 'Saudi Arabia',
                plateCountryCode: 'SA',
            },
            driver: {
                id: 236,
                name: 'Hamad Al-Subaie',
                mobileNo: '0519000136',
                passportCountry: 'Saudi Arabia',
                passportNumber: '********',
            },
            routeId: 19002,
            entryPort: { id: 436, type: 1, name: { arabic: 'ينبع - مدخل', english: 'Yanbu Entry' } },
            exitPort: { id: 446, type: 1, name: { arabic: 'ينبع - مخرج', english: 'Yanbu Exit' } },
            startDate: '2025-07-27T13:00:00Z',
            endDate: null,
            tracker: { id: 336, serialNumber: 'TRK-YN1036' },
            transitDate: null,
            transSeq: null,
            shipmentDescription: 'Marine spares',
            newShipmentDescription: null,
            transitTypeName: 'Industrial',
            ownerDesc: 'Gulf Marine',
            activeAlerts: [],
            currentState: {
                trackerDateTime: '2025-07-27T14:40:00Z',
                locationSource: 1,
                batteryLevelPercentage: 57,
                chargerStatus: 1,
                gpsSignalStrength: 3,
                gsmSignalStrength: 3,
                currentSpeed: 78,
                routeZone: 2,
                isWithinRouteGeofence: true,
                timeElapsedSinceTripStartInMinutes: 45,
                remainingDistanceInMeters: 200000,
                completeDistanceInMeters: 200000,
                isWithinSuspiciousZone: false,
                createdAt: '2025-07-27T14:40:00Z',
                lat: 26.406,
                long: 50.07,
                address: {
                    arabic: 'King Abdulaziz Port checkpoint',
                    english: 'King Abdulaziz Port checkpoint',
                },
            },
            eLocks: [
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1001',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1002',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1003',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1004',
                },
            ],
            securityNotes: null,
            isFocused: true,
            isSuspicious: true,
        },

        // Final few entries
        {
            id: 37,
            transitNumber: 37,
            status: 1,
            trackingStatus: 1,
            vehicle: {
                id: 137,
                plateNo: 'ARR-1037',
                model: 'MAN TGX',
                type: 'Truck',
                color: 'Green',
                plateCountryName: 'Saudi Arabia',
                plateCountryCode: 'SA',
            },
            driver: {
                id: 237,
                name: 'Mazen Al-Qahtani',
                mobileNo: '0520000137',
                passportCountry: 'Saudi Arabia',
                passportNumber: '********',
            },
            routeId: 20001,
            entryPort: { id: 437, type: 1, name: { arabic: 'عرعر - مدخل', english: 'Arar Entry' } },
            exitPort: { id: 447, type: 1, name: { arabic: 'عرعر - مخرج', english: 'Arar Exit' } },
            startDate: '2025-07-26T01:00:00Z',
            endDate: null,
            tracker: { id: 337, serialNumber: 'TRK-AR1037' },
            transitDate: null,
            transSeq: null,
            shipmentDescription: 'Medical supplies',
            newShipmentDescription: null,
            transitTypeName: 'Medical',
            ownerDesc: 'Frontline Logistics',
            activeAlerts: [],
            currentState: {
                trackerDateTime: '2025-07-26T02:10:00Z',
                locationSource: 1,
                batteryLevelPercentage: 66,
                chargerStatus: 1,
                gpsSignalStrength: 4,
                gsmSignalStrength: 4,
                currentSpeed: 48,
                routeZone: 2,
                isWithinRouteGeofence: true,
                timeElapsedSinceTripStartInMinutes: 150,
                remainingDistanceInMeters: 150000,
                completeDistanceInMeters: 150000,
                isWithinSuspiciousZone: false,
                createdAt: '2025-07-26T02:10:00Z',
                lat: 26.406,
                long: 50.07,
                address: {
                    arabic: 'King Abdulaziz Port checkpoint',
                    english: 'King Abdulaziz Port checkpoint',
                },
            },
            eLocks: [
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1001',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1002',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1003',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1004',
                },
            ],
            securityNotes: null,
            isFocused: true,
            isSuspicious: true,
        },
        {
            id: 38,
            transitNumber: 38,
            status: 1,
            trackingStatus: 1,
            vehicle: {
                id: 138,
                plateNo: 'SAK-1038',
                model: 'Iveco Eurocargo',
                type: 'Truck',
                color: 'Blue',
                plateCountryName: 'Saudi Arabia',
                plateCountryCode: 'SA',
            },
            driver: {
                id: 238,
                name: 'Tariq Al-Johani',
                mobileNo: '0520000138',
                passportCountry: 'Saudi Arabia',
                passportNumber: '********',
            },
            routeId: 20002,
            entryPort: { id: 438, type: 2, name: { arabic: 'سكاكا - مدخل', english: 'Sakaka Entry' } },
            exitPort: { id: 448, type: 3, name: { arabic: 'سكاكا - مخرج', english: 'Sakaka Exit' } },
            startDate: '2025-07-25T11:00:00Z',
            endDate: null,
            tracker: { id: 338, serialNumber: 'TRK-SK1038' },
            transitDate: null,
            transSeq: null,
            shipmentDescription: 'Animal feed',
            newShipmentDescription: null,
            transitTypeName: 'Agriculture',
            ownerDesc: 'Jouf Feed',
            activeAlerts: [],
            currentState: {
                trackerDateTime: '2025-07-25T12:30:00Z',
                locationSource: 1,
                batteryLevelPercentage: 69,
                chargerStatus: 1,
                gpsSignalStrength: 4,
                gsmSignalStrength: 4,
                currentSpeed: 50,
                routeZone: 2,
                isWithinRouteGeofence: true,
                timeElapsedSinceTripStartInMinutes: 60,
                remainingDistanceInMeters: 78000,
                completeDistanceInMeters: 78000,
                isWithinSuspiciousZone: false,
                createdAt: '2025-07-25T12:30:00Z',
                lat: 26.406,
                long: 50.07,
                address: {
                    arabic: 'King Abdulaziz Port checkpoint',
                    english: 'King Abdulaziz Port checkpoint',
                },
            },
            eLocks: [
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1001',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1002',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1003',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1004',
                },
            ],
            securityNotes: null,
            isFocused: true,
            isSuspicious: true,
        },
        {
            id: 39,
            transitNumber: 39,
            status: 1,
            trackingStatus: 1,
            vehicle: {
                id: 139,
                plateNo: 'TAB-1039',
                model: 'Mitsubishi Fuso',
                type: 'Truck',
                color: 'Red',
                plateCountryName: 'Saudi Arabia',
                plateCountryCode: 'SA',
            },
            driver: {
                id: 239,
                name: 'Riyadh Al-Mansour',
                mobileNo: '0521000139',
                passportCountry: 'Saudi Arabia',
                passportNumber: '********',
            },
            routeId: 21001,
            entryPort: { id: 439, type: 3, name: { arabic: 'الطائف - مدخل', english: 'Taif Entry' } },
            exitPort: { id: 449, type: 1, name: { arabic: 'الطائف - مخرج', english: 'Taif Exit' } },
            startDate: '2025-07-24T07:00:00Z',
            endDate: null,
            tracker: { id: 339, serialNumber: 'TRK-TB1039' },
            transitDate: null,
            transSeq: null,
            shipmentDescription: 'Event equipment',
            newShipmentDescription: null,
            transitTypeName: 'Special',
            ownerDesc: 'EventPro',
            activeAlerts: [],
            currentState: {
                trackerDateTime: '2025-07-24T08:20:00Z',
                locationSource: 1,
                batteryLevelPercentage: 70,
                chargerStatus: 1,
                gpsSignalStrength: 4,
                gsmSignalStrength: 4,
                currentSpeed: 33,
                routeZone: 2,
                isWithinRouteGeofence: true,
                timeElapsedSinceTripStartInMinutes: 20,
                remainingDistanceInMeters: 18000,
                completeDistanceInMeters: 18000,
                isWithinSuspiciousZone: false,
                createdAt: '2025-07-24T08:20:00Z',
                lat: 26.406,
                long: 50.07,
                address: {
                    arabic: 'King Abdulaziz Port checkpoint',
                    english: 'King Abdulaziz Port checkpoint',
                },
            },
            eLocks: [
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1001',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1002',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1003',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1004',
                },
            ],
            securityNotes: null,
            isFocused: true,
            isSuspicious: true,
        },
        {
            id: 40,
            transitNumber: 40,
            status: 2,
            trackingStatus: 2,
            vehicle: {
                id: 140,
                plateNo: 'ABH-1040',
                model: 'Volvo FH',
                type: 'Truck',
                color: 'White',
                plateCountryName: 'Saudi Arabia',
                plateCountryCode: 'SA',
            },
            driver: {
                id: 240,
                name: 'Samir Al-Dossary',
                mobileNo: '0521000140',
                passportCountry: 'Saudi Arabia',
                passportNumber: '********',
            },
            routeId: 22001,
            entryPort: { id: 440, type: 2, name: { arabic: 'أبها - مدخل', english: 'Abha Entry' } },
            exitPort: { id: 450, type: 3, name: { arabic: 'أبها - مخرج', english: 'Abha Exit' } },
            startDate: '2025-07-23T03:00:00Z',
            endDate: '2025-07-23T12:00:00Z',
            tracker: { id: 340, serialNumber: 'TRK-AH1040' },
            transitDate: '2025-07-23',
            transSeq: 'S-22040',
            shipmentDescription: 'Delivered - closed',
            newShipmentDescription: null,
            transitTypeName: 'Domestic',
            ownerDesc: 'Abha Deliveries',
            activeAlerts: [],
            currentState: {
                trackerDateTime: '2025-07-23T12:00:00Z',
                locationSource: 1,
                batteryLevelPercentage: 49,
                chargerStatus: 1,
                gpsSignalStrength: 4,
                gsmSignalStrength: 3,
                currentSpeed: 0,
                routeZone: 3,
                isWithinRouteGeofence: true,
                timeElapsedSinceTripStartInMinutes: 0,
                remainingDistanceInMeters: 0,
                completeDistanceInMeters: 0,
                isWithinSuspiciousZone: false,
                createdAt: '2025-07-23T12:00:00Z',
                lat: 26.406,
                long: 50.07,
                address: {
                    arabic: 'King Abdulaziz Port checkpoint',
                    english: 'King Abdulaziz Port checkpoint',
                },
            },
            eLocks: [
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1001',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1002',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1003',
                },
                {
                    deviceId: 201,
                    tripELockId: 1,
                    serialNumber: 'Elock-E1004',
                },
            ],
            securityNotes: null,
            isFocused: true,
            isSuspicious: true,
        },
    ],
    pagination: {
        pageSize: 20,
        currentPage: 1,
        totalPages: 2,
        totalCount: 40,
        hasPrevious: false,
        hasNext: true,
    },
};
