import type { GetAlertTypesResponse } from '../../api/alert-types/types';

export const dummyAlertTypesResponse: GetAlertTypesResponse = {
    data: [
        {
            id: 2,
            name: {
                arabic: 'العبث بجهاز التتبع',
                english: 'Tracker Tamper',
            },
            code: 1,
            priority: 1,
            origin: 2,
        },
        {
            id: 3,
            name: {
                arabic: 'سقوط جھاز التتبع',
                english: 'Tracker Dropped',
            },
            code: 2,
            priority: 1,
            origin: 2,
        },
        {
            id: 4,
            name: {
                arabic: 'العبث في القفل',
                english: 'Lock Tamper',
            },
            code: 3,
            priority: 1,
            origin: 2,
        },
        {
            id: 5,
            name: {
                arabic: 'تم فتح القفل',
                english: 'Lock Open',
            },
            code: 4,
            priority: 1,
            origin: 2,
        },
        {
            id: 6,
            name: {
                arabic: 'فقدان الإتصال مع القفل',
                english: 'Lock Connection Lost',
            },
            code: 5,
            priority: 1,
            origin: 2,
        },
        {
            id: 7,
            name: {
                arabic: 'بطارية جھاز التتبع ضعيفة',
                english: 'Tracker Battery Low',
            },
            code: 31,
            priority: 2,
            origin: 2,
        },
        {
            id: 8,
            name: {
                arabic: 'بطارية القفل ضعيفة',
                english: 'Lock Low Battery',
            },
            code: 32,
            priority: 2,
            origin: 2,
        },
        {
            id: 9,
            name: {
                arabic: 'بطارية القفل ضعيفة',
                english: 'Lock Very Low Battery',
            },
            code: 33,
            priority: 2,
            origin: 2,
        },
        {
            id: 10,
            name: {
                arabic: 'فقدان إشارة شبكة الإتصال',
                english: 'GSM Signal Lost',
            },
            code: 34,
            priority: 1,
            origin: 2,
        },
        {
            id: 1,
            name: {
                arabic: 'فقدان إشارة تحديد الموقع',
                english: 'GPS Signal Lost',
            },
            code: 35,
            priority: 1,
            origin: 2,
        },
    ],
    pagination: {
        pageSize: 25,
        currentPage: 1,
        totalPages: 1,
        totalCount: 25,
        hasPrevious: false,
        hasNext: false,
    },
};
