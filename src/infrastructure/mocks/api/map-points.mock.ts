import type { GetMapPointsResponse } from '../../api/map-points/types';

export const MapPointData: GetMapPointsResponse = {
    data: [
        {
            id: 1,
            name: {
                arabic: 'مركز توزيع الرياض',
                english: 'Riyadh Distribution Center',
            },
            type: 1,
            entryType: 1,
            lat: 28.7136,
            long: 46.6753,
            contact: {
                name: {
                    arabic: 'أحمد محمد العتيبي',
                    english: '<PERSON>',
                },
                phone: '+966-50-123-4567',
            },
        },
        {
            id: 2,
            name: {
                arabic: 'نقطة تفتيش المنطقة الشرقية',
                english: 'Eastern Province Checkpoint',
            },
            type: 1,
            entryType: 2,
            lat: 25.4207,
            long: 43.9777,
            contact: {
                name: {
                    arabic: 'خالد سعد الدوسري',
                    english: '<PERSON>',
                },
                phone: '+966-55-987-6543',
            },
        },
        {
            id: 3,
            name: {
                arabic: 'مر<PERSON><PERSON> شحن جدة',
                english: 'Jeddah Cargo Hub',
            },
            type: 1,
            entryType: 1,
            lat: 25.4225,
            long: 40.2261,
            contact: {
                name: {
                    arabic: 'عبدالله عبدالرحمن الغامدي',
                    english: 'Abdullah Abdulrahman Al-Ghamdi',
                },
                phone: '+966-56-456-7890',
            },
        },
        {
            id: 4,
            name: {
                arabic: 'استراحة القصيم',
                english: 'Al-Qassim Rest Stop',
            },
            type: 1,
            entryType: 1,
            lat: 20.3269,
            long: 40.975,
            contact: {
                name: {
                    arabic: 'محمد عبدالعزيز السبيعي',
                    english: 'Mohammed Abdulaziz Al-Subaie',
                },
                phone: '+966-53-234-5678',
            },
        },
        {
            id: 5,
            name: {
                arabic: 'مرفق حدود تبوك',
                english: 'Tabuk Border Facility',
            },
            type: 1,
            entryType: 2,
            lat: 28.3998,
            long: 36.57,
            contact: {
                name: {
                    arabic: 'فهد صالح الحربي',
                    english: 'Fahd Saleh Al-Harbi',
                },
                phone: '+966-54-345-6789',
            },
        },
        {
            id: 6,
            name: {
                arabic: 'نقطة تفتيش عسير',
                english: 'Asir Checkpoint',
            },
            type: 1,
            entryType: 2,
            lat: 18.2465,
            long: 42.5047,
            contact: {
                name: {
                    arabic: 'سعد علي القحطاني',
                    english: 'Saad Ali Al-Qahtani',
                },
                phone: '+966-58-876-5432',
            },
        },
        {
            id: 7,
            name: {
                arabic: 'مركز أمني المدينة المنورة',
                english: 'Medina Security Center',
            },
            type: 1,
            entryType: 1,
            lat: 24.5247,
            long: 39.5692,
            contact: {
                name: {
                    arabic: 'نواف أحمد الأنصاري',
                    english: 'Nawaf Ahmed Al-Ansari',
                },
                phone: '+966-59-123-7890',
            },
        },
        {
            id: 8,
            name: {
                arabic: 'منطقة مشبوهة الباحة',
                english: 'Al-Baha Suspected Area',
            },
            type: 1,
            entryType: 3,
            lat: 20.0129,
            long: 41.4677,
            contact: {
                name: {
                    arabic: 'عبدالرحمن يوسف الزهراني',
                    english: 'Abdulrahman Youssef Al-Zahrani',
                },
                phone: '+966-57-567-8901',
            },
        },
    ],
    pagination: {
        pageSize: 8,
        currentPage: 1,
        totalPages: 1,
        totalCount: 8,
        hasPrevious: false,
        hasNext: false,
    },
};
