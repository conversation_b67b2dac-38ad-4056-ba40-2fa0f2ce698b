import { appConfig } from '@/shared/config/app-settings.config';
import { SignalrHubManager } from '@/shared/lib/SignalR/signalrHubManager';
import { SubscriptionManager } from '@/shared/lib/SignalR/subscriptionManager';
import type { ConnectionState, SubscriptionId } from '@/shared/lib/SignalR/types';

import { logger } from '../../logging';

import type { AlertMessage, LocationUpdateMessage } from './types';

export type TripsHubEvents = {
    ReceiveAllAlerts: AlertMessage;
    ReceiveAllLocationUpdates: LocationUpdateMessage;
    ConnectionStateChanged: ConnectionState;
};

export class TripsHub {
    public static instance: TripsHub;
    private hubManager: SignalrHubManager;
    private subManager: SubscriptionManager<TripsHubEvents>;

    private constructor() {
        TripsHub.instance = this;
        this.subManager = new SubscriptionManager<TripsHubEvents>();

        const hubUrl = `${appConfig.get('hubBaseUrl')}/hub/trips`;

        this.hubManager = new SignalrHubManager({
            hubUrl,
            onConnectionStateChange: (state: ConnectionState) => {
                this.subManager.broadcast('ConnectionStateChanged', state);
            },
        });
    }

    public static getInstance(): TripsHub {
        if (!TripsHub.instance) {
            TripsHub.instance = new TripsHub();
        }
        return TripsHub.instance;
    }

    // ================================ Connect ================================

    public async connect(): Promise<void> {
        try {
            await this.hubManager.connect((hub) => {
                hub.on('ReceiveAllAlerts', (alert: AlertMessage) => {
                    this.subManager.broadcast('ReceiveAllAlerts', alert);
                });
                hub.on('ReceiveAllLocationUpdates', (locationUpdate: LocationUpdateMessage) => {
                    this.subManager.broadcast('ReceiveAllLocationUpdates', locationUpdate);
                });
            });
        } catch (error) {
            logger.error('TripsHub: Connection failed', error as Error);
            throw error;
        }
    }

    // ================================ Disconnect ================================

    public disconnect(): void {
        this.hubManager.disconnect();
    }

    // ================================ Subscriptions ================================

    public subscribe<K extends keyof TripsHubEvents>(
        eventName: K,
        callback: (data: TripsHubEvents[K]) => void,
    ): SubscriptionId {
        return this.subManager.subscribe(eventName, callback);
    }

    public unsubscribe<K extends keyof TripsHubEvents>(eventName: K, subscriberId: SubscriptionId): boolean {
        return this.subManager.unsubscribe(eventName, subscriberId);
    }

    // ================================ Groups ================================
    public joinAllAlertsGroup(): Promise<unknown> {
        return this.hubManager.sendToHub('SubscribeToAllAlerts');
    }

    public leaveAllAlertsGroup(): Promise<unknown> {
        return this.hubManager.sendToHub('UnsubscribeFromAllAlerts');
    }

    public joinAllLocationUpdatesGroup(): Promise<unknown> {
        return this.hubManager.sendToHub('SubscribeToAllLocationUpdates');
    }

    public leaveAllLocationUpdatesGroup(): Promise<unknown> {
        return this.hubManager.sendToHub('UnsubscribeFromAllLocationUpdates');
    }

    // ================================ Dispose ================================
    public dispose(): void {
        this.disconnect();
        this.subManager.dispose();
        logger.info('TripsHub: Disposed all resources');
    }
}

export const tripsHub = TripsHub.getInstance();
