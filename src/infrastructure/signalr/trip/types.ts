import type z from 'zod';

import type {
    AlertMessagePayloadSchema,
    AlertMessageSchema,
    LocationUpdateMessagePayloadSchema,
    LocationUpdateMessageSchema,
} from './schemas';

export type AlertMessage = z.infer<typeof AlertMessageSchema>;
export type LocationUpdateMessage = z.infer<typeof LocationUpdateMessageSchema>;

export type AlertMessagePayload = z.infer<typeof AlertMessagePayloadSchema>;
export type LocationUpdateMessagePayload = z.infer<typeof LocationUpdateMessagePayloadSchema>;
