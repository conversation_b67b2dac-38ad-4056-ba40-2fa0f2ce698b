import { logger } from '@/infrastructure/logging';
import { valy } from '@/shared/lib/Valy';
import { fetchy } from '@/shared/lib/Fetchy';

import type { SendEmailRequest, SendEmailResponse } from './types';
import { SendEmailRequestSchema, SendEmailResponseSchema } from './schemas';

export class EmailService {
    private static instance: EmailService;

    private constructor() {}

    public static getInstance(): EmailService {
        if (!EmailService.instance) {
            EmailService.instance = new EmailService();
        }
        return EmailService.instance;
    }

    /**
     * Send email via backend
     * @param payload SendEmailRequest
     * @returns Promise<SendEmailResponse>
     */
    public async sendEmail(payload: SendEmailRequest): Promise<SendEmailResponse | null> {
        logger.info('[EmailService] Sending email with payload:', payload);

        try {
            // Validate request
            const validationResult = valy.validate(SendEmailRequestSchema, payload, 'sendEmailRequest');
            if (!validationResult.success) {
                logger.error(
                    '[EmailService] Invalid request payload',
                    new Error('validation error', { cause: validationResult.errors }),
                );
                return null;
            }

            // Call backend
            const response = await fetchy.post<SendEmailResponse>('emails', { body: payload });

            if (response.status !== 200) {
                logger.error(
                    '[EmailService] Invalid response from email API',
                    new Error(`API returned error status ${response.status}`),
                );
                return null;
            }

            // Validate response
            const responseValidation = valy.validate(SendEmailResponseSchema, response.data, 'sendEmailResponse');
            if (!responseValidation.success) {
                logger.error(
                    '[EmailService] Invalid response from email API',
                    new Error('validation error', { cause: responseValidation.errors }),
                );
                return null;
            }

            return responseValidation.data as SendEmailResponse;
        } catch (error: unknown) {
            logger.error('[EmailService] Error sending email:', error as Error);
            throw error;
        }
    }
}

export const emailService = EmailService.getInstance();
