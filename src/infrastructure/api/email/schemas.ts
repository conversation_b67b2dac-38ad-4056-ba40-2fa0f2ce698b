import { z } from 'zod';

import { Scope } from '@/shared/enums';

//-- Email validation regex: allows letters, numbers, and special chars (.$_!~) before @, domain name, and 2-4 char extension
const EMAIL_REGEX = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;

export const SendEmailRequestSchema = z.object({
    from: z.email().optional(),
    to: z.array(z.email({ pattern: EMAIL_REGEX, error: 'Please enter a valid email address' })).nonempty(),
    subject: z.string().min(1, 'Subject is required'),
    body: z.string().min(1, 'Body is required'),
    cc: z.array(z.email()).optional(),
    bcc: z.array(z.email()).optional(),
    scope: z.enum(Scope), // Trip=1, TripAlert=2
});

export const SendEmailResponseSchema = z.object({
    from: z.email().nullable(),
    to: z.array(z.email()).nonempty(),
});
