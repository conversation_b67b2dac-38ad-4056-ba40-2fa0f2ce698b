import { logger } from '@/infrastructure/logging';
import { valy } from '@/shared/lib/Valy';
import { fetchy } from '@/shared/lib/Fetchy';
import { DEFAULT_PAGINATION } from '@/infrastructure/validation/schemas/pagination.schema';

import type { GetMapPointsQueryParams, GetMapPointsResponse } from './types';
import { GetMapPointsResponseSchema } from './schemas';

export class MapPointService {
    private static instance: MapPointService;

    private constructor() {}

    public static getInstance(): MapPointService {
        if (!MapPointService.instance) {
            MapPointService.instance = new MapPointService();
        }
        return MapPointService.instance;
    }

    public async getMapPoints(params: GetMapPointsQueryParams): Promise<GetMapPointsResponse> {
        logger.info('[MapPointService] fetching map points with query params: ', params);

        try {
            const response = await fetchy.get<GetMapPointsResponse>('map-points', { params });
            // todo: cache using react-query

            const validationResult = valy.validate(GetMapPointsResponseSchema, response.data, 'map_points');
            if (validationResult.success === false) {
                logger.error(
                    '[MapPointService] Invalid response from map points API',
                    new Error('validation error', { cause: validationResult.errors }),
                );
                return {
                    data: [],
                    pagination: DEFAULT_PAGINATION,
                };
            }

            return validationResult.data as GetMapPointsResponse;
        } catch (error: unknown) {
            logger.error('[MapPointService] Error fetching map points: ', error as Error);
            throw error;
        }
    }
}
