import { z } from 'zod';

// Login request schema
export const loginRequestSchema = z.object({
    email: z.email('Please enter a valid email address'),
    password: z.string().min(1, 'Password is required'),
});

// Refresh request schema
export const refreshRequestSchema = z.object({
    refreshToken: z.string().min(1, 'Refresh token is required'),
});

// Auth response schema (for both login and refresh)
export const authResponseSchema = z.object({
    tokenType: z.string(),
    accessToken: z.string(),
    expiresIn: z.number(),
    refreshToken: z.string(),
});

// User profile schema
export const userProfileResponseSchema = z.object({
    id: z.number(),
    email: z.email(),
    userName: z.string(),
});
