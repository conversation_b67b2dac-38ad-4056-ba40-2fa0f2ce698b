import type { z } from 'zod';

import type {
    authResponseSchema,
    loginRequestSchema,
    refreshRequestSchema,
    userProfileResponseSchema,
} from './schemas';

export type LoginRequest = z.infer<typeof loginRequestSchema>;
export type RefreshRequest = z.infer<typeof refreshRequestSchema>;
export type AuthResponse = z.infer<typeof authResponseSchema>;
export type UserProfileResponse = z.infer<typeof userProfileResponseSchema>;
