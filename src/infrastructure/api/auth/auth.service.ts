import i18n from 'i18next';

import { logger } from '@/infrastructure/logging';
import { valy } from '@/shared/lib/Valy';
import { fetchy } from '@/shared/lib/Fetchy';

import { loginRequestSchema, refreshRequestSchema, authResponseSchema, userProfileResponseSchema } from './schemas';
import type { LoginRequest, RefreshRequest, AuthResponse, UserProfileResponse } from './types';

export class AuthService {
    private static instance: AuthService;

    private constructor() {}

    public static getInstance(): AuthService {
        if (!AuthService.instance) {
            AuthService.instance = new AuthService();
        }
        return AuthService.instance;
    }

    public async login(credentials: LoginRequest): Promise<AuthResponse> {
        logger.info('[AuthService] attempting login', { email: credentials.email });

        try {
            // Validate input
            const validationResult = valy.validate(loginRequestSchema, credentials, 'login_request');
            if (!validationResult.success) {
                throw new Error(i18n.t('login.invalidCredentials'));
            }

            const response = await fetchy.post<AuthResponse>('/auth/login', {
                body: credentials,
            });

            // Validate response
            const responseValidation = valy.validate(authResponseSchema, response.data, 'auth_response');
            if (!responseValidation.success) {
                logger.error('[AuthService] Invalid response from login API', new Error('validation error'));
                throw new Error(i18n.t('login.invalidResponse'));
            }

            logger.info('[AuthService] login successful');
            return responseValidation.data as AuthResponse;
        } catch (error: unknown) {
            logger.error('[AuthService] Error during login:', error as Error);
            throw error;
        }
    }

    public async refreshToken(refreshData: RefreshRequest): Promise<AuthResponse> {
        logger.info('[AuthService] attempting token refresh');

        try {
            // Validate input
            const validationResult = valy.validate(refreshRequestSchema, refreshData, 'refresh_request');
            if (!validationResult.success) {
                throw new Error(i18n.t('login.invalidRefreshToken'));
            }

            const response = await fetchy.post<AuthResponse>('/auth/refresh', {
                body: refreshData,
            });

            // Validate response
            const responseValidation = valy.validate(authResponseSchema, response.data, 'auth_response');
            if (!responseValidation.success) {
                logger.error('[AuthService] Invalid response from refresh API', new Error('validation error'));
                throw new Error(i18n.t('login.invalidResponse'));
            }

            logger.info('[AuthService] token refresh successful');
            return responseValidation.data as AuthResponse;
        } catch (error: unknown) {
            logger.error('[AuthService] Error during token refresh:', error as Error);
            throw error;
        }
    }

    public async getUserProfile(): Promise<UserProfileResponse> {
        logger.info('[AuthService] fetching user profile');

        try {
            const response = await fetchy.get<UserProfileResponse>('/auth/profile');

            // Validate response
            const validationResult = valy.validate(userProfileResponseSchema, response.data, 'user_profile');
            if (!validationResult.success) {
                logger.error('[AuthService] Invalid response from user profile API', new Error('validation error'));
                throw new Error(i18n.t('login.invalidUserProfile'));
            }

            logger.info('[AuthService] user profile fetched successfully');
            return validationResult.data as UserProfileResponse;
        } catch (error: unknown) {
            logger.error('[AuthService] Error fetching user profile:', error as Error);
            throw error;
        }
    }
}

export const authService = AuthService.getInstance();
