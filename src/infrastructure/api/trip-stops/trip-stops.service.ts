import { logger } from '@/infrastructure/logging';
import { valy } from '@/shared/lib/Valy';
import { fetchy } from '@/shared/lib/Fetchy';
import { DEFAULT_PAGINATION } from '@/infrastructure/validation/schemas/pagination.schema';

import type { GetTripStopsQueryParams, TripStopsResponse } from './types';
import { TripStopsResponseSchema } from './schemas';

export class TripStopsService {
    private static instance: TripStopsService;

    private constructor() {}

    public static getInstance(): TripStopsService {
        if (!TripStopsService.instance) {
            TripStopsService.instance = new TripStopsService();
        }
        return TripStopsService.instance;
    }

    public async getTripStops(params: GetTripStopsQueryParams): Promise<TripStopsResponse> {
        logger.info('[TripStopsService] fetching trip stops with params: ', params);
        try {
            const response = await fetchy.get<TripStopsResponse>(`trips/${params.tripId}/stops`);

            const validationResult = valy.validate(TripStopsResponseSchema, response.data, 'trip_stops');
            if (validationResult.success === false) {
                logger.error(
                    '[TripStopsService] Invalid response from trip stops API',
                    new Error('validation error', { cause: validationResult.errors }),
                );
                return {
                    data: [],
                    pagination: DEFAULT_PAGINATION,
                };
            }

            return validationResult.data as TripStopsResponse;
        } catch (error: unknown) {
            logger.error('[TripStopsService] Error fetching trip stops: ', error as Error);
            throw error;
        }
    }
}

export const tripStopsService = TripStopsService.getInstance();
