import type { z } from 'zod';

import type { PeriodSchema, TripStopSchema, TripStopsResponseSchema, GetTripStopsQueryParamsSchema } from './schemas';

export type Period = z.infer<typeof PeriodSchema>;
export type TripStop = z.infer<typeof TripStopSchema>;
export type TripStopsResponse = z.infer<typeof TripStopsResponseSchema>;
export type GetTripStopsQueryParams = z.infer<typeof GetTripStopsQueryParamsSchema>;
