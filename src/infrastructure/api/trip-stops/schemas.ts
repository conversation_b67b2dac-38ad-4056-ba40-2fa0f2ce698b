import { z } from 'zod';

import { PaginationSchema } from '@/infrastructure/validation/schemas/pagination.schema';
import { TranslatableSchema } from '@/infrastructure/validation/schemas/translatable.schema';

export const PeriodSchema = z.object({
    hours: z.number(),
    minutes: z.number(),
});

export const TripStopSchema = z.object({
    id: z.number(),
    fromTime: z.string(),
    toTime: z.string(),
    address: TranslatableSchema,
    period: PeriodSchema,
});

export const TripStopsResponseSchema = z.object({
    data: z.array(TripStopSchema),
    pagination: PaginationSchema,
});

export const GetTripStopsQueryParamsSchema = z.object({
    tripId: z.number(), // Only tripId needed for URL path
});
