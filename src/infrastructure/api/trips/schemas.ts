import { z } from 'zod';

import { PaginationSchema } from '@/infrastructure/validation/schemas/pagination.schema';
import { TranslatableSchema } from '@/infrastructure/validation/schemas/translatable.schema';
import {
    TripLocation,
    TrackingStatus,
    TripStatus,
    OrderDirection,
    TripFilterOrderBy,
    TripCategory,
    AlertAcknowledgement,
    PortType,
} from '@/shared/enums';

import { AlertTypeSchema } from '../alert-types/schemas';

export const TripStatusSchema = z.enum(TripStatus);
export const RouteZoneSchema = z.enum(TripLocation);
export const TrackingStatusSchema = z.enum(TrackingStatus);

export const TrackerDtoSchema = z.object({
    id: z.coerce.number(),
    serialNumber: z.string(),
});

export const VehicleDtoSchema = z.object({
    id: z.coerce.number(),
    plateNo: z.string().nullable(),
    model: z.string().nullable(),
    type: z.string().nullable(),
    color: z.string().nullable(),
    plateCountryName: z.string().nullable(),
    plateCountryCode: z.string().nullable(),
});

export const DriverSchema = z.object({
    id: z.coerce.number(),
    name: z.string().nullable(),
    mobileNo: z.string().nullable(),
    passportCountry: z.string().nullable(),
    passportNumber: z.string().nullable(),
});

export const PortTypeSchema = z.enum(PortType);

export const PortDtoSchema = z.object({
    id: z.coerce.number(),
    type: PortTypeSchema,
    name: TranslatableSchema,
});

export const AlertTypeDtoSchema = z.object({
    id: z.coerce.number(),
    name: TranslatableSchema,
});

export const AlertDtoSchema = z.object({
    id: z.coerce.number(),
    type: AlertTypeDtoSchema,
});

export const ELockSchema = z.object({
    deviceId: z.coerce.number(),
    tripELockId: z.coerce.number(),
    serialNumber: z.string(),
});

export const TripStateSchema = z.object({
    trackerDateTime: z.coerce.date().transform((date) => date.toISOString()), // Coerce to Date then transform to ISO string
    locationSource: z.coerce.number(),
    batteryLevelPercentage: z.coerce.number(),
    chargerStatus: z.coerce.number(),
    gpsSignalStrength: z.coerce.number(),
    gsmSignalStrength: z.coerce.number(),
    currentSpeed: z.coerce.number(),
    routeZone: z.coerce.number(),
    isWithinRouteGeofence: z.coerce.boolean(),
    timeElapsedSinceTripStartInMinutes: z.coerce.number(),
    remainingDistanceInMeters: z.coerce.number(),
    completeDistanceInMeters: z.coerce.number(),
    isWithinSuspiciousZone: z.coerce.boolean(),
    createdAt: z.coerce.date().transform((date) => date.toISOString()),
    lat: z.number(),
    long: z.number(),
    address: TranslatableSchema,
});

// Main data item schema
export const TripsItemSchema = z.object({
    id: z.coerce.number(),
    transitNumber: z.coerce.number().nullable(),
    status: TripStatusSchema.nullable().default(null),
    trackingStatus: TrackingStatusSchema.nullable().default(null),
    vehicle: VehicleDtoSchema,
    driver: DriverSchema,
    routeId: z.coerce.number(),
    entryPort: PortDtoSchema,
    exitPort: PortDtoSchema,
    startDate: z.coerce.date().transform((date) => date.toISOString()),
    endDate: z.coerce
        .date()
        .transform((date) => date.toISOString())
        .nullable(),
    tracker: TrackerDtoSchema,
    transitDate: z.string().nullable(),
    transSeq: z.string().nullable(),
    shipmentDescription: z.string().nullable(),
    newShipmentDescription: z.string().nullable(),
    transitTypeName: z.string().nullable(),
    ownerDesc: z.string().nullable(),
    activeAlerts: z.array(AlertDtoSchema),
    currentState: TripStateSchema.nullable(),
    eLocks: z.array(ELockSchema),
    securityNotes: z.string().nullable(),
    isFocused: z.boolean(),
    isSuspicious: z.boolean(),
});

// Final response schema
export const TripsApiResponseSchema = z.object({
    data: z.array(TripsItemSchema),
    pagination: PaginationSchema,
});

// ==================

export const TripLocationRequestSchema = z.object({
    inPorts: z.array(z.number()).default([]),
    outPorts: z.array(z.number()).default([]),
    alertTypes: z.array(z.number()).default([]),
    activeAlertsOnly: z.boolean().nullable().default(null),
    transitNumber: z.number().nullable().default(null),
    transitSeqNumber: z.string().nullable().default(null),
    driverName: z.string().nullable().default(null),
    driverNationality: z.string().nullable().default(null),
    driverMobileNo: z.string().nullable().default(null),
    driverPassportNumber: z.string().nullable().default(null),
    plateNumber: z.string().nullable().default(null),
    trackerNumber: z.string().nullable().default(null),
    tipCode: z.string().nullable().default(null),
    tripCategory: z.enum(TripCategory).nullable().default(null),
    tripLocations: z.array(z.enum(TripLocation)).default([]),
    activeTripsOnly: z.boolean().nullable().default(null),
    tripStartDate: z.date().nullable().default(null),
    tripEndDate: z.date().nullable().default(null),
    transDate: z.date().nullable().default(null),
    orderBy: z.enum(TripFilterOrderBy).nullable().default(null),
    orderDir: z.enum(OrderDirection).nullable().default(null),
    alertAcknowledgement: AlertAcknowledgement,
    pageSize: z.number().min(1).default(1000),
    pageNumber: z.number().min(1).default(1),
});

export const TripLocationSchema = z.object({
    id: z.number(),
    location: z.object({
        latitude: z.number(),
        longitude: z.number(),
    }),
    bearing: z.number(),
});

export const TripLocationResponseSchema = z.object({
    data: z.array(TripLocationSchema),
    pagination: PaginationSchema,
});

// ==================

export const PortSchema = z.object({
    id: z.number(),
    name: TranslatableSchema,
});

export const StateChangeSchema = z.object({
    stateId: z.number(),
    timestamp: z.string(),
});

export const AlertSchema = z.object({
    alertStateChangeId: z.number(),
    alertType: AlertTypeSchema,
    fromState: StateChangeSchema,
    toState: StateChangeSchema.nullable(),
});

export const GetTripDetailQueryParamsSchema = z.object({
    id: z.number(),
});
export const TripDetailSchema = z.object({
    id: z.number(),
    transitNumber: z.number(),
    status: z.string(),
    trackingStatus: z.string(),
    vehicle: z.object({
        id: z.number(),
        plateNo: z.string(),
    }),
    driver: z.object({
        id: z.number(),
        name: z.string(),
        mobileNo: z.string(),
        passportCountry: z.string(),
        passportNumber: z.string(),
    }),
    routeId: z.number().nullable(),
    entryPort: PortSchema.nullable(),
    exitPort: PortSchema.nullable(),
    startDate: z.string(),
    endDate: z.string().nullable(),
    tracker: z.object({
        id: z.number(),
        serialNumber: z.string(),
    }),
    eLocks: z.array(
        z.object({
            deviceId: z.number(),
            serialNumber: z.string(),
        }),
    ),
    trackingPriority: z.string(),
    transitDate: z.string(),
    transSeq: z.string(),
    shipmentDescription: z.string().nullable(),
    newShipmentDescription: z.string().nullable(),
    transitTypeName: z.string(),
    ownerDesc: z.string(),
    ownerType: z.string(),
    activeAlerts: z.array(AlertSchema),
    currentState: TripStateSchema.nullable(),
    securityNotes: z.string().nullable(),
    isFocused: z.boolean(),
    isSuspicious: z.boolean(),
});

// ==================
// Stats
export const TripsStatsResponseSchema = z.object({
    totalActiveTrips: z.number(),
    totalActiveTripsWithActiveAlerts: z.number(),
    totalActiveTripsWithoutActiveAlerts: z.number(),
    totalInActiveTrips: z.number(),
});
