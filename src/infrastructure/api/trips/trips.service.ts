import { logger } from '@/infrastructure/logging';
import { valy } from '@/shared/lib/Valy';
import { fetchy } from '@/shared/lib/Fetchy';
import { DEFAULT_PAGINATION } from '@/infrastructure/validation/schemas/pagination.schema';

import { TripsApiResponseSchema, TripsStatsResponseSchema } from './schemas';
import type { TripsRequest, TripsResponse, TripsStatsResponse } from './types';
import { TripLocationResponseSchema } from './schemas';
import type { TripLocationRequest, TripLocationResponse } from './types';
import { TripDetailSchema } from './schemas';
import type { GetTripDetailQueryParams, TripDetail } from './types';

export class TripsService {
    private static instance: TripsService;

    private constructor() {}

    public static getInstance(): TripsService {
        if (!TripsService.instance) {
            TripsService.instance = new TripsService();
        }
        return TripsService.instance;
    }

    public async getTrips(params: TripsRequest): Promise<TripsResponse> {
        logger.info('[TripsService] fetching trips with params: ', params);

        try {
            const response = await fetchy.post<TripsResponse>('/trips', { body: params });
            // todo cache using react-query

            const validationResult = valy.validate(TripsApiResponseSchema, response.data, 'trips_response');
            if (validationResult.success === false) {
                logger.error(
                    '[TripsService] Invalid response from trips API',
                    new Error('validation error', { cause: validationResult.errors }),
                );
                return { data: [], pagination: DEFAULT_PAGINATION };
            }

            return validationResult.data as TripsResponse;
        } catch (error: unknown) {
            logger.error('[TripsService] Error fetching trips: ', error as Error);
            throw error;
        }
    }

    public async getTripLocations(params: TripLocationRequest): Promise<TripLocationResponse | null> {
        logger.info('[TripsService] fetching trip locations with params: ', params);

        try {
            const response = await fetchy.post<TripLocationResponse>('/trips/location', { body: params });
            // todo cache using react-query

            if (response.status !== 200) {
                logger.error(
                    '[TripsService] Invalid response from trip locations API',
                    new Error(`api return with error status ${response.status}`),
                );
                return null;
            }

            const validationResult = valy.validate(TripLocationResponseSchema, response.data, 'trip_location_response');
            if (validationResult.success === false) {
                logger.error(
                    '[TripsService] Invalid response from trip locations API',
                    new Error('validation error', { cause: validationResult.errors }),
                );
                return null;
            }

            return validationResult.data as TripLocationResponse;
        } catch (error: unknown) {
            logger.error('[TripsService] Error fetching trip locations: ', error as Error);
            throw error;
        }
    }

    public async getTripDetail(params: GetTripDetailQueryParams): Promise<TripDetail | null> {
        logger.info('[TripsService] fetching trip detail with ID: ', params);

        try {
            const response = await fetchy.get<TripDetail>(`trips/${params.id}`);

            const validationResult = valy.validate(TripDetailSchema, response.data, 'trip_detail');
            if (validationResult.success === false) {
                logger.error(
                    '[TripsService] Invalid response from trip detail API',
                    new Error('validation error', { cause: validationResult.errors }),
                );
                return null;
            }

            return validationResult.data as TripDetail;
        } catch (error: unknown) {
            logger.error('[TripsService] Error fetching trip detail: ', error as Error);
            throw error;
        }
    }

    // ==================
    public async endTrip(tripId: number, payload: { isEnded: boolean; reason: string }): Promise<void> {
        logger.info('[TripsService] ending trip with ID: ', { tripId });

        try {
            const response = await fetchy.patch<void>(`/trips/${tripId}`, { body: payload });

            if (![204].includes(response.status)) {
                throw new Error(`endTrip failed with status ${response.status}`);
            }
        } catch (error) {
            logger.error('[TripsService] Error ending trip: ', error as Error);
            throw error;
        }
    }

    public async markTripAsFocused(tripId: number, payload: { isFocused: boolean }): Promise<void> {
        logger.info('[TripsService] marking trip as focused with ID: ', { tripId });

        try {
            const response = await fetchy.patch<void>(`/trips/${tripId}`, { body: payload });

            if (![204].includes(response.status)) {
                throw new Error(`markTripAsFocused failed with status ${response.status}`);
            }
        } catch (error) {
            logger.error('[TripsService] Error marking trip as focused: ', error as Error);
            throw error;
        }
    }

    public async markTripAsSuspicious(tripId: number, payload: { isSuspicious: boolean }): Promise<void> {
        logger.info('[TripsService] marking trip as suspicious with ID: ', { tripId });

        try {
            const response = await fetchy.patch<void>(`/trips/${tripId}`, { body: payload });

            if (![204].includes(response.status)) {
                throw new Error(`markTripAsSuspicious failed with status ${response.status}`);
            }
        } catch (error) {
            logger.error('[TripsService] Error marking trip as suspicious: ', error as Error);
            throw error;
        }
    }

    public async updateSecurityNotes(tripId: number, securityNotes: string | null): Promise<void> {
        logger.info('[TripsService] updating security notes for trip ID: ', { tripId, securityNotes });

        try {
            const response = await fetchy.patch<void>(`/trips/${tripId}`, {
                body: { SecurityNotes: securityNotes },
            });

            if (![204].includes(response.status)) {
                throw new Error(`updateSecurityNotes failed with status ${response.status}`);
            }
        } catch (error) {
            logger.error('[TripsService] Error updating security notes: ', error as Error);
            throw error;
        }
    }

    // ==================
    // Stats

    public async getTripsStats(): Promise<TripsStatsResponse | null> {
        logger.info('[TripsService] fetching trips stats');

        try {
            const response = await fetchy.get<TripsStatsResponse>(`trips/stats`);

            const validationResult = valy.validate(TripsStatsResponseSchema, response.data, 'trips_stats');
            if (validationResult.success === false) {
                logger.error(
                    '[TripsService] Invalid response from trips stats API',
                    new Error('validation error', { cause: validationResult.errors }),
                );
                return null;
            }

            return validationResult.data as TripsStatsResponse;
        } catch (error: unknown) {
            logger.error('[TripsService] Error fetching trips stats: ', error as Error);
            throw error;
        }
    }
}

export const tripsService = TripsService.getInstance();
