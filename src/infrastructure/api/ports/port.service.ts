import { logger } from '@/infrastructure/logging';
import { valy } from '@/shared/lib/Valy';
import { fetchy } from '@/shared/lib/Fetchy';
import { DEFAULT_PAGINATION } from '@/infrastructure/validation/schemas/pagination.schema';

import type { GetPortsQueryParams, GetPortsApiResponse, GetPortByIdApiResponse } from './types';
import { GetPortByIdResponseSchema, GetPortsApiResponseSchema } from './schemas';

export class PortService {
    private static instance: PortService;

    private constructor() {}

    public static getInstance(): PortService {
        if (!PortService.instance) {
            PortService.instance = new PortService();
        }
        return PortService.instance;
    }

    public async getPorts(params: GetPortsQueryParams): Promise<GetPortsApiResponse> {
        logger.info('[PortService] fetching ports with query params: ', params);

        try {
            const response = await fetchy.get<GetPortsApiResponse>('ports', { params });
            // todo: cache using react-query

            const validationResult = valy.validate(GetPortsApiResponseSchema, response.data, 'ports');
            if (validationResult.success === false) {
                logger.error(
                    '[PortService] Invalid response from ports API',
                    new Error('validation error', { cause: validationResult.errors }),
                );
                return {
                    data: [],
                    pagination: DEFAULT_PAGINATION,
                };
            }

            return validationResult.data as GetPortsApiResponse;
        } catch (error: unknown) {
            logger.error('[PortService] Error fetching ports: ', error as Error);
            throw error;
        }
    }

    public async getPortById(portId: number): Promise<GetPortByIdApiResponse | null> {
        const response = await fetchy.get<GetPortByIdApiResponse>(`ports/${portId}`);

        if (response.status !== 200) {
            logger.error(
                '[PortService] Invalid response from port API',
                new Error(`API returned with error status ${response.status}`),
            );
            return null;
        }
        const validationResult = valy.validate(GetPortByIdResponseSchema, response.data, 'port');
        if (validationResult.success === false) {
            logger.error(
                '[PortService] Invalid response from port API',
                new Error('validation error', { cause: validationResult.errors }),
            );
            return null;
        }

        return validationResult.data as GetPortByIdApiResponse;
    }
}

export const portService = PortService.getInstance();
