import { logger } from '@/infrastructure/logging';
import { valy } from '@/shared/lib/Valy';
import { fetchy } from '@/shared/lib/Fetchy';

import { PortStatisticsResponseSchema } from './schemas';
import type { PortStatisticsRequest, PortStatisticsResponse, PortStatisticsErrorResponse } from './types';

export class PortStatisticsService {
    private static instance: PortStatisticsService;

    private constructor() {}

    public static getInstance(): PortStatisticsService {
        if (!PortStatisticsService.instance) {
            PortStatisticsService.instance = new PortStatisticsService();
        }
        return PortStatisticsService.instance;
    }

    public async getPortStatistics(params: PortStatisticsRequest): Promise<PortStatisticsResponse | null> {
        logger.info('[PortStatisticsService] fetching port statistics with params: ', params);

        try {
            const response = await fetchy.get<PortStatisticsResponse | PortStatisticsErrorResponse>(
                `/ports/${params.portId}/statistics`,
            );

            if (response.status === 404) {
                logger.warn('[PortStatisticsService] Port not found', { portId: params.portId });
                return null;
            }

            if (response.status !== 200) {
                logger.error(
                    '[PortStatisticsService] Invalid response from port statistics API',
                    new Error(`API returned with error status ${response.status}`),
                );
                return null;
            }

            const validationResult = valy.validate(
                PortStatisticsResponseSchema,
                response.data,
                'port_statistics_response',
            );
            if (validationResult.success === false) {
                logger.error(
                    '[PortStatisticsService] Invalid response from port statistics API',
                    new Error('validation error', { cause: validationResult.errors }),
                );
                return null;
            }

            return validationResult.data as PortStatisticsResponse;
        } catch (error: unknown) {
            logger.error('[PortStatisticsService] Error fetching port statistics: ', error as Error);
            throw error;
        }
    }
}

export const portStatisticsService = PortStatisticsService.getInstance();
