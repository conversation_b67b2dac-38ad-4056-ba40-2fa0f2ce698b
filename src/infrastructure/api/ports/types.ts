import type { z } from 'zod';

import type { GetPortByIdResponseSchema, GetPortsQueryParamsSchema } from './schemas';
import type { GetPortsApiResponseSchema } from './schemas';
import type { DataItemSchema } from './schemas';

/** Type inferred from schema */
export type GetPortsQueryParams = z.infer<typeof GetPortsQueryParamsSchema>;

export type GetPortsApiResponse = z.infer<typeof GetPortsApiResponseSchema>;
export type GetPortByIdApiResponse = z.infer<typeof GetPortByIdResponseSchema>;
export type PortItem = z.infer<typeof DataItemSchema>;
