import { logger } from '@/infrastructure/logging';
import { valy } from '@/shared/lib/Valy';
import { fetchy } from '@/shared/lib/Fetchy';
import { DEFAULT_PAGINATION } from '@/infrastructure/validation/schemas/pagination.schema';

import { usersResponseSchema, usersStatsResponseSchema } from './schemas';
import type { GetUsersQueryParams, UsersResponse, UsersStatsResponse } from './types';

export class UsersService {
    private static instance: UsersService;

    private constructor() {}

    public static getInstance(): UsersService {
        if (!UsersService.instance) {
            UsersService.instance = new UsersService();
        }
        return UsersService.instance;
    }

    public async getUsers(params?: Partial<GetUsersQueryParams>): Promise<UsersResponse> {
        logger.info('[UsersService] fetching user list with params:', params ?? {});

        try {
            const response = await fetchy.get<UsersResponse>(`users`, { params });
            const validationResult = valy.validate(usersResponseSchema, response.data, 'users');
            if (validationResult.success === false) {
                logger.error(
                    '[UsersService] Invalid response from users API',
                    new Error('validation error', { cause: validationResult.errors }),
                );
                return {
                    data: [],
                    pagination: DEFAULT_PAGINATION,
                };
            }
            return validationResult.data as UsersResponse;
        } catch (error) {
            logger.error('[UsersService] getUsers error', error as Error);
            throw error;
        }
    }

    public async getStats(): Promise<UsersStatsResponse> {
        logger.info('[UsersService] fetching user stats');

        try {
            const response = await fetchy.get<UsersStatsResponse>('users/stats');
            const validationResult = valy.validate(usersStatsResponseSchema, response.data, 'users_stats');
            if (validationResult.success === false) {
                logger.error(
                    '[UsersService]] Invalid response from users stats API',
                    new Error('validation error', { cause: validationResult.errors }),
                );
                return {
                    totalUsers: 0,
                    totalActiveUsers: 0,
                    totalInActiveUsers: 0,
                };
            }

            return validationResult.data as UsersStatsResponse;
        } catch (error) {
            logger.error('[UsersService] getStats error', error as Error);
            throw error;
        }
    }
}

export const usersService = UsersService.getInstance();
