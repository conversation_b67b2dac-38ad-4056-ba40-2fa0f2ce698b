import type { z } from 'zod';

import type {
    getUsersQuerySchema,
    userSchema,
    userRoleSchema,
    usersResponseSchema,
    usersStatsResponseSchema,
} from './schemas';

export type GetUsersQueryParams = z.infer<typeof getUsersQuerySchema>;
export type User = z.infer<typeof userSchema>;
export type UserRole = z.infer<typeof userRoleSchema>;
export type UsersResponse = z.infer<typeof usersResponseSchema>;
export type UsersStatsResponse = z.infer<typeof usersStatsResponseSchema>;
