import { z } from 'zod';

import { PaginationSchema } from '@/infrastructure/validation/schemas/pagination.schema';

// ------------------------------------
// ----------- Request Schemas --------
// ------------------------------------
export const getUsersQuerySchema = z.object({
    q: z.string().nullable(),
    isActive: z.boolean().nullable(),
    pageSize: z.number().min(1).default(10),
    pageNumber: z.number().min(1).default(1),
});

// ------------------------------------
// ----------- Response Schemas -------
// ------------------------------------
export const userRoleSchema = z.object({
    id: z.number(),
    name: z.string(),
});

export const userSchema = z.object({
    id: z.number(),
    username: z.string(),
    email: z.string().nullable(),
    phone: z.string().nullable(),
    roles: z.array(userRoleSchema),
    isActive: z.boolean(),
});

export const usersResponseSchema = z.object({
    data: z.array(userSchema),
    pagination: PaginationSchema,
});

// ==================
// Stats
export const usersStatsResponseSchema = z.object({
    totalUsers: z.number().default(0),
    totalActiveUsers: z.number().default(0),
    totalInActiveUsers: z.number().default(0),
});
