import { valy } from '@/shared/lib/Valy';
import { fetchy } from '@/shared/lib/Fetchy';
import { DEFAULT_PAGINATION } from '@/infrastructure/validation/schemas/pagination.schema';
import { logger } from '@/infrastructure/logging';

import type { ActivityLogsResponse, GetActivityLogsQueryParams } from './types';
import { ActivityLogsResponseSchema, GetActivityLogsQueryParamsSchema } from './schemas';

export class ActivityLogsService {
    private static instance: ActivityLogsService;
    private constructor() {}
    public static getInstance(): ActivityLogsService {
        if (!ActivityLogsService.instance) ActivityLogsService.instance = new ActivityLogsService();
        return ActivityLogsService.instance;
    }

    /** Public API */
    public async getActivityLogs(params: GetActivityLogsQueryParams): Promise<ActivityLogsResponse> {
        // Validate incoming params (and apply defaults)
        const vParams = valy.validate(GetActivityLogsQueryParamsSchema, params, 'get_activity_logs_params');
        const safeParams = vParams.success
            ? (vParams.data as GetActivityLogsQueryParams)
            : { pageNumber: 1, pageSize: 20 };

        try {
            // Only include defined filters
            const q = this.pickDefined({
                pageSize: safeParams.pageSize,
                pageNumber: safeParams.pageNumber,
                tripId: safeParams.tripId,
                activityId: safeParams.activityId,
                statusId: safeParams.statusId,
                actionId: safeParams.actionId,
                isDeleted: safeParams.isDeleted,
            });

            const res = await fetchy.get<ActivityLogsResponse>('activities/logs', { params: q });

            const vRes = valy.validate(ActivityLogsResponseSchema, res.data, 'activity_logs_response');
            if (!vRes.success) return { data: [], pagination: DEFAULT_PAGINATION };
            return vRes.data as ActivityLogsResponse;
        } catch (error) {
            // In case of unexpected shape or network error, keep consumer safe
            logger.error('[ActivityLogsService] Error fetching activity logs: ', error as Error);
            return { data: [], pagination: DEFAULT_PAGINATION };
        }
    }

    /** Helper: remove undefined keys so the backend gets clean params */
    private pickDefined<T extends Record<string, unknown>>(obj: T): Partial<T> {
        const out: Partial<T> = {};
        for (const [k, v] of Object.entries(obj)) {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            if (v !== undefined) (out as any)[k] = v;
        }
        return out;
    }
}

export const activityLogsService = ActivityLogsService.getInstance();
