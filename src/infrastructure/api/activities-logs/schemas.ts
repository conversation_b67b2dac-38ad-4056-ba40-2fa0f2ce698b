import { z } from 'zod';

import { PaginationSchema } from '@/infrastructure/validation/schemas/pagination.schema';

/** Shared (reused) */
export const LocalizedNameSchema = z.object({
    arabic: z.string(),
    english: z.string(),
});

/** Snapshots (before/after) */
export const ActivitySnapshotSchema = z.object({
    status: LocalizedNameSchema,
    action: LocalizedNameSchema,
    details: z.string().nullable(),
    note: z.string().nullable(),
});

/** Actor */
export const ActivityUserSchema = z.object({
    id: z.number(),
    name: z.string(),
});

/** Log Row */
export const ActivityLogItemSchema = z.object({
    id: z.number(),
    actionType: z.string(), // e.g., "Delete" (keep flexible for "Create"/"Update")
    user: ActivityUserSchema,
    isDeleted: z.boolean(),
    deleteReason: z.string().optional().nullable(), // present mainly when isDeleted = true
    timestamp: z.string(), // ISO
    tripId: z.number(),
    before: ActivitySnapshotSchema.optional().nullable(),
    after: ActivitySnapshotSchema.optional().nullable(),
});

/** Response */
export const ActivityLogsResponseSchema = z.object({
    pagination: PaginationSchema,
    data: z.array(ActivityLogItemSchema),
});

/** Query Params */
export const GetActivityLogsQueryParamsSchema = z.object({
    pageSize: z.number().optional().default(20),
    pageNumber: z.number().optional().default(1),
    activityId: z.number().optional(),
    tripId: z.number().optional(),
    statusId: z.number().optional(),
    actionId: z.number().optional(),
    isDeleted: z.boolean().optional(),
});
