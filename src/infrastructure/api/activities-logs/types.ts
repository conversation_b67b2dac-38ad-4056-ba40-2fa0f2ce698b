// features/activity-logs/types.ts
import type { z } from 'zod';

import type {
    LocalizedNameSchema,
    ActivitySnapshotSchema,
    ActivityUserSchema,
    ActivityLogItemSchema,
    ActivityLogsResponseSchema,
    GetActivityLogsQueryParamsSchema,
} from './schemas';

export type LocalizedName = z.infer<typeof LocalizedNameSchema>;

export type ActivitySnapshot = z.infer<typeof ActivitySnapshotSchema>;
export type ActivityUser = z.infer<typeof ActivityUserSchema>;

export type ActivityLogItem = z.infer<typeof ActivityLogItemSchema>;
export type ActivityLogsResponse = z.infer<typeof ActivityLogsResponseSchema>;

export type GetActivityLogsQueryParams = z.infer<typeof GetActivityLogsQueryParamsSchema>;
