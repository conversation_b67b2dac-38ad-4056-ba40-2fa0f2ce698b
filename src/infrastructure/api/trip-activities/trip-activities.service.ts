import { valy } from '@/shared/lib/Valy';
import { fetchy } from '@/shared/lib/Fetchy';
import { DEFAULT_PAGINATION } from '@/infrastructure/validation/schemas/pagination.schema';

import type {
    GetTripActivitiesQueryParams,
    TripActivitiesResponse,
    CreateTripActivityRequest,
    CreateTripActivityResponse,
    DeleteTripActivityRequest,
    EditTripActivityRequest,
} from './types';
import {
    TripActivitiesResponseSchema,
    CreateTripActivityRequestSchema,
    CreateTripActivityResponseSchema,
    DeleteTripActivityRequestSchema,
    EditTripActivityRequestSchema,
} from './schemas';

export class TripActivitiesService {
    private static instance: TripActivitiesService;
    private constructor() {}
    public static getInstance(): TripActivitiesService {
        if (!TripActivitiesService.instance) TripActivitiesService.instance = new TripActivitiesService();
        return TripActivitiesService.instance;
    }

    // List
    public async getTripActivities(params: GetTripActivitiesQueryParams): Promise<TripActivitiesResponse> {
        try {
            const response = await fetchy.get<TripActivitiesResponse>(`trips/${params.tripId}/activities`, {
                params: { pageNumber: params.pageNumber, pageSize: params.pageSize },
            });
            const v = valy.validate(TripActivitiesResponseSchema, response.data, 'trip_activities');
            if (!v.success) return { data: [], pagination: DEFAULT_PAGINATION };
            return v.data as TripActivitiesResponse;
        } catch (error) {
            throw error;
        }
    }

    // Create
    // ...
    // trip-activities.service.ts
    public async createTripActivity(
        tripId: number,
        payload: CreateTripActivityRequest,
    ): Promise<CreateTripActivityResponse> {
        const vReq = valy.validate(CreateTripActivityRequestSchema, payload, 'create_trip_activity_request');
        if (!vReq.success) throw new Error('Invalid create activity payload');

        try {
            const res = await fetchy.post<CreateTripActivityResponse>(`trips/${tripId}/activities`, {
                body: JSON.stringify({
                    statusId: payload.statusId,
                    actionId: payload.actionId,
                    details: payload.details,
                    // only include notes if defined
                    ...(payload.notes ? { notes: payload.notes } : {}),
                }),
                headers: {
                    'Content-Type': 'application/json; charset=utf-8',
                    Accept: 'application/json',
                },
            });

            const vRes = valy.validate(CreateTripActivityResponseSchema, res.data, 'create_trip_activity');
            if (!vRes.success) throw new Error('Invalid create activity response');

            return vRes.data as CreateTripActivityResponse;
        } catch {
            throw new Error('Failed to create activity');
        }
    }
    // ...
    public async deleteTripActivity(
        tripId: number,
        activityId: number,
        payload: DeleteTripActivityRequest,
    ): Promise<void> {
        const vReq = valy.validate(DeleteTripActivityRequestSchema, payload, 'delete_trip_activity_request');
        if (!vReq.success) throw new Error('Invalid delete activity payload');

        const res = await fetchy.delete<void>(`trips/${tripId}/activities/${activityId}`, {
            body: payload,
            headers: {
                'Content-Type': 'application/json; charset=utf-8',
                Accept: 'application/json',
            },
        });

        if (res.status !== 204) {
            if (res.status === 404) throw new Error('Activity not found');
            throw new Error('Failed to delete activity');
        }
    }
    // ✅ Edit Activity
    public async editTripActivity(tripId: number, activityId: number, payload: EditTripActivityRequest): Promise<void> {
        const vReq = valy.validate(EditTripActivityRequestSchema, payload, 'edit_trip_activity_request');
        if (!vReq.success) throw new Error('Invalid edit activity payload');

        const res = await fetchy.patch<void>(`trips/${tripId}/activities/${activityId}`, {
            body: JSON.stringify(payload),
            headers: {
                'Content-Type': 'application/json; charset=utf-8',
                Accept: 'application/json',
            },
        });

        if (res.status !== 204) {
            if (res.status === 404) throw new Error('Activity not found');
            if (res.status === 400) throw new Error('Invalid edit activity data');
            throw new Error('Failed to edit activity');
        }
    }
}

export const tripActivitiesService = TripActivitiesService.getInstance();
