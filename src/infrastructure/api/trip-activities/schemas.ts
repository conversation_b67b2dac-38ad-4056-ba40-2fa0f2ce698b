import { z } from 'zod';

import { PaginationSchema } from '@/infrastructure/validation/schemas/pagination.schema';
import { TranslatableSchema } from '@/infrastructure/validation/schemas/translatable.schema';

/** Shared */
export const LocalizedNameSchema = z.object({
    arabic: z.string(),
    english: z.string(),
});
export const StatusSchema = z.object({ id: z.number(), name: LocalizedNameSchema });
export const ActionSchema = z.object({ id: z.number(), name: LocalizedNameSchema });

/** Lookups */
export const ActivityStatusesResponseSchema = z.array(StatusSchema);
export const ActivityActionsResponseSchema = z.array(ActionSchema);

/** Listing */
export const LocationSchema = z.object({
    latitude: z.number(),
    longitude: z.number(),
});
export const TripActivitySchema = z.object({
    id: z.number(),
    location: LocationSchema,
    note: z.string().nullable(),
    details: z.string().nullable(),
    createdAt: z.string(),
    updatedAt: z.string().nullable(),
    address: TranslatableSchema.nullable(),
    status: StatusSchema,
    action: ActionSchema,
    origin: z.string(),
});
export const TripActivitiesResponseSchema = z.object({
    pagination: PaginationSchema,
    data: z.array(TripActivitySchema),
});
export const GetTripActivitiesQueryParamsSchema = z.object({
    tripId: z.number(),
    pageNumber: z.number().optional().default(1),
    pageSize: z.number().optional().default(20),
});

/** Create */
export const CreateTripActivityRequestSchema = z.object({
    statusId: z.number(),
    actionId: z.number(),
    details: z.string(),
    notes: z.string().optional(), // (رقم البلاغ)
});
export const CreateTripActivityResponseSchema = z.object({
    id: z.number(),
});
export const DeleteTripActivityRequestSchema = z.object({
    reason: z.string(),
});
// ✅ Edit Activity request (all optional)
export const EditTripActivityRequestSchema = z.object({
    statusId: z.number().optional(),
    actionId: z.number().optional(),
    details: z.string().optional(),
    note: z.string().optional(),
});
