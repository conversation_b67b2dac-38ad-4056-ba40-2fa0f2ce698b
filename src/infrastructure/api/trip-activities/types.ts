import type { z } from 'zod';

import type {
    TripActivitySchema,
    TripActivitiesResponseSchema,
    GetTripActivitiesQueryParamsSchema,
    ActivityStatusesResponseSchema,
    ActivityActionsResponseSchema,
    CreateTripActivityRequestSchema,
    CreateTripActivityResponseSchema,
    StatusSchema,
    ActionSchema,
    DeleteTripActivityRequestSchema,
    EditTripActivityRequestSchema,
} from './schemas';

export type TripActivity = z.infer<typeof TripActivitySchema>;
export type TripActivitiesResponse = z.infer<typeof TripActivitiesResponseSchema>;
export type GetTripActivitiesQueryParams = z.infer<typeof GetTripActivitiesQueryParamsSchema>;

export type ActivityStatus = z.infer<typeof StatusSchema>;
export type ActivityAction = z.infer<typeof ActionSchema>;
export type ActivityStatusesResponse = z.infer<typeof ActivityStatusesResponseSchema>;
export type ActivityActionsResponse = z.infer<typeof ActivityActionsResponseSchema>;

export type CreateTripActivityRequest = z.infer<typeof CreateTripActivityRequestSchema>;
export type CreateTripActivityResponse = z.infer<typeof CreateTripActivityResponseSchema>;
export type DeleteTripActivityRequest = z.infer<typeof DeleteTripActivityRequestSchema>;

export type EditTripActivityRequest = z.infer<typeof EditTripActivityRequestSchema>;
