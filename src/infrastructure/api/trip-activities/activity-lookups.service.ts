import { logger } from '@/infrastructure/logging';
import { valy } from '@/shared/lib/Valy';
import { fetchy } from '@/shared/lib/Fetchy';

import { ActivityStatusesResponseSchema, ActivityActionsResponseSchema } from './schemas';
import type { ActivityStatusesResponse, ActivityActionsResponse } from './types';

export class ActivityLookupsService {
    private static instance: ActivityLookupsService;
    private constructor() {}
    public static getInstance(): ActivityLookupsService {
        if (!ActivityLookupsService.instance) {
            ActivityLookupsService.instance = new ActivityLookupsService();
        }
        return ActivityLookupsService.instance;
    }

    public async getStatuses(): Promise<ActivityStatusesResponse> {
        logger.info('[ActivityLookupsService] fetching statuses');
        try {
            const res = await fetchy.get<ActivityStatusesResponse>('activities/statuses');
            const v = valy.validate(ActivityStatusesResponseSchema, res.data, 'activity_statuses');
            if (!v.success) {
                // logger.error('[ActivityLookupsService] invalid statuses schema', v.errors);
                throw new Error('Invalid statuses response');
            }
            return v.data as ActivityStatusesResponse;
        } catch (e) {
            logger.error('[ActivityLookupsService] statuses failed', e as Error);
            throw new Error('Failed to load statuses');
        }
    }

    public async getActions(): Promise<ActivityActionsResponse> {
        logger.info('[ActivityLookupsService] fetching actions');
        try {
            const res = await fetchy.get<ActivityActionsResponse>('activities/actions');
            const v = valy.validate(ActivityActionsResponseSchema, res.data, 'activity_actions');
            if (!v.success) {
                // logger.error('[ActivityLookupsService] invalid actions schema', v.errors);
                throw new Error('Invalid actions response');
            }
            return v.data as ActivityActionsResponse;
        } catch (e) {
            logger.error('[ActivityLookupsService] actions failed', e as Error);
            throw new Error('Failed to load actions');
        }
    }
}

export const activityLookupsService = ActivityLookupsService.getInstance();
