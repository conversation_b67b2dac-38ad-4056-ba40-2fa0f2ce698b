import { logger } from '@/infrastructure/logging';
import { valy } from '@/shared/lib/Valy';
import { fetchy } from '@/shared/lib/Fetchy';
import { DEFAULT_PAGINATION } from '@/infrastructure/validation/schemas/pagination.schema';

import type { AlertTypeQueryParams, GetAlertTypesResponse } from './types';
import { GetAlertTypesResponseSchema } from './schemas';

export class AlertTypesService {
    private static instance: AlertTypesService;

    private constructor() {}
    public static getInstance(): AlertTypesService {
        if (!AlertTypesService.instance) {
            AlertTypesService.instance = new AlertTypesService();
        }
        return AlertTypesService.instance;
    }

    public async getAlertTypes(params: Partial<AlertTypeQueryParams> = {}): Promise<GetAlertTypesResponse> {
        logger.info('[AlertTypesService] fetching alert types with query params: ', params ?? {});

        try {
            const response = await fetchy.get<GetAlertTypesResponse>('alert-types', { params });
            // todo cache using react-query

            const validationResult = valy.validate(GetAlertTypesResponseSchema, response.data, 'alert_types');
            if (validationResult.success === false) {
                logger.error(
                    '[AlertTypesService] Invalid response from alert types API',
                    new Error('validation error', { cause: validationResult.errors }),
                );
                return {
                    data: [],
                    pagination: DEFAULT_PAGINATION,
                };
            }

            return validationResult.data as GetAlertTypesResponse;
        } catch (error: unknown) {
            logger.error('[AlertTypesService] Error fetching alert types: ', error as Error);
            throw error;
        }
    }
}

export const alertTypesService = AlertTypesService.getInstance();
