import { logger } from '@/infrastructure/logging';
import { valy } from '@/shared/lib/Valy';
import { fetchy } from '@/shared/lib/Fetchy';
import { DEFAULT_PAGINATION } from '@/infrastructure/validation/schemas/pagination.schema';
import { removeNullOrEmptyValues } from '@/shared/utils/object-filters';

import type { AlertsQueryParams, GetAlertStatsSummaryResponse, GetAlertsResponse } from './types';
import { GetAlertStatsSummaryResponseSchema, GetAlertsResponseSchema } from './schemas';

export class AlertsService {
    private static instance: AlertsService;

    private constructor() {}
    public static getInstance(): AlertsService {
        if (!AlertsService.instance) {
            AlertsService.instance = new AlertsService();
        }
        return AlertsService.instance;
    }

    public async getAlerts(body: Partial<AlertsQueryParams> = {}): Promise<GetAlertsResponse> {
        logger.info('[AlertsService] fetching alerts with query params: ', body ?? {});

        try {
            const response = await fetchy.post<GetAlertsResponse>(`alerts`, { body: removeNullOrEmptyValues(body) });
            // TODO: cache using react-query
            const validationResult = valy.validate(GetAlertsResponseSchema, response.data, 'alerts');
            if (validationResult.success === false) {
                logger.error(
                    '[AlertsService] Invalid response from alerts API',
                    new Error('validation error', { cause: validationResult.errors }),
                );
                return {
                    data: [],
                    pagination: DEFAULT_PAGINATION,
                };
            }

            return validationResult.data as GetAlertsResponse;
        } catch (error: unknown) {
            logger.error('[AlertsService] Error fetching alerts: ', error as Error);
            throw error;
        }
    }

    public async getAlertStatsSummary(): Promise<GetAlertStatsSummaryResponse> {
        logger.info('[AlertsService] fetching alert stats summary');

        try {
            const response = await fetchy.get<GetAlertStatsSummaryResponse>('alerts/stats');
            const validationResult = valy.validate(
                GetAlertStatsSummaryResponseSchema,
                response.data,
                'alert_stats_summary',
            );

            if (validationResult.success === false) {
                logger.error(
                    '[AlertsService] Invalid response from alert stats summary API',
                    new Error('validation error', { cause: validationResult.errors }),
                );
                return {
                    totalAcknowledgedAlerts: 0,
                    totalUnacknowledgedAlerts: 0,
                    totalActiveTrips: 0,
                    totalInActiveTrips: 0,
                };
            }
            return validationResult.data as GetAlertStatsSummaryResponse;
        } catch (error: unknown) {
            logger.error('[AlertsService] Error fetching alert stats summary: ', error as Error);
            throw error;
        }
    }
}

export const alertsService = AlertsService.getInstance();
