import type { z } from 'zod';

import type {
    AlertSchema,
    AlertsQueryParamsSchema,
    GetAlertsResponseSchema,
    GetAlertStatsSummaryResponseSchema,
} from './schemas';

export type Alert = z.infer<typeof AlertSchema>;
export type AlertsQueryParams = z.infer<typeof AlertsQueryParamsSchema>;
export type GetAlertsResponse = z.infer<typeof GetAlertsResponseSchema>;
export type GetAlertStatsSummaryResponse = z.infer<typeof GetAlertStatsSummaryResponseSchema>;
