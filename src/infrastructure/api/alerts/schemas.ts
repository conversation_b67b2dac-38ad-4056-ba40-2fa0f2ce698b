import { z } from 'zod';

import { PaginationSchema } from '@/infrastructure/validation/schemas/pagination.schema';
import { AlertAcknowledgement, TripCategory, TripLocation } from '@/shared/enums';

import { AlertTypeSchema as BaseAlertTypeSchema } from '../alert-types/schemas';
import { TripStateSchema as BaseTripStateSchema, PortSchema } from '../trips/schemas';

export const AlertTypeSchema = BaseAlertTypeSchema.omit({
    code: true,
    priority: true,
});

const TripStateSchema = BaseTripStateSchema.extend({
    stateId: z.coerce.number(),
});

export const AlertsQueryParamsSchema = z.object({
    tripId: z.string().nullable().default(null),
    fromServerDate: z.coerce.date().nullable().default(null),
    toServerDate: z.coerce.date().nullable().default(null),
    fromTrackerDate: z.coerce.date().nullable().default(null),
    toTrackerDate: z.coerce.date().nullable().default(null),
    inPorts: z.array(z.number()).default([]),
    outPorts: z.array(z.number()).default([]),
    alertTypes: z.array(z.number()).default([]),
    activeAlertsOnly: z.boolean().nullable().default(null),
    transitNumber: z.number().nullable().default(null),
    transitSeqNumber: z.string().nullable().default(null),
    driverName: z.string().nullable().default(null),
    driverNationality: z.string().nullable().default(null),
    driverMobileNo: z.string().nullable().default(null),
    driverPassportNumber: z.string().nullable().default(null),
    plateNumber: z.string().nullable().default(null),
    trackerNumber: z.string().nullable().default(null),
    tipCode: z.string().nullable().default(null),
    tripCategory: z.enum(TripCategory).nullable().default(null),
    tripLocations: z.array(z.enum(TripLocation)).default([]),
    activeTripsOnly: z.boolean().nullable().default(null),
    tripStartDate: z.date().nullable().default(null),
    tripEndDate: z.date().nullable().default(null),
    transDate: z.date().nullable().default(null),
    alertAcknowledgement: AlertAcknowledgement,
    pageNumber: z.number().optional().default(1),
    pageSize: z.number().optional().default(50),
});

export const GetAlertStatsSummaryResponseSchema = z.object({
    totalAcknowledgedAlerts: z.number(),
    totalUnacknowledgedAlerts: z.number(),
    totalActiveTrips: z.number(),
    totalInActiveTrips: z.number(),
});

export const AlertSchema = z.object({
    id: z.coerce.number(),
    tripId: z.coerce.number(),
    entryPort: PortSchema.nullable(),
    exitPort: PortSchema.nullable(),
    alertType: AlertTypeSchema,
    fromState: TripStateSchema,
    toState: TripStateSchema.optional().nullable(),
    acknowledgedAt: z.coerce.date().optional().nullable(),
    acknowledgedBy: z
        .object({
            id: z.string().default('00000000-0000-0000-0000-000000000000'),
            name: z.string().default('Dummy User'),
        })
        .default({
            id: '00000000-0000-0000-0000-000000000000',
            name: 'Dummy User',
        })
        .nullable(),
});

export const GetAlertsResponseSchema = z.object({
    data: z.array(AlertSchema),
    pagination: PaginationSchema,
});
