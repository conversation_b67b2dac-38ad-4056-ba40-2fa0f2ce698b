import type { z } from 'zod';

import type {
    AlertTypeSchema,
    TripAlertSchema,
    TripAlertsQueryParamsSchema,
    GetTripAlertsResponseSchema,
} from './schemas';

export type AlertType = z.infer<typeof AlertTypeSchema>;
export type TripAlert = z.infer<typeof TripAlertSchema>;
export type TripAlertsQueryParams = z.infer<typeof TripAlertsQueryParamsSchema>;
export type GetTripAlertsResponse = z.infer<typeof GetTripAlertsResponseSchema>;
