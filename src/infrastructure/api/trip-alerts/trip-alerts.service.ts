import { logger } from '@/infrastructure/logging';
import { valy } from '@/shared/lib/Valy';
import { fetchy } from '@/shared/lib/Fetchy';
import { DEFAULT_PAGINATION } from '@/infrastructure/validation/schemas/pagination.schema';

import type { TripAlertsQueryParams, GetTripAlertsResponse } from './types';
import { GetTripAlertsResponseSchema } from './schemas';

export class TripAlertsService {
    private static instance: TripAlertsService;

    private constructor() {}
    public static getInstance(): TripAlertsService {
        if (!TripAlertsService.instance) {
            TripAlertsService.instance = new TripAlertsService();
        }
        return TripAlertsService.instance;
    }

    public async getTripAlerts(params: Partial<TripAlertsQueryParams> = {}): Promise<GetTripAlertsResponse> {
        logger.info('[TripAlertsService] fetching trip alerts with query params: ', params ?? {});

        try {
            const response = await fetchy.get<GetTripAlertsResponse>(`trips/${params.tripId}/alerts/`, { params });
            // TODO: cache using react-query
            const validationResult = valy.validate(GetTripAlertsResponseSchema, response.data, 'trip_alerts');
            if (validationResult.success === false) {
                logger.error(
                    '[TripAlertsService] Invalid response from trip alerts API',
                    new Error('validation error', { cause: validationResult.errors }),
                );
                return {
                    data: [],
                    pagination: DEFAULT_PAGINATION,
                };
            }

            return validationResult.data as GetTripAlertsResponse;
        } catch (error: unknown) {
            logger.error('[TripAlertsService] Error fetching trip alerts: ', error as Error);
            throw error;
        }
    }

    public async acknowledgeTripAlert(tripId: string, alertId: string): Promise<void> {
        logger.info(`[TripAlertsService] acknowledging alert ${alertId} for trip ${tripId}`);

        try {
            const response = await fetchy.put<unknown>(`trips/${tripId}/alerts/${alertId}/acknowledge`);
            // Accept both 200 and 204 as valid
            if (![200, 204].includes(response.status)) {
                throw new Error(`Unexpected response status: ${response.status}`);
            }

            return;
        } catch (error: unknown) {
            logger.error('[TripAlertsService] Error acknowledging alert: ', error as Error);
            throw error;
        }
    }

    public async acknowledgeAllTripAlerts(tripId: string): Promise<void> {
        logger.info(`[TripAlertsService] acknowledging ALL alerts for trip ${tripId}`);

        try {
            const response = await fetchy.put<unknown>(`trips/${tripId}/alerts/acknowledge`);

            // Accept both 200 and 204 as valid success responses
            if (![200, 204].includes(response.status)) {
                throw new Error(`Unexpected response status: ${response.status}`);
            }

            return;
        } catch (error: unknown) {
            logger.error('[TripAlertsService] Error acknowledging all alerts: ', error as Error);
            throw error;
        }
    }
}

export const tripAlertsService = TripAlertsService.getInstance();
