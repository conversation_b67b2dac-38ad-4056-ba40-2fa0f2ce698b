import { z } from 'zod';

import { PaginationSchema } from '@/infrastructure/validation/schemas/pagination.schema';

import { AlertTypeSchema as BaseAlertTypeSchema } from '../alert-types/schemas';
import { TripStateSchema as BaseTripStateSchema } from '../trips/schemas';

// create a new AlertTypeSchema schema without `alertCode` and `alertPriority`
export const AlertTypeSchema = BaseAlertTypeSchema.omit({
    code: true,
    priority: true,
});

// create a new TripStateSchema schema based on TripStateSchema + stateId
const TripStateSchema = BaseTripStateSchema.extend({
    stateId: z.coerce.number(),
});

export const TripAlertsQueryParamsSchema = z.object({
    tripId: z.string().nullable().default(null),
    PageNumber: z.number().optional().default(1),
    PageSize: z.number().optional().default(20),
});

export const TripAlertSchema = z.object({
    id: z.coerce.number(),
    alertType: AlertTypeSchema,
    fromState: TripStateSchema,
    toState: TripStateSchema.optional().nullable(),
    acknowledgedAt: z.coerce.date().optional().nullable(),
    acknowledgedBy: z
        .object({
            id: z.string().default('00000000-0000-0000-0000-000000000000'),
            name: z.string().default('Dummy User'),
        })
        .default({
            id: '00000000-0000-0000-0000-000000000000',
            name: 'Dummy User',
        })
        .nullable(),
});

export const GetTripAlertsResponseSchema = z.object({
    data: z.array(TripAlertSchema),
    pagination: PaginationSchema,
});
