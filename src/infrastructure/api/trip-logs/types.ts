import type { z } from 'zod';

import type { TripLogSchema, TripLogsResponseSchema, GetTripLogsQueryParamsSchema, LogUserSchema } from './schemas';

export type TripLog = z.infer<typeof TripLogSchema>;
export type TripLogsResponse = z.infer<typeof TripLogsResponseSchema>;
export type GetTripLogsQueryParams = z.infer<typeof GetTripLogsQueryParamsSchema>;
export type LogUser = z.infer<typeof LogUserSchema>;
