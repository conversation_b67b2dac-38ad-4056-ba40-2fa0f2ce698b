// src/infrastructure/api/trip-logs/trip-logs.services.ts
import { logger } from '@/infrastructure/logging';
import { valy } from '@/shared/lib/Valy';
import { fetchy } from '@/shared/lib/Fetchy';
import { DEFAULT_PAGINATION } from '@/infrastructure/validation/schemas/pagination.schema';

import type { GetTripLogsQueryParams, TripLogsResponse } from './types';
import { TripLogsResponseSchema } from './schemas';

export class TripLogsService {
    private static instance: TripLogsService;

    private constructor() {}

    public static getInstance(): TripLogsService {
        if (!TripLogsService.instance) {
            TripLogsService.instance = new TripLogsService();
        }
        return TripLogsService.instance;
    }

    public async getTripLogs(params: GetTripLogsQueryParams): Promise<TripLogsResponse> {
        logger.info('[TripLogsService] fetching trip logs with params: ', params);
        try {
            const queryParams = new URLSearchParams();

            if (params.scope) {
                if (Array.isArray(params.scope)) {
                    params.scope.forEach((scope) => queryParams.append('scope', String(scope)));
                } else {
                    queryParams.append('scope', String(params.scope));
                }
            }
            if (params.action) queryParams.append('action', String(params.action));
            if (params.pageNumber) queryParams.append('pageNumber', String(params.pageNumber));
            if (params.pageSize) queryParams.append('pageSize', String(params.pageSize));

            const url = `trips/${params.tripId}/logs${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;

            const response = await fetchy.get<TripLogsResponse>(url);

            const validationResult = valy.validate(TripLogsResponseSchema, response.data, 'trip_logs');
            if (validationResult.success === false) {
                logger.error(
                    '[TripLogsService] Invalid response from trip logs API',
                    new Error('validation error', { cause: validationResult.errors }),
                );
                return {
                    data: [],
                    pagination: DEFAULT_PAGINATION,
                };
            }

            return validationResult.data as TripLogsResponse;
        } catch (error: unknown) {
            logger.error('[TripLogsService] Error fetching trip logs: ', error as Error);
            throw error;
        }
    }
}

export const tripLogsService = TripLogsService.getInstance();
