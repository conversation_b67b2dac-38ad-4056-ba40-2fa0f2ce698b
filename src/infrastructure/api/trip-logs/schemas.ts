import { z } from 'zod';

import { PaginationSchema } from '@/infrastructure/validation/schemas/pagination.schema';
import { TranslatableSchema } from '@/infrastructure/validation/schemas/translatable.schema';

//-- Enums
export const LogScopeEnum = z.enum(['1', '2']).transform(Number); // 1=Trip, 2=TripAlert
export const LogActionEnum = z.enum(['1', '2']).transform(Number); // 1=Watch, 2=Acknowledge

//-- User schema inside a log
export const LogUserSchema = z.object({
    id: z.int(),
    name: z.string(),
});

//-- Single log entry
export const TripLogSchema = z.object({
    id: z.number(),
    log: TranslatableSchema,
    action: z.number(), // matches LogActionEnum
    scope: z.number(), // matches LogScopeEnum
    user: LogUserSchema,
    createdAt: z.string(), // ISO datetime
});

//-- Response schema
export const TripLogsResponseSchema = z.object({
    data: z.array(TripLogSchema),
    pagination: PaginationSchema,
});

//-- Query params schema
export const GetTripLogsQueryParamsSchema = z.object({
    tripId: z.number(),
    scope: z.number().optional(), // 1=Trip, 2=TripAlert
    action: z.number().optional(), // 1=Watch, 2=Acknowledge
    pageNumber: z.number().optional().default(1),
    pageSize: z.number().optional().default(20),
});

export type TripLog = z.infer<typeof TripLogSchema>;
export type TripLogsResponse = z.infer<typeof TripLogsResponseSchema>;
export type GetTripLogsQueryParams = z.infer<typeof GetTripLogsQueryParamsSchema>;
