import type { z } from 'zod';

import type {
    LocationSchema,
    TripLocationSchema,
    TripLocationsResponseSchema,
    GetTripLocationsQueryParamsSchema,
} from './schemas';

export type Location = z.infer<typeof LocationSchema>;
export type TripLocation = z.infer<typeof TripLocationSchema>;
export type TripLocationsResponse = z.infer<typeof TripLocationsResponseSchema>;
export type GetTripLocationsQueryParams = z.infer<typeof GetTripLocationsQueryParamsSchema>;
