import { logger } from '@/infrastructure/logging';
import { valy } from '@/shared/lib/Valy';
import { fetchy } from '@/shared/lib/Fetchy';
import { DEFAULT_PAGINATION } from '@/infrastructure/validation/schemas/pagination.schema';

import type { GetTripLocationsQueryParams, TripLocationsResponse } from './types';
import { TripLocationsResponseSchema } from './schemas';

export class TripLocationsService {
    private static instance: TripLocationsService;

    private constructor() {}

    public static getInstance(): TripLocationsService {
        if (!TripLocationsService.instance) {
            TripLocationsService.instance = new TripLocationsService();
        }
        return TripLocationsService.instance;
    }

    public async getTripLocations(params: GetTripLocationsQueryParams): Promise<TripLocationsResponse> {
        logger.info('[TripLocationsService] fetching trip locations with params: ', params);
        try {
            logger.info('[TripLocationsService] fetching trip locations with params: ', params);
            const response = await fetchy.get<TripLocationsResponse>(
                `trips/${params.tripId}/locations`,
                { params }, // includes from, to, page, pageSize
            );

            const validationResult = valy.validate(TripLocationsResponseSchema, response.data, 'trip_locations');
            if (validationResult.success === false) {
                logger.error(
                    '[TripLocationsService] Invalid response from trip locations API',
                    new Error('validation error', { cause: validationResult.errors }),
                );
                return {
                    data: [],
                    pagination: DEFAULT_PAGINATION,
                };
            }

            return validationResult.data as TripLocationsResponse;
        } catch (error: unknown) {
            logger.error('[TripLocationsService] Error fetching trip locations: ', error as Error);
            throw error;
        }
    }
}

export const tripLocationsService = TripLocationsService.getInstance();
