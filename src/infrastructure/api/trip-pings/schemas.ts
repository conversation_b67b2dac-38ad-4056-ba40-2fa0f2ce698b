import { z } from 'zod';

import { PaginationSchema } from '@/infrastructure/validation/schemas/pagination.schema';
import { TranslatableSchema } from '@/infrastructure/validation/schemas/translatable.schema';

export const LocationSchema = z.object({
    latitude: z.number(),
    longitude: z.number(),
});

export const TripLocationSchema = z.object({
    id: z.number(),
    trackerDateTime: z.string(),
    serverDateTime: z.string(),
    density: z.number(),
    location: LocationSchema,
    address: TranslatableSchema,
    bearing: z.number(),
    speed: z.number(),
    batteryLevelPercentage: z.coerce.number(),
    chargerStatus: z.coerce.number(),
    gpsSignalStrength: z.coerce.number(),
    gsmSignalStrength: z.coerce.number(),
    routeZone: z.coerce.number(),
    isWithinRouteGeofence: z.coerce.boolean(),
    timeElapsedSinceTripStartInMinutes: z.coerce.number(),
    remainingDistanceInMeters: z.coerce.number(),
    completeDistanceInMeters: z.coerce.number(),
    isWithinSuspiciousZone: z.coerce.boolean(),
});

export const TripLocationsResponseSchema = z.object({
    data: z.array(TripLocationSchema),
    pagination: PaginationSchema,
});

export const GetTripLocationsQueryParamsSchema = z.object({
    tripId: z.number(),
    from: z.string().optional(), // ISO datetime
    to: z.string().optional(),
    page: z.number().optional().default(1),
    pageSize: z.number().optional().default(20),
});
