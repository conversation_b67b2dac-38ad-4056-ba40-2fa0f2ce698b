/* eslint-disable @typescript-eslint/no-explicit-any */
import type { InternalAxiosRequestConfig } from 'axios';

import { fetchy } from '@/shared/lib/Fetchy';
import { useAuthStore } from '@/stores/auth.store';

import { LocalStorage } from '../local-storage/local-storage';
import { LocalStorageKeys } from '../local-storage/local-storage-keys.constants';
import { logger } from '../logging';

interface AuthTokens {
    tokenType: string;
    accessToken: string;
    expiresIn: number;
    refreshToken: string;
}

fetchy.addRequestInterceptor((config: InternalAxiosRequestConfig) => {
    const authData = LocalStorage.get(LocalStorageKeys.AUTH_TOKEN) as AuthTokens | null;

    if (!authData?.accessToken) return config;

    config.headers.Authorization = `Bearer ${authData.accessToken}`;
    return config;
});

fetchy.addResponseInterceptor(
    (response: any) => response,
    // TODO:: fix any type
    async (error: any): Promise<any> => {
        const originalRequest = error.config as InternalAxiosRequestConfig & { _retry?: boolean };

        // Don't retry if this is already a retry attempt or if the request is to the refresh/login endpoint
        const isRefreshEndpoint = originalRequest.url?.includes('/auth/refresh');
        const isLoginEndpoint = originalRequest.url?.includes('/auth/login');
        if (error.response?.status === 401 && !originalRequest._retry && !isRefreshEndpoint && !isLoginEndpoint) {
            originalRequest._retry = true;

            try {
                const authStore = useAuthStore.getState();
                const refreshSuccess = await authStore.refreshToken();

                if (refreshSuccess) {
                    const newAuthData = LocalStorage.get(LocalStorageKeys.AUTH_TOKEN) as AuthTokens | null;
                    if (newAuthData?.accessToken) {
                        originalRequest.headers.Authorization = `Bearer ${newAuthData.accessToken}`;
                        return fetchy.retryRequest(originalRequest);
                    }
                }
            } catch (refreshError) {
                logger.error('[AuthInterceptor] Token refresh failed:', refreshError as Error);
            }

            // Clear auth and redirect to login
            useAuthStore.getState().logout();
            window.location.href = '/login';
        } else if (error.response?.status === 401 && isRefreshEndpoint) {
            // If refresh endpoint itself returns 401, clear auth and redirect immediately
            logger.error('[AuthInterceptor] Refresh token is invalid, clearing auth and redirecting to login');
            useAuthStore.getState().logout();
            window.location.href = '/login';
        }

        return Promise.reject(error);
    },
);
