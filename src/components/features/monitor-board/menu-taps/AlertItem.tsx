import { useRef } from 'react';
import { format } from 'date-fns';
import { useTranslation } from 'react-i18next';

import { appConfig } from '@/shared/config/app-settings.config';
import { useTripLocationStore } from '@/stores/trip-location.store';
import { useAlertStore, type Alert } from '@/stores/alert.store';
import { useLocalized } from '@/shared/hooks/use-localized.hook';
import { Icon } from '@/components/common/ui/Icon';
import { alertIconMapping } from '@/shared/utils/alerts.utils';
import { useTripAlertsStore } from '@/stores/trip-alerts.store';
import { logger } from '@/infrastructure/logging';

export default function AlertItem({ alert }: { alert: Alert }) {
    const { t } = useTranslation();
    const { localized, dir } = useLocalized();

    const setHighlightedTrip = useTripLocationStore((state) => state.setHighlightedTrip);
    const HIGHLIGHT_DURATION = appConfig.get('markerHighlightDurationInMs') || 3000; // default 3s
    // keep last timeout id so we can clear and restart it on each click
    const highlightTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

    const setIsAcknowledged = useAlertStore((state) => state.setIsAcknowledged);
    const acknowledgeTripAlert = useTripAlertsStore((s) => s.acknowledgeTripAlert);

    const handleAlertClick = async (tripId: string) => {
        // set highlight immediately
        setHighlightedTrip(tripId);

        // clear previous timeout if exists (restart timer)
        if (highlightTimeoutRef.current) {
            clearTimeout(highlightTimeoutRef.current);
            highlightTimeoutRef.current = null;
        }

        // start new timeout to reset highlight after duration
        highlightTimeoutRef.current = setTimeout(() => {
            setHighlightedTrip(null);
            highlightTimeoutRef.current = null;
        }, HIGHLIGHT_DURATION);

        const tripKey = String(tripId);
        const alertKey = String(alert.alertId);
        try {
            await acknowledgeTripAlert(tripKey, alertKey);
            logger.info('[AlertItem] Acknowledge successful');
            if (!alert.isAcknowledged) {
                setIsAcknowledged(alert);
            }
        } catch (error) {
            logger.error('[AlertItem] Acknowledge failed: ', error as Error);
        }
    };

    // Set background color and opacity based on acknowledgment status
    const alertBackGround = alert.isAcknowledged ? 'bg-gray-50' : 'bg-[#FFFFFF]';
    const contentOpacity = alert.isAcknowledged ? 'opacity-60' : 'opacity-100';

    // Only show green dot for non-acknowledged alerts
    const showGreenDot = !alert.isAcknowledged;

    return (
        <div
            className={`w-full ${alertBackGround} shadow-sm border border-gray-200 rounded-lg overflow-hidden mb-3 hover:bg-[#F1FCF6]`}
            dir={dir}>
            <div
                className={`px-2 py-2 m-0 cursor-pointer transition-colors ${contentOpacity}`}
                onClick={() => handleAlertClick(alert.tripId.toString())}>
                {/* Header row*/}
                <div className="flex items-center justify-between ">
                    {/* Right side - Trip ID with green highlight */}
                    <div className="flex items-center gap-1">
                        <span className="text-green-600 font-bold text-sm">{alert.tripId} /</span>
                        <span className="text-gray-500 text-sm font-medium">{alert.transitNumber}</span>
                    </div>

                    {/* Left side - Green dot and date/time */}
                    <div className="flex items-center gap-2">
                        <span className="text-gray-500 text-xs" dir={dir === 'rtl' ? 'ltr' : ''}>
                            {format(new Date(alert.currentState.dateTime), 'dd-MM-yyyy - hh:mmaaa').toLowerCase()}
                        </span>
                        {showGreenDot && <div className="w-2 h-2 bg-green-500 rounded-full flex-shrink-0"></div>}
                    </div>
                </div>

                {/* Route description */}
                <div className="mb-3">
                    <span className="text-black-700 text-sm font-medium">
                        <span className="text-gray-500 text-xs">{t('common.from')} </span>
                        {localized(alert.entryPort.name)}
                        <span className="text-gray-500 text-xs"> {t('common.to')} </span>
                        {localized(alert.exitPort.name)}
                    </span>
                </div>

                {/* GPS Alert Box - Red background with warning icons */}
                <div className="border border-red-200 rounded-md p-2 my-3 bg-[#FDF3F3]">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                            <Icon
                                name={alertIconMapping[alert.alertType.id] ?? 'alert'}
                                className="w-4 h-4 text-red-400"
                            />
                            <span className="text-red-400 font-bold text-xs">{localized(alert.alertType.name)}</span>
                        </div>
                    </div>
                </div>

                {/* Bottom icons row */}
                <div className="flex items-center  gap-2">
                    <Icon
                        name="gpsDisconnected"
                        className="w-4 h-4 text-red-500 hover:text-red-600 cursor-pointer transition-colors"
                    />
                    <Icon
                        name="batteryVeryLow"
                        className="w-4 h-4 text-red-500 hover:text-red-600 cursor-pointer transition-colors"
                    />
                    <Icon
                        name="speed"
                        className="w-4 h-4 text-green-500 hover:text-green-600 cursor-pointer transition-colors"
                    />
                    <Icon
                        name="charger"
                        className="w-4 h-4 text-orange-400 hover:text-orange-500 cursor-pointer transition-colors"
                    />
                    <Icon
                        name="leavingGeofence"
                        className="w-4 h-4 text-green-500 hover:text-green-600 cursor-pointer transition-colors"
                    />
                </div>
            </div>
        </div>
    );
}
