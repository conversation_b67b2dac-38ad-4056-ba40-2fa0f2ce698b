import { useTranslation } from 'react-i18next';
import { FcAdvertising } from 'react-icons/fc';
import { Md<PERSON><PERSON><PERSON>, MdAdd, Md<PERSON><PERSON>erList } from 'react-icons/md';
import { useState, useEffect } from 'react';
import { RefreshCwIcon } from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/common/ui/Card';
import { useAlertStore } from '@/stores/alert.store';
import { Checkbox } from '@/components/common/ui/Checkbox';
import { useAlertTypeLookups, useAlertTypeStore } from '@/stores/alert-type.store';
import { useLocalized } from '@/shared/hooks/use-localized.hook';
import { Button } from '@/components/common/ui/Button';
import { Radio } from '@/components/common/ui/Radio';

import AlertItem from './AlertItem';

export default function AlertsTap() {
    const { t, i18n } = useTranslation();
    const { t: tWarnings } = useTranslation('warnings');
    const localized = useLocalized();
    const alertTypes = useAlertTypeLookups();
    const loadAlertTypes = useAlertTypeStore((state) => state.loadAlertTypes);
    const [selectedAlertTypes, setSelectedAlertTypes] = useState<number[]>([]);
    const [statusFilter, setStatusFilter] = useState<'all' | 'active'>('all');
    const [ackFilter, setAckFilter] = useState<'all' | 'acknowledged' | 'notAcknowledged'>('all');
    const alerts = useAlertStore((state) => state.alerts); // <-- subscribe to alerts
    const filterAlerts = useAlertStore((state) => state.filterAlerts);
    const filteredAlerts = useAlertStore((state) => state.filteredAlerts);

    // keep last timeout id so we can clear and restart it on each click

    const [isMinimized, setIsMinimized] = useState(false);
    const [isFilterOpen, setIsFilterOpen] = useState(false);

    const updateSelectedAlertTypes = (id: number) => {
        setSelectedAlertTypes((prev) => (prev.includes(id) ? prev.filter((sid) => sid !== id) : [...prev, id]));
    };

    // Recompute filteredAlerts whenever selectedAlertTypes or alerts change

    useEffect(() => {
        loadAlertTypes({ PageNumber: 1, PageSize: 1000 });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // Filter alerts whenever filters or alerts change
    useEffect(() => {
        filterAlerts((alert) => {
            // Filter by type
            const matchType = selectedAlertTypes.length === 0 || selectedAlertTypes.includes(alert.alertType.id);

            // Filter by status
            const matchActive = statusFilter === 'all' || (statusFilter === 'active' && alert.toStateDateTime === null);

            // Filter by acknowledgement
            let matchAck = true;
            if (ackFilter === 'acknowledged') {
                matchAck = alert.isAcknowledged === true;
            } else if (ackFilter === 'notAcknowledged') {
                matchAck = alert.isAcknowledged === false;
            }

            return matchType && matchActive && matchAck;
        });
    }, [selectedAlertTypes, alerts, statusFilter, ackFilter, filterAlerts]);

    return (
        <>
            {!isMinimized && (
                <Card className="_effect fixed left-2 top-30 m-4 w-100 shadow-lg" dir={i18n.dir()}>
                    {/* Header */}
                    <CardHeader className="px-4 grid grid-cols-12 items-center border-b bg-white">
                        <div className="col-span-3 flex justify-start">
                            <button
                                onClick={() => setIsMinimized(true)}
                                className="border border-gray-300 rounded p-0 text-gray-500 hover:text-gray-700">
                                <MdRemove size={20} />
                            </button>
                        </div>
                        <div className="col-span-6 flex justify-center">
                            <CardTitle className="flex items-center gap-0">
                                <FcAdvertising className="size-5" />
                                {t('reports.newAlerts')}
                            </CardTitle>
                        </div>
                        <div className="col-span-3 flex justify-end">
                            <button
                                onClick={() => setIsFilterOpen(!isFilterOpen)}
                                className="border border-gray-300 rounded p-0 text-gray-500 hover:text-gray-700">
                                <MdFilterList size={20} />
                            </button>
                            {isFilterOpen && (
                                <div
                                    dir={i18n.dir()}
                                    className={`absolute mt-0 w-56 top-15 bg-white border border-gray-200 rounded shadow-lg z-50 ${
                                        i18n.dir() === 'rtl' ? 'left-0' : 'right-0'
                                    }`}>
                                    {/* Warning status filter */}
                                    <div className="space-y-1 border-b p-3">
                                        <Radio
                                            label={tWarnings('activeWarnings')}
                                            name="warnings-radio-group"
                                            onChange={() => setStatusFilter('active')}
                                            checked={statusFilter === 'active'}
                                        />
                                        <Radio
                                            label={tWarnings('allWarnings')}
                                            name="warnings-radio-group"
                                            onChange={() => setStatusFilter('all')}
                                            checked={statusFilter === 'all'}
                                        />
                                    </div>
                                    {/* Warning acknowledgement filter */}
                                    <div className="space-y-1 border-b p-3">
                                        <Radio
                                            label={tWarnings('acknowledgeWarnings')}
                                            name="acknowledgement-radio-group"
                                            onChange={() => setAckFilter('acknowledged')}
                                            checked={ackFilter === 'acknowledged'}
                                        />
                                        <Radio
                                            label={tWarnings('notAcknowledgedWarnings')}
                                            name="acknowledgement-radio-group"
                                            onChange={() => setAckFilter('notAcknowledged')}
                                            checked={ackFilter === 'notAcknowledged'}
                                        />
                                        <Radio
                                            label={tWarnings('allWarnings')}
                                            name="acknowledgement-radio-group"
                                            onChange={() => setAckFilter('all')}
                                            checked={ackFilter === 'all'}
                                        />
                                    </div>

                                    <div className="p-3">
                                        <div className="space-y-1 max-h-60 overflow-auto">
                                            <Checkbox
                                                isSelectAll={true}
                                                label={t('filter.selectAll')}
                                                checked={
                                                    selectedAlertTypes?.length === alertTypes.length &&
                                                    alertTypes.length > 0
                                                }
                                                onChange={(checked: boolean) => {
                                                    if (checked) {
                                                        // Select all alert types
                                                        setSelectedAlertTypes(alertTypes.map((type) => type.id));
                                                    } else {
                                                        // Deselect all alert types
                                                        setSelectedAlertTypes([]);
                                                    }
                                                }}
                                            />
                                            {alertTypes.map((type) => {
                                                return (
                                                    <Checkbox
                                                        key={type.id}
                                                        label={localized(type.name)}
                                                        checked={selectedAlertTypes.includes(type.id)}
                                                        onChange={() => updateSelectedAlertTypes(type.id)}
                                                    />
                                                );
                                            })}
                                        </div>
                                    </div>
                                    <div className=" py-1 flex items-center justify-end  border-t border-gray-200">
                                        <Button
                                            variant="outline"
                                            className="mx-2 my-1"
                                            onClick={() => {
                                                setSelectedAlertTypes([]);
                                                setStatusFilter('all');
                                                setAckFilter('all');
                                            }}>
                                            <RefreshCwIcon /> {t('filter.reset')}
                                        </Button>
                                    </div>
                                </div>
                            )}
                        </div>
                    </CardHeader>

                    {/* Alerts list */}
                    <CardContent className="grid px-2 max-h-[calc(93vh-250px)] overflow-y-auto">
                        {filteredAlerts.map((alert, index) => (
                            <AlertItem key={index} alert={alert} />
                        ))}
                    </CardContent>
                </Card>
            )}

            {/* Minimized View */}
            {isMinimized && (
                <div
                    className="fixed left-6 bottom-18  bg-white shadow-lg border rounded-lg px-4 py-2 flex items-center justify-between w-60 cursor-pointer"
                    onClick={() => setIsMinimized(false)}>
                    <div className="flex items-center gap-2">
                        <FcAdvertising className="size-5" />
                        <span className="font-medium">{t('reports.newAlerts')}</span>
                    </div>
                    <MdAdd size={30} className="border border-gray-300 rounded p-1 text-gray-500 hover:text-gray-700" />
                </div>
            )}
        </>
    );
}
