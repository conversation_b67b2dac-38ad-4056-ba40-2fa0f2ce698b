/* eslint-disable @typescript-eslint/no-non-null-assertion */
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { useTranslation } from 'react-i18next';
import { useEffect, useRef, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import type { VirtualScrollerLazyEvent } from 'primereact/virtualscroller';
import { Toast } from 'primereact/toast';

import { formatLocalizedDate } from '@/shared/utils/format-date.utils';
import { useLocalized } from '@/shared/hooks/use-localized.hook';
import i18n from '@/shared/config/i18n.config';
import { ExportExcelButton } from '@/components/common/ui/ExportExcelButton';
import { Button } from '@/components/common/ui/Button';
import { useTripActivitiesStore } from '@/stores/trip-activities.store';
import { Icon } from '@/components/common/ui/Icon';
import Tooltip from '@/components/common/ui/Tooltip';
import DeleteActivityDialog from '@/components/common/ui/DeleteActivityDialog';
import ActivityLogDialog from '@/components/common/ui/ActivityLogDialog';
import { ActivityLogsDialog } from '@/components/common/ui/DeletedActivityLogsDialog';
import { useTripDetailStore } from '@/stores/trip-detail.store';
import { tripWithActivitiesExcelMapper } from '@/mappers/tripWithActivitiesExcel.mapper';
import { showError, showSuccess } from '@/components/common/ui/Toast';
import PrintButton from '@/components/common/printing-templates/PrintButton';
import { ExportPdfButton } from '@/components/common/ui/ExportPdfButton';

import ActivityReportPrintable from './ActivityReportPrintable';

type RowShape = {
    id: number;
    details?: string | null;
    note?: string | null;
    status?: { id: number } | null;
    action?: { id: number } | null;
    location?: { latitude: number; longitude: number } | null;
    origin?: string;
    createdAt: string;
};

export default function TripActivitiesReportView() {
    const { t } = useTranslation();
    const { localized, dir } = useLocalized();
    const params = useParams();
    const navigate = useNavigate();
    const tripId = Number(params.id);

    // Toast
    const toast = useRef<Toast>(null);

    // Store hooks
    const activities = useTripActivitiesStore((s) => s.activities);
    const pagination = useTripActivitiesStore((s) => s.pagination);
    const isLoading = useTripActivitiesStore((s) => s.isLoading);
    const isDeleting = useTripActivitiesStore((s) => s.isDeleting);
    const loadTripActivities = useTripActivitiesStore((s) => s.loadTripActivities);
    const loadMoreTripActivities = useTripActivitiesStore((s) => s.loadTripActivities);
    const deleteTripActivity = useTripActivitiesStore((s) => s.deleteTripActivity);
    // const [open, setOpen] = React.useState(false);

    const PAGE_SIZE = 50;

    // Delete dialog state
    const [isDeleteOpen, setIsDeleteOpen] = useState(false);
    const [deleteTargetId, setDeleteTargetId] = useState<number | null>(null);
    const [deleteReason, setDeleteReason] = useState('');

    // Deleted activities dialog state
    const [isDeletedLogsOpen, setIsDeletedLogsOpen] = useState(false);

    // Edit dialog state
    const [isEditOpen, setIsEditOpen] = useState(false);
    const [editTargetId, setEditTargetId] = useState<number | null>(null);
    const [editInitial, setEditInitial] = useState<{
        statusId?: number;
        actionId?: number;
        details?: string;
        note?: string;
    }>({});
    // 🔹 Activity Logs dialog state
    const [logsOpen, setLogsOpen] = useState(false);
    const [selectedActivityId, setSelectedActivityId] = useState<number | null>(null);

    // Create dialog state
    const [isCreateOpen, setIsCreateOpen] = useState(false);

    useEffect(() => {
        if (tripId) {
            loadTripActivities({ tripId, pageNumber: 1, pageSize: PAGE_SIZE });
        }
    }, [tripId, loadTripActivities]);

    // Open delete dialog
    const openDeleteDialog = (activityId: number) => {
        setDeleteTargetId(activityId);
        setDeleteReason('');
        setIsDeleteOpen(true);
    };

    // Confirm delete
    const handleConfirmDelete = async () => {
        if (!tripId || !deleteTargetId || !deleteReason.trim()) return;
        try {
            await deleteTripActivity(tripId, deleteTargetId, { reason: deleteReason.trim() });
            showSuccess(t('tripActivities.success'), t('tripActivities.deletedSuccessfully'));
            setIsDeleteOpen(false);
        } catch {
            showError(t('common.error'), t('tripActivities.deleteFailed'));
        }
    };

    // Open edit dialog with row defaults
    const openEditDialog = (row: RowShape) => {
        setEditTargetId(row.id);
        setEditInitial({
            statusId: row.status?.id ?? undefined,
            actionId: row.action?.id ?? undefined,
            details: row.details ?? '',
            note: row.note ?? '',
        });
        setIsEditOpen(true);
    };

    // Actions cell (edit + delete)
    const actionsTemplate = (rowData: RowShape) => {
        if (rowData.origin === 'System') return null;

        return (
            <div className="flex gap-1 items-center justify-center">
                {/* View Log */}
                <Tooltip tooltipMessage={t('common.activityLog')}>
                    <Button
                        variant="outline"
                        onClick={() => {
                            setSelectedActivityId(rowData.id);
                            setLogsOpen(true);
                        }}
                        className="p-button-outlined bg-white border !border-green-600 rounded-sm px-3 py-2 m-1 text-green-700">
                        <Icon name="viewLog" className="h-6 w-6" />
                    </Button>
                </Tooltip>

                {/* Edit */}
                <Tooltip tooltipMessage={t('common.edit')}>
                    <Button
                        variant="outline"
                        onClick={() => openEditDialog(rowData)}
                        className="p-button-outlined bg-white border !border-green-600 rounded-sm px-3 py-2 m-1 text-green-700">
                        <Icon name="edit" className="h-6 w-6" />
                    </Button>
                </Tooltip>

                {/* Delete */}
                <Tooltip tooltipMessage={t('common.delete')}>
                    <Button
                        variant="outline"
                        onClick={() => openDeleteDialog(rowData.id)}
                        className="p-button-outlined bg-white border !border-red-600 rounded-sm px-3 py-2 m-1 text-red-700">
                        <Icon name="delete" className="h-6 w-6" />
                    </Button>
                </Tooltip>
            </div>
        );
    };

    const { tripDetail } = useTripDetailStore();
    const excelTables = tripWithActivitiesExcelMapper(tripDetail, activities, t, localized);

    return (
        <div className="w-full">
            {/* Toast */}
            <Toast ref={toast} position="top-right" />

            {/* Header */}
            <div className="flex items-center justify-between flex-wrap px-4 gap-1 py-3" dir={dir}>
                <h1 className="text-xl font-bold py-0">{t('tripActivities.title')}</h1>
                <div className="flex flex-wrap gap-3">
                    {/* <PrintTableButton
                        tableId="tripActivitiesTable"
                        title={t('tripActivities.reportTitle')}
                        tripId={tripId}
                        rowsPerPage={10}
                    /> */}
                    {/* Export Excel */}
                    <ExportExcelButton
                        fileName={`trip_${tripId}_activities_fullReport`}
                        sheetName="Trip Activities Report"
                        tables={excelTables}
                        horizontal={true}
                        rtl={dir === 'rtl'}
                    />
                    <PrintButton
                        contentRenderer={ActivityReportPrintable}
                        contentProps={{ details: tripDetail, activities }}
                        dir={dir}
                        // label="Print Trip Activities Report"
                        className="p-button-outlined bg-white border !border-green-600 rounded-sm px-4 py-2 text-green-700"
                    />

                    {/* Export PDF */}
                    <ExportPdfButton
                        fileName={`trip-${tripId}-activities-PDF`}
                        tripDetail={tripDetail}
                        tripActivities={activities}
                    />
                    <Button
                        variant="secondary"
                        className="p-button-outlined bg-green-700 !border-green-700 rounded-sm px-4 py-2 hover:bg-green-800 text-white"
                        onClick={() => setIsCreateOpen(true)}>
                        {t('tripActivities.create')}
                    </Button>
                    <>
                        {/* 🔹 Button that opens the dialog */}
                        <Button
                            onClick={() => {
                                setSelectedActivityId(null); // Clear activityId filter for deleted activities
                                setIsDeletedLogsOpen(true);
                            }}
                            variant="outline"
                            className="flex items-center gap-2 border border-red-400 text-red-400 bg-white rounded-sm px-3 py-2 hover:bg-red-50">
                            <span>{t('activityLogs.deletedActivities')}</span>
                        </Button>

                        {/* 🔹 edited activities dialog */}
                        <ActivityLogsDialog
                            open={logsOpen}
                            onOpenChange={setLogsOpen}
                            title={t('activityLogs.title')}
                            params={{
                                pageNumber: 1,
                                pageSize: PAGE_SIZE,
                                activityId: selectedActivityId ?? undefined,
                            }}
                        />
                        {/* deleted activities dialog */}
                        <ActivityLogsDialog
                            open={isDeletedLogsOpen}
                            onOpenChange={setIsDeletedLogsOpen}
                            title={t('tripActivities.deletedActivitiesLog')}
                            showDeleteReason={true}
                            params={{
                                pageNumber: 1,
                                pageSize: PAGE_SIZE,
                                isDeleted: true,
                                activityId: selectedActivityId ?? undefined,
                            }}
                        />
                    </>
                </div>
            </div>

            {/* DataTable */}
            <div className="_table_container flex-grow-1">
                <DataTable
                    id="tripActivitiesTable"
                    key={i18n.language}
                    value={activities}
                    dir={dir}
                    scrollable
                    scrollHeight="81vh"
                    loading={isLoading}
                    tableStyle={{ minWidth: '60rem' }}
                    className="shadow-md border border-gray-200"
                    lazy
                    totalRecords={pagination.totalCount}
                    virtualScrollerOptions={{
                        itemSize: 80,
                        showLoader: true,
                        loading: isLoading,
                        onLazyLoad: (event: VirtualScrollerLazyEvent) => {
                            const nextPage = Math.floor((event.first as number) / PAGE_SIZE) + 1;
                            loadMoreTripActivities({
                                tripId,
                                pageNumber: nextPage,
                                pageSize: PAGE_SIZE,
                            });
                        },
                    }}
                    emptyMessage={isLoading ? t('loading') : t('common.noDataExist')}>
                    <Column
                        field="origin"
                        header={t('tripActivities.employee')}
                        bodyClassName="text-center"
                        body={(row) => row.origin ?? '-'}
                        headerClassName="bg-gray-100 font-semibold text-sm"
                    />
                    <Column
                        field="createdAt"
                        header={t('tripActivities.date')}
                        body={(row) => formatLocalizedDate(row.createdAt)}
                        bodyClassName="text-center"
                        headerClassName="bg-gray-100 font-semibold text-sm"
                    />
                    <Column
                        field="status"
                        header={t('tripActivities.state')}
                        body={(row) => localized(row.status?.name)}
                        bodyClassName="text-center"
                        headerClassName="bg-gray-100 font-semibold text-sm"
                    />
                    <Column
                        field="action"
                        header={t('tripActivities.action')}
                        body={(row) => localized(row.action?.name)}
                        bodyClassName="text-center"
                        headerClassName="bg-gray-100 font-semibold text-sm"
                    />
                    <Column
                        field="details"
                        header={t('tripActivities.details')}
                        bodyClassName="text-center"
                        headerClassName="bg-gray-100 font-semibold text-sm"
                    />
                    <Column
                        field="location"
                        header={t('tripActivities.location')}
                        body={(row) => (
                            <span
                                className="text-blue-600 hover:text-blue-800 cursor-pointer underline"
                                onClick={() =>
                                    navigate(`/trips/${tripId}/details?activityId=${row.id}&isOpen=true&view=map`, {
                                        replace: true,
                                    })
                                }
                                title={t('tripActivities.clickToViewOnMap')}>
                                {`${row.location.latitude}, ${row.location.longitude}`}
                            </span>
                        )}
                        bodyClassName="text-center"
                        headerClassName="bg-gray-100 font-semibold text-sm"
                    />
                    <Column
                        field="action"
                        header={t('common.procedures')}
                        body={actionsTemplate}
                        bodyClassName="text-center"
                        headerClassName="bg-gray-100 font-semibold text-sm"
                    />
                </DataTable>
            </div>

            {/* Delete dialog */}
            <DeleteActivityDialog
                open={isDeleteOpen}
                onOpenChange={setIsDeleteOpen}
                reason={deleteReason}
                onReasonChange={setDeleteReason}
                onConfirm={handleConfirmDelete}
                loading={isDeleting}
                dir={dir === 'rtl' ? 'rtl' : 'ltr'}
            />

            {/* Edit dialog */}
            <ActivityLogDialog
                mode="edit"
                open={isEditOpen}
                onOpenChange={setIsEditOpen}
                tripId={tripId}
                activityId={editTargetId!}
                initial={editInitial}
                onSuccess={() => {
                    showSuccess(t('tripActivities.success'), t('tripActivities.updatedSuccessfully'));
                    setIsCreateOpen(false);
                }}
                onError={() => showError(t('common.error'), t('tripActivities.updateFailed'))}
            />

            {/* Create dialog */}
            <ActivityLogDialog
                mode="create"
                open={isCreateOpen}
                onOpenChange={setIsCreateOpen}
                tripId={tripId}
                onSuccess={() => {
                    showSuccess(t('tripActivities.success'), t('tripActivities.created'));
                    setIsCreateOpen(false);
                }}
                onError={() => showError(t('common.error'), t('tripActivities.createFailed'))}
            />
        </div>
    );
}
