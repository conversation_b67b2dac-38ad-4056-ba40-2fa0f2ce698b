import { useEffect } from 'react';
import { useParams } from 'react-router-dom';

import { TripDetailsMap } from '@/components/common/google-map/TripDetailsMap';
import { useTripLocationsLookups } from '@/stores/trip-location.store';
import { usePortMapMarkers } from '@/stores/ports.store';
import { useMapPointLookups, useMapPointsStore } from '@/stores/map-points.store';
import { useDisplaySettingsStore } from '@/stores/display-settings.store';
import { useTripLocationStore } from '@/stores/trip-location.store';
import { MapPointEntryType } from '@/shared/enums';
import { useTripActivitiesStore } from '@/stores/trip-activities.store';
import { useTripPingsStore } from '@/stores/trip-ping.store';

import TripMapMenu from './TripMapMenu';

export default function TripMap() {
    const loadMapPoints = useMapPointsStore((state) => state.loadMapPoints);
    const loadTripLocations = useTripLocationStore((state) => state.loadTripLocations); // TODO:: with current implementation this will result in multiple api call and maybe some performance degradation, need to optimize
    const loadTripActivities = useTripActivitiesStore((s) => s.loadTripActivities);
    const loadTripPings = useTripPingsStore((s) => s.loadTripPings);
    const mapPoints = useMapPointLookups();
    const ports = usePortMapMarkers();

    const params = useParams();
    // Get tripId from route parameters (e.g., /trips/:Id/details)
    const tripId = Number(params.id);

    useEffect(() => {
        if (tripId) loadTripPings({ tripId, page: 1, pageSize: 1000 });
    }, [tripId, loadTripPings]);

    useEffect(() => {
        if (tripId) loadTripActivities({ tripId, pageNumber: 1, pageSize: 1000 });
    }, [tripId, loadTripActivities]);

    // todo: trigger when loading user filters from cache
    const settings = useDisplaySettingsStore((state) => state.settings);
    useEffect(() => {
        loadMapPoints({
            pageSize: 1000,
            pageNumber: 1,
            entryTypes: [
                settings.checkpoints.police ? MapPointEntryType.POLICE_STATION : null,
                settings.checkpoints.customs ? MapPointEntryType.CHECKPOINT : null,
                settings.checkpoints.suspiciousGeofences ? MapPointEntryType.SUSPECTED_AREA : null,
                MapPointEntryType.NONE,
            ].filter((x): x is MapPointEntryType => x != null),
        });
        loadTripLocations({
            pageSize: 1000,
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const trip = useTripLocationsLookups().find((t) => t.id === tripId);

    if (!trip) return null;
    const mapCenter = { lat: Number(trip.location.latitude), lng: Number(trip.location.longitude) };
    return <TripDetailsMap trip={trip} center={mapCenter} points={mapPoints} ports={ports} menu={<TripMapMenu />} />;
}
