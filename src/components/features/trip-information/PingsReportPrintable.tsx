import { forwardRef } from 'react';

import type { TripDetail } from '@/infrastructure/api/trips/types';
import { SaudiCustomsTuHeader } from '@/components/common/printing-templates/SaudiCustomsTuHeader';
import { TripDetailsSection } from '@/components/common/printing-templates/TripDetailsSection';
import type { TripLocation } from '@/infrastructure/api/trip-pings/types';

import { PingsSection } from './PingsSection'; // Assuming you've created PingsSection as before

type Props = {
    details: TripDetail | null;
    pings: TripLocation[];
    dir?: 'rtl' | 'ltr';
};

const PingsReportPrintable = forwardRef<HTMLDivElement, Props>(({ details, pings, dir = 'rtl' }, ref) => {
    return (
        <div ref={ref} dir={dir} className="print:bg-white">
            {/* PAGE */}
            <div
                className="mx-auto bg-white text-black"
                // style={{
                //     width: '250mm', // Adjusted width if needed
                //     minHeight: '297mm',
                //     padding: '12mm',
                //     boxSizing: 'border-box',
                // }}
            >
                <SaudiCustomsTuHeader />

                {/* Trip details (first) */}
                {details && <TripDetailsSection tripDetail={details} />}

                {/* Optional break before pings if you want them to start on a new page */}
                <div className="print:break-before-page"></div>

                {/* Pings section */}
                <PingsSection pings={pings} />
            </div>
        </div>
    );
});

PingsReportPrintable.displayName = 'PingsReportPrintable';
export default PingsReportPrintable;
