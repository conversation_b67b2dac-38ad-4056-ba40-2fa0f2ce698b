import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { useTranslation } from 'react-i18next';
import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { RefreshCw as RefreshCwIcon } from 'lucide-react';

import { Checkbox } from '@/components/common/ui/Checkbox';
import { useTripLogsStore, useTripLogsLookups } from '@/stores/trip-logs.store';
import { formatLocalizedDate } from '@/shared/utils/format-date.utils';
import { Scope } from '@/shared/enums';
import { useLocalized } from '@/shared/hooks/use-localized.hook';

const PAGE_SIZE = 30;

export default function EventTabView() {
    const { t, i18n } = useTranslation();
    const dir = i18n.dir();
    const params = useParams();
    const tripId = params.tripId || params.id;
    const { localized } = useLocalized();
    const { loadTripLogs, isLoading, pagination, clearTripLogs } = useTripLogsStore();
    const logs = useTripLogsLookups();

    const [, setPage] = useState(1);
    const [isFilterOpen, setIsFilterOpen] = useState(false);

    const filterOptions = [
        { label: t('events.trip'), value: 'trip' },
        { label: t('events.alert'), value: 'alert' },
    ];

    // Initialize with all filters selected by default
    const [selectedFilters, setSelectedFilters] = useState<string[]>(filterOptions.map((opt) => opt.value));

    const getScopeFromFilters = (filters: string[]): Scope | undefined => {
        if (filters.length === 1) {
            return filters[0] === 'trip' ? Scope.Trip : Scope.Alert;
        }
        return undefined;
    };

    // 🔹 Load logs on mount or language change
    useEffect(() => {
        clearTripLogs();
        setPage(1);

        if (tripId) {
            const scope = getScopeFromFilters(selectedFilters);
            loadTripLogs({
                tripId: Number(tripId),
                pageNumber: 1,
                pageSize: PAGE_SIZE,
                scope,
            });
        }
    }, [i18n.language, tripId, selectedFilters, clearTripLogs, loadTripLogs]);

    const toggleFilter = (value: string) => {
        const newFilters = selectedFilters.includes(value)
            ? selectedFilters.filter((v) => v !== value)
            : [...selectedFilters, value];
        setSelectedFilters(newFilters);
        clearTripLogs();
        setPage(1);

        if (tripId) {
            const scope = getScopeFromFilters(newFilters);
            loadTripLogs({
                tripId: Number(tripId),
                pageNumber: 1,
                pageSize: PAGE_SIZE,
                scope,
            });
        }
    };

    const toggleSelectAll = (checked: boolean) => {
        const newFilters = checked ? filterOptions.map((opt) => opt.value) : [];
        setSelectedFilters(newFilters);
        clearTripLogs();
        setPage(1);

        if (tripId) {
            const scope = getScopeFromFilters(newFilters);
            loadTripLogs({
                tripId: Number(tripId),
                pageNumber: 1,
                pageSize: PAGE_SIZE,
                scope,
            });
        }
    };

    const resetFilters = () => {
        setSelectedFilters([]);
        clearTripLogs();
        setPage(1);

        if (tripId) {
            const scope = getScopeFromFilters([]);
            loadTripLogs({
                tripId: Number(tripId),
                pageNumber: 1,
                pageSize: PAGE_SIZE,
                scope,
            });
        }
    };

    return (
        <div className="w-full">
            {/* Title Bar */}
            <div
                className="flex items-center justify-between px-4 py-3 border-b border-gray-200 bg-gray-50 rounded-t-md"
                dir={dir}>
                <h2 className="text-gray-800 font-semibold text-lg">{t('events.title')}</h2>

                <div className="flex items-center gap-2 relative">
                    {/* Dropdown Filter */}
                    <div className="relative">
                        <button
                            onClick={() => setIsFilterOpen(!isFilterOpen)}
                            className="p-button-outlined bg-white border !border-black-400 rounded-sm px-4 py-2 w-48 flex justify-between items-center">
                            <span>
                                {selectedFilters.length > 0
                                    ? selectedFilters.length === filterOptions.length
                                        ? t('events.all')
                                        : filterOptions
                                              .filter((opt) => selectedFilters.includes(opt.value))
                                              .map((opt) => opt.label)
                                              .join(' or ')
                                    : t('')}
                            </span>
                            <span className="ml-2">▾</span>
                        </button>

                        {isFilterOpen && (
                            <div
                                dir={dir}
                                className={`absolute mt-1 w-56 bg-white border border-gray-200 rounded shadow-lg z-50 ${
                                    dir === 'rtl' ? 'left-0' : 'right-0'
                                }`}>
                                <div className="p-3 space-y-1 max-h-60 overflow-auto">
                                    {/* Select All */}
                                    <Checkbox
                                        isSelectAll={true}
                                        label={t('filter.selectAll')}
                                        checked={
                                            selectedFilters.length === filterOptions.length && filterOptions.length > 0
                                        }
                                        onChange={toggleSelectAll}
                                    />

                                    {/* Filter Options */}
                                    {filterOptions.map((opt) => (
                                        <Checkbox
                                            key={opt.value}
                                            label={opt.label}
                                            checked={selectedFilters.includes(opt.value)}
                                            onChange={() => toggleFilter(opt.value)}
                                        />
                                    ))}
                                </div>

                                {/* Reset Button */}
                                <div className="py-1 flex items-center justify-end border-t border-gray-200">
                                    <button
                                        onClick={resetFilters}
                                        className="flex items-center gap-1 px-3 py-1 border border-green-500 text-green-600 rounded hover:bg-green-50 mx-2 my-1">
                                        <RefreshCwIcon size={14} />
                                        {t('filter.reset')}
                                    </button>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </div>

            {/* DataTable */}
            <div className="_table_container flex-grow-1">
                <DataTable
                    key={i18n.language}
                    value={logs}
                    dir={dir}
                    scrollable
                    scrollHeight="72vh"
                    lazy
                    totalRecords={pagination.totalCount}
                    tableStyle={{ minWidth: '60rem' }}
                    className="shadow-md border border-gray-200 rounded-b-md text-sm"
                    virtualScrollerOptions={{
                        lazy: true,
                        itemSize: 50,
                        showLoader: true,
                        loading: isLoading,
                    }}
                    emptyMessage={t('loading')}>
                    {/* <Column
                        field="event"
                        header={t('events.event')}
                        bodyClassName="text-center"
                        headerClassName="bg-gray-100 text-gray-800 font-semibold text-sm border-b border-gray-300 py-3 text-center"
                        body={(rowData) => Scope[rowData.scope]}
                    /> */}

                    <Column
                        field="user.name"
                        header={t('events.user')}
                        bodyClassName="text-center"
                        headerClassName="bg-gray-100 text-gray-800 font-semibold text-sm border-b border-gray-300 py-3 text-center"
                        body={(rowData) => rowData.user ?? '-'}
                    />

                    <Column
                        field="action"
                        header={t('events.details')}
                        bodyClassName="text-center"
                        headerClassName="bg-gray-100 text-gray-800 font-semibold text-sm border-b border-gray-300 py-3 text-center"
                        body={(rowData) => localized(rowData.log)}
                    />
                    {/* <Column
                        field="scope"
                        header={t('events.scope')}
                        bodyClassName="text-center"
                        headerClassName="bg-gray-100 text-gray-800 font-semibold text-sm border-b border-gray-300 py-3 text-center"
                        body={(rowData) => Scope[rowData.scope]}
                    /> */}

                    <Column
                        field="createdAt"
                        header={t('events.time')}
                        bodyClassName="text-center"
                        headerClassName="bg-gray-100 text-gray-800 font-semibold text-sm border-b border-gray-300 py-3 text-center"
                        body={(rowData) => formatLocalizedDate(rowData.createdAt)}
                    />
                </DataTable>
            </div>
        </div>
    );
}
