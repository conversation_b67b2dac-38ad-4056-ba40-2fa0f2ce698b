import { forwardRef } from 'react';

import type { TripDetail } from '@/infrastructure/api/trips/types';
import type { TripActivity } from '@/infrastructure/api/trip-activities/types';
import { SaudiCustomsTuHeader } from '@/components/common/printing-templates/SaudiCustomsTuHeader';
import { TripDetailsSection } from '@/components/common/printing-templates/TripDetailsSection';

import { ActivitySection } from './ActivitySection'; // Assuming you've created ActivitySection

type Props = {
    details: TripDetail | null;
    activities: TripActivity[];
    dir?: 'rtl' | 'ltr';
};

const ActivityReportPrintable = forwardRef<HTMLDivElement, Props>(({ details, activities, dir = 'rtl' }, ref) => {
    return (
        <div ref={ref} dir={dir} className="print:bg-white">
            {/* PAGE */}
            <div
                className="mx-auto bg-white text-black"
                // style={{
                //     width: '250mm', // Adjusted width for more space
                //     minHeight: '297mm',
                //     padding: '12mm',
                //     boxSizing: 'border-box',
                // }}
            >
                <SaudiCustomsTuHeader />

                {/* Trip details */}
                {details && <TripDetailsSection tripDetail={details} />}

                {/* Optional break before activities */}
                <div className="print:break-before-page"></div>

                {/* Activity section */}
                <ActivitySection activities={activities} />
            </div>
        </div>
    );
});

ActivityReportPrintable.displayName = 'ActivityReportPrintable';
export default ActivityReportPrintable;
