import { useTranslation } from 'react-i18next';

import { formatLocalizedDate } from '@/shared/utils/format-date.utils';
import type { TripLocation } from '@/infrastructure/api/trip-pings/types';
import { useLocalized } from '@/shared/hooks/use-localized.hook';

interface PingsSectionProps {
    pings: TripLocation[];
}

export function PingsSection({ pings }: PingsSectionProps) {
    const { t } = useTranslation();
    const { localized } = useLocalized();

    // Helper functions

    const formatDate = (dateValue: string | Date | null | undefined): string => {
        if (!dateValue) return 'N/A';
        try {
            const dateStr = dateValue instanceof Date ? dateValue.toISOString() : dateValue;
            return formatLocalizedDate(dateStr);
        } catch {
            return 'N/A';
        }
    };

    // Empty state handling
    if (!pings || pings.length === 0) {
        return (
            <div className="w-full max-w-4xl mx-auto bg-white p-4 print:p-2 print:max-w-none print:mx-0">
                <div className="mb-6 print:mb-4">
                    <h2 className="text-xl font-bold text-center mb-4 text-gray-800 print:text-lg print:mb-2">
                        {t('pings.title')}
                    </h2>
                    <p className="text-center text-gray-500 py-4 print:text-gray-700">{t('common.noPings')}</p>
                </div>
            </div>
        );
    }

    return (
        <div className="w-full max-w-4xl mx-auto bg-white p-4 print:p-2 print:max-w-none print:mx-0">
            <div className="mb-6 print:mb-4">
                <h2 className="text-xl font-bold text-center mb-4 text-gray-800 print:text-lg print:mb-2">
                    {t('pings.title')}
                </h2>

                <div className="border border-gray-200 rounded overflow-hidden print:border-gray-400 print:rounded-none print:break-inside-avoid">
                    <table className="w-full border-collapse">
                        {/* Table Header */}
                        <thead className="bg-gray-50 print:bg-gray-100">
                            <tr>
                                <th className="px-3 py-2 text-center font-medium text-gray-700 border-b border-r border-gray-200 print:px-2 print:py-1 print:text-black print:border-gray-400">
                                    {t('common.address')}
                                </th>
                                <th className="px-3 py-2 text-center font-medium text-gray-700 border-b border-r border-gray-200 print:px-2 print:py-1 print:text-black print:border-gray-400">
                                    {t('pings.lat')}
                                </th>
                                <th className="px-3 py-2 text-center font-medium text-gray-700 border-b border-r border-gray-200 print:px-2 print:py-1 print:text-black print:border-gray-400">
                                    {t('pings.long')}
                                </th>
                                <th className="px-3 py-2 text-center font-medium text-gray-700 border-b border-r border-gray-200 print:px-2 print:py-1 print:text-black print:border-gray-400">
                                    {t('common.timestamp')}
                                </th>
                                <th className="px-3 py-2 text-center font-medium text-gray-700 border-b border-r border-gray-200 print:px-2 print:py-1 print:text-black print:border-gray-400">
                                    {t('common.speed')}
                                </th>
                            </tr>
                        </thead>
                        {/* Table Body */}
                        <tbody>
                            {pings.map((ping, index) => (
                                <tr
                                    key={ping.id}
                                    className={index < pings.length - 1 ? 'border-b border-gray-200' : ''}>
                                    {/* Timestamp */}
                                    <td className="px-3 py-2 text-center text-gray-600 border-r border-gray-200 print:px-2 print:py-1 print:text-black print:border-gray-400">
                                        {localized(ping.address)}
                                    </td>
                                    {/* Latitude */}
                                    <td className="px-3 py-2 text-center text-gray-600 border-r border-gray-200 print:px-2 print:py-1 print:text-black print:border-gray-400">
                                        {ping.location.latitude}
                                    </td>
                                    {/* Longitude */}
                                    <td className="px-3 py-2 text-center text-gray-600 border-r border-gray-200 print:px-2 print:py-1 print:text-black print:border-gray-400">
                                        {ping.location.longitude}
                                    </td>
                                    {/* Speed */}
                                    <td className="px-3 py-2 text-center text-gray-600 border-r border-gray-200 print:px-2 print:py-1 print:text-black print:border-gray-400">
                                        {formatDate(ping.serverDateTime)}
                                    </td>
                                    {/* Status */}
                                    <td className="px-3 py-2 text-center text-gray-600 print:px-2 print:py-1 print:text-black">
                                        {ping.speed}
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    );
}
