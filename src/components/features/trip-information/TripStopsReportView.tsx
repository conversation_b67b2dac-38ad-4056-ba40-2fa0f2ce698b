import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { useTranslation } from 'react-i18next';
import { useEffect } from 'react';
import { useParams } from 'react-router-dom';

import { useTripStopsStore } from '@/stores/trip-stops.store';
import { formatLocalizedDate } from '@/shared/utils/format-date.utils';
import type { TripStop } from '@/infrastructure/api/trip-stops/types';
import { ExportExcelButton } from '@/components/common/ui/ExportExcelButton';
import { useTripDetailStore } from '@/stores/trip-detail.store';
import { useLocalized } from '@/shared/hooks/use-localized.hook';
import { tripWithStopsExcelMapper } from '@/mappers/tripWithStopsExcel.mapper';
import PrintButton from '@/components/common/printing-templates/PrintButton';
import StopReportPrintable from '@/components/common/printing-templates/StopsReportPrintable';
import { ExportPdfButton } from '@/components/common/ui/ExportPdfButton';

export default function TripStopsReportView() {
    const { t, i18n } = useTranslation();
    const { localized, dir } = useLocalized();
    const params = useParams();

    // Get tripId from route parameters (e.g., /trips/:tripId/details)
    const tripId = params.tripId || params.id;

    // Use the store
    const { tripStops, isLoading, loadTripStops } = useTripStopsStore();

    // Load trip stops when component mounts or tripId changes
    useEffect(() => {
        if (tripId) {
            loadTripStops({ tripId: parseInt(tripId) });
        }
    }, [tripId, loadTripStops]);

    // Print handler

    const { tripDetail } = useTripDetailStore();
    const excelTables = tripWithStopsExcelMapper(tripDetail, tripStops, t, localized);

    return (
        <div className="w-full">
            {/* Title Bar */}
            <div className="flex items-center justify-between flex-wrap px-4 gap-1 py-3" dir={dir}>
                {/* Title */}
                <h1 className="text-xl font-bold py-0 text-gray-800">{t('stops.title', 'Stops Report')}</h1>

                {/* Actions */}
                <div className="flex flex-wrap gap-3">
                    {/* Print button */}

                    {/* Export Excel */}
                    <ExportExcelButton
                        fileName={`trip_${tripId}_stops_fullReport`}
                        sheetName="Trip Stops Report"
                        tables={excelTables}
                        horizontal={true}
                        rtl={dir === 'rtl'}
                    />
                    <PrintButton
                        contentRenderer={StopReportPrintable}
                        contentProps={{ details: tripDetail, tripStops }}
                        dir={dir}
                        label="Print Trip Stops Report"
                        className="p-button-outlined bg-white border !border-green-600 rounded-sm px-4 py-2 text-green-700"
                    />

                    {/* Export PDF */}
                    <ExportPdfButton
                        fileName={`trip-${tripId}-stops-PDF`}
                        tripDetail={tripDetail}
                        tripStops={tripStops}
                    />
                </div>
            </div>

            {/* DataTable */}
            <div className="_table_container flex-grow-1">
                <DataTable
                    key={i18n.language}
                    value={tripStops}
                    dir={dir}
                    scrollable
                    scrollHeight="72vh"
                    loading={isLoading}
                    tableStyle={{ minWidth: '60rem' }}
                    className="shadow-md border border-gray-200 rounded-b-md text-sm"
                    emptyMessage={isLoading ? t('loading') : t('common.noDataExist')}>
                    <Column
                        field="address"
                        header={t('stops.placeName', 'Place Name')}
                        bodyClassName="text-center"
                        headerClassName="bg-gray-100 text-gray-800 font-semibold text-sm border-b border-gray-300 py-3 text-center"
                        body={(rowData: TripStop) => localized(rowData.address)}
                    />

                    <Column
                        field="fromTime"
                        header={t('stops.from', 'From')}
                        bodyClassName="text-center"
                        headerClassName="bg-gray-100 text-gray-800 font-semibold text-sm border-b border-gray-300 py-3 text-center"
                        body={(rowData: TripStop) => formatLocalizedDate(rowData.fromTime)}
                    />

                    <Column
                        field="toTime"
                        header={t('stops.to', 'To')}
                        bodyClassName="text-center"
                        headerClassName="bg-gray-100 text-gray-800 font-semibold text-sm border-b border-gray-300 py-3 text-center"
                        body={(rowData: TripStop) => formatLocalizedDate(rowData.toTime)}
                    />

                    <Column
                        header={t('stops.duration', 'Duration')}
                        bodyClassName="text-center"
                        headerClassName="bg-gray-100 text-gray-800 font-semibold text-sm border-b border-gray-300 py-3 text-center"
                        body={(rowData: TripStop) => `${rowData.period.hours}h ${rowData.period.minutes}m`}
                    />
                </DataTable>
            </div>
        </div>
    );
}
