import { useCallback, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { IoChevronUpOutline } from 'react-icons/io5';
import { useParams } from 'react-router-dom';
import { useMap } from '@vis.gl/react-google-maps';

import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/common/ui/Tabs';
import { useMapPointsStore } from '@/stores/map-points.store';
import { useDisplaySettingsStore } from '@/stores/display-settings.store';
import { useUIStore } from '@/stores/ui.store';
import { MapPointEntryType } from '@/shared/enums';
import { useTripAlertsLookups, useTripAlertsStore } from '@/stores/trip-alerts.store';

import DisplaySettingsTap from '../monitor-board/menu-taps/display-settings-tap/DisplaySettingsTap';

import TripMapFilterBox from './TripMapFilterBox';
import { Trip<PERSON>lertChart } from './TripAlertChart';
import TripReplayMenu from './TripReplayMenu';
type TripMenuProps = {
    mapDialogRootRef?: React.RefObject<HTMLElement | null>;
};
export default function TripMapMenu({ mapDialogRootRef }: TripMenuProps) {
    const { t } = useTranslation();
    const loadMapPoints = useMapPointsStore((state) => state.loadMapPoints);

    // UI store state & actions
    const activeTab = useUIStore((s) => s.activeTab);
    const setActiveTab = useUIStore((s) => s.setActiveTab);

    const params = useParams();
    // Get tripId from route parameters (e.g., /trips/:Id/details)
    const tripId = Number(params.id);
    const loadTripAlerts = useTripAlertsStore((state) => state.loadTripAlerts);
    const tripAlerts = useTripAlertsLookups();

    // Create ref to avoid function dependency issues
    const loadTripAlertsRef = useRef(loadTripAlerts);
    loadTripAlertsRef.current = loadTripAlerts;

    // Load trip Alerts when component mounts or tripId changes
    useEffect(() => {
        if (tripId) {
            loadTripAlertsRef.current({ tripId: tripId.toString(), PageNumber: 1, PageSize: 1000 });
        }
    }, [tripId]);

    // Create ref for loadMapPoints to avoid function dependency issues
    const loadMapPointsRef = useRef(loadMapPoints);
    loadMapPointsRef.current = loadMapPoints;

    // Subscribe to display settings changes and update map points / ports
    useEffect(() => {
        const unsubscribeDisplay = useDisplaySettingsStore.subscribe(
            (state) => state.settings,
            (settings) => {
                loadMapPointsRef.current({
                    pageSize: 1000,
                    pageNumber: 1,
                    entryTypes: [
                        settings.checkpoints.police ? MapPointEntryType.POLICE_STATION : null,
                        settings.checkpoints.customs ? MapPointEntryType.CHECKPOINT : null,
                        settings.checkpoints.suspiciousGeofences ? MapPointEntryType.SUSPECTED_AREA : null,
                        MapPointEntryType.NONE,
                    ].filter((x): x is MapPointEntryType => x != null),
                });
            },
        );

        // Cleanup function to unsubscribe when component unmounts
        return () => {
            unsubscribeDisplay();
        };
    }, []);

    // Create ref for setActiveTab to avoid function dependency issues
    const setActiveTabRef = useRef(setActiveTab);
    setActiveTabRef.current = setActiveTab;

    // Always open TripMapFilterBox (AlertIcons) on component mount
    useEffect(() => {
        setActiveTabRef.current('AlertIcons');
    }, []);

    // Controlled Tabs value is activeTab from uiStore. Tabs component expects string value.
    const tabsValue = activeTab ?? '';

    const onTabsValueChange = useCallback(
        (value: string) => {
            // 'close' special action -> close tabs
            if (value === 'close') {
                if (activeTab !== null) setActiveTab(null);
                return;
            }

            // other tabs: set active tab (or null)
            if (value) {
                if (activeTab !== value) setActiveTab(value);
            } else if (activeTab !== null) setActiveTab(null);
        },
        [activeTab, setActiveTab],
    );

    // --- Change alertIcons tab position when map is take a full screen
    const [isMapTakeFullscreen, setIsMapTakeFullscreen] = useState(false);
    const map = useMap();

    useEffect(() => {
        if (!map) return;
        const handleFullscreenChange = () => {
            const fullscreenEl = document.fullscreenElement; //--- Get curren element that take a fullscreen
            const isFullscreen =
                (fullscreenEl && (fullscreenEl === map.getDiv() || map.getDiv().contains(fullscreenEl))) ?? false;
            setIsMapTakeFullscreen(isFullscreen);
        };
        document.addEventListener('fullscreenchange', handleFullscreenChange);
        return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
    }, [map]);

    return (
        <Tabs
            className={`absolute top-2.5 right-15 flex items-end`}
            value={tabsValue}
            onValueChange={onTabsValueChange}>
            <TabsList className="h-10 bg-white [&_[data-state=active]]:bg-gray-100">
                <TabsTrigger value="AlertIcons">{t('tripDetails.alertIcons')}</TabsTrigger>
                <TabsTrigger value="TripReplay">{t('tripDetails.trackReplay')}</TabsTrigger>
                <TabsTrigger value="DisplaySettings">{t('common.display_settings_button')}</TabsTrigger>
                <TabsTrigger value="Chart">{t('common.chart')}</TabsTrigger>
                <TabsTrigger value="close">
                    <IoChevronUpOutline className="size-3.2" />
                </TabsTrigger>
            </TabsList>
            <TabsContent value="AlertIcons" className="max-h-[70vh] overflow-x-hidden overflow-y-auto">
                <TripMapFilterBox position="bottom" isMapTakeFullscreen={isMapTakeFullscreen} />
            </TabsContent>
            <TabsContent value="TripReplay" className="max-h-[70vh] overflow-x-hidden overflow-y-auto">
                <TripReplayMenu />
            </TabsContent>
            <TabsContent value="DisplaySettings" className="max-h-[70vh] overflow-x-hidden overflow-y-auto">
                <DisplaySettingsTap showTripMode={false} showPorts={false} />
            </TabsContent>
            <TabsContent value="Chart" className="max-h-[80vh] w-full overflow-x-hidden overflow-y-auto">
                <TripAlertChart tripAlerts={tripAlerts} portalContainer={mapDialogRootRef?.current ?? undefined} />
            </TabsContent>
        </Tabs>
    );
}
