import { useTranslation } from 'react-i18next';

import { Button } from '@/components/common/ui/Button';
import { Card } from '@/components/common/ui/Card';
import { Icon } from '@/components/common/ui/Icon';
import Tooltip from '@/components/common/ui/Tooltip';
import { useTripReplayStore, type TripRouteReplayTypeSpeed } from '@/stores/trip-route-replay.store';

export default function TripReplayMenu() {
    const { t } = useTranslation();

    //--- Animation Status (play | pause)
    const isPlaying = useTripReplayStore((state) => state.isPlaying);
    const setIsPlaying = useTripReplayStore((state) => state.changeRoutePlayingStatus);

    //--- Animation Speed
    const playbackSpeed = useTripReplayStore((state) => state.speed);
    const setPlaybackSpeed = useTripReplayStore((state) => state.changeRoutePlayingSpeed);

    //--- Animation Direction (Rewind | Forward)
    const direction = useTripReplayStore((state) => state.direction);
    const setPlayDirection = useTripReplayStore((state) => state.changeRoutePlayingDirection);

    const handlePlayPause = () => {
        setIsPlaying(!isPlaying);
    };

    const handlePrevious = () => {
        setPlayDirection('rewind');
    };

    const handleNext = () => {
        setPlayDirection('forward');
    };

    const handleSpeedChange = () => {
        // Cycle through speed options: 1x, 1.5x, 2x
        const speeds: TripRouteReplayTypeSpeed[] = [1, 1.5, 2];
        const currentIndex = speeds.indexOf(playbackSpeed);
        const nextIndex = (currentIndex + 1) % speeds.length;
        setPlaybackSpeed(speeds[nextIndex]);
    };

    return (
        <Card className="bg-neutral-50 shadow-sm border-0 p-6 max-w-md mx-auto">
            <div className="text-center mb-6">
                <p className="text-neutral-800 text-lg font-medium leading-relaxed">{t('tripReplay.description')}</p>
            </div>

            <div className="flex items-center justify-center gap-4">
                <Tooltip tooltipMessage="tripReplay.fastForward">
                    <Button
                        variant="ghost"
                        disabled={direction === 'forward' || !isPlaying}
                        size="icon"
                        onClick={handleNext}
                        className="w-10 not-disabled:border not-disabled:border-gray-200 h-10 hover:bg-neutral-200">
                        <Icon name="rewind" className="text-neutral-600" />
                    </Button>
                </Tooltip>

                <Tooltip tooltipMessage={isPlaying ? 'tripReplay.pause' : 'tripReplay.play'}>
                    <Button
                        variant="default"
                        size="icon"
                        onClick={handlePlayPause}
                        style={{ background: isPlaying ? '#e74c3c' : '#27ae60' }}
                        className="w-12 h-12 bg-primary-600 hover:bg-primary-700 rounded-full">
                        <Icon name={isPlaying ? 'pause' : 'play'} className="text-white" />
                    </Button>
                </Tooltip>

                <Tooltip tooltipMessage="tripReplay.rewind">
                    <Button
                        variant="ghost"
                        size="icon"
                        onClick={handlePrevious}
                        disabled={direction === 'rewind' || !isPlaying}
                        className="w-10 h-10 not-disabled:border not-disabled:border-gray-200 hover:bg-neutral-200">
                        <Icon name="fastForward" className="text-neutral-600" />
                    </Button>
                </Tooltip>

                {/* Playback Speed Control */}
                <Tooltip tooltipMessage="tripReplay.speed">
                    <Button
                        variant="ghost"
                        onClick={handleSpeedChange}
                        className="px-3 py-2 h-8 w-12 bg-neutral-200 hover:bg-neutral-300 text-neutral-800 font-medium rounded-full text-sm">
                        {playbackSpeed}x
                    </Button>
                </Tooltip>
            </div>
        </Card>
    );
}
