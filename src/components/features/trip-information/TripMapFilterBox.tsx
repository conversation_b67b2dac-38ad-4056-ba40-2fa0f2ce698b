import { useEffect, useState, useRef } from 'react';
import { t } from 'i18next';
import { useParams } from 'react-router-dom';

import { cn } from '@/shared/utils/class-name.utils';
import { Button } from '@/components/common/ui/Button';
import Tooltip from '@/components/common/ui/Tooltip';
import { TooltipTrigger } from '@/components/common/ui/LibTooltip';
import { Icon, type IconName } from '@/components/common/ui/Icon';
import { useTripPingsStore } from '@/stores/trip-ping.store';
import { useTripActivitiesStore } from '@/stores/trip-activities.store';
import { useTripAlertsStore } from '@/stores/trip-alerts.store';
import { AlertTypeMapByName } from '@/shared/utils/alerts.utils';
import { useLocalized } from '@/shared/hooks/use-localized.hook';

// 1 - Define the type for each item in the filter box
type TripMapFilterBoxItem = {
    id: IconName;
    translationKey: string;
    onClick?: () => void;
};

type Position = 'top' | 'bottom';

interface TripMapFilterBoxProps {
    position: Position;
    isMapTakeFullscreen?: boolean;
}

// 2 - Main component
export default function TripMapFilterBox({ position = 'top', isMapTakeFullscreen = false }: TripMapFilterBoxProps) {
    const [selected, setSelected] = useState<IconName[]>([]);
    const loadTripPings = useTripPingsStore((state) => state.loadTripPings);
    const selectAllTripPings = useTripPingsStore((state) => state.selectAllTripPings);
    const clearSelectedTripPings = useTripPingsStore((state) => state.clearSelectedTripPings);
    const selectAllTripActivities = useTripActivitiesStore((state) => state.selectAllTripActivities);
    const clearSelectedTripActivities = useTripActivitiesStore((state) => state.clearSelectedTripActivities);

    const selectTripAlertsByType = useTripAlertsStore((state) => state.selectTripAlertsByType);
    const clearSelectedTripAlertsByType = useTripAlertsStore((state) => state.clearSelectedTripAlertsByType);
    const loadTripAlerts = useTripAlertsStore((state) => state.loadTripAlerts);

    // Get tripId from route parameters
    const params = useParams();
    const tripId = params.id;

    // Create ref to avoid function dependency issues
    const loadTripAlertsRef = useRef(loadTripAlerts);
    loadTripAlertsRef.current = loadTripAlerts;

    //--- Tap Position
    const { dir } = useLocalized();
    const isRTL = dir === 'rtl';
    const sharedClass = `${!isMapTakeFullscreen && (isRTL ? 'left-[40%] translate-x-[-55%]' : 'left-[55%] translate-x-[-40%]')}`;
    const centerElements = 'left-0 translate-x-0 flex items-center justify-center w-full';
    const positionMapper: Record<Position, string> = {
        bottom: `bottom-[2rem]  ${sharedClass} ${isMapTakeFullscreen && centerElements}`,
        top: `${isMapTakeFullscreen ? centerElements + ' top-[3.5rem]' : 'top-[9.3rem]'} ${sharedClass}`,
    };

    useEffect(() => {
        if (tripId) {
            loadTripAlertsRef.current({ tripId, PageNumber: 1, PageSize: 1000 });
        }
    }, [tripId]);

    // 3 - Define filter items
    const items: TripMapFilterBoxItem[] = [
        {
            id: 'activity',
            translationKey: 'tripDetails.tripMap.activity',
            onClick: () => {
                if (selected.includes('activity')) {
                    clearSelectedTripActivities();
                } else {
                    selectAllTripActivities();
                }
            },
        },
        {
            id: 'tripRoute',
            translationKey: 'tripDetails.tripMap.tripRoute',
            onClick: () => {
                // TODO:: need to optimize, cache qpi call for short period to prevent spamming the server + selectAll should handle initialLoad if data is not loaded yet - @0x03r00
                if (selected.includes('tripRoute')) {
                    clearSelectedTripPings();
                } else {
                    if (tripId) {
                        loadTripPings({ tripId: Number(tripId), page: 1, pageSize: 1000 });
                        selectAllTripPings();
                    }
                }
            },
        },
        {
            id: 'truckStopped',
            translationKey: 'tripDetails.tripMap.truckStopped',
            onClick: () => {
                if (selected.includes('truckStopped')) {
                    clearSelectedTripAlertsByType(AlertTypeMapByName['truck_stopped']);
                } else {
                    selectTripAlertsByType(AlertTypeMapByName['truck_stopped']);
                }
            },
        },
        {
            id: 'trackerTamper',
            translationKey: 'tripDetails.tripMap.trackerTamper',
            onClick: () => {
                if (selected.includes('trackerTamper')) {
                    clearSelectedTripAlertsByType(AlertTypeMapByName['tracker_tamper']);
                } else {
                    selectTripAlertsByType(AlertTypeMapByName['tracker_tamper']);
                }
            },
        },
        {
            id: 'trackerDropped',
            translationKey: 'tripDetails.tripMap.trackerDropped',
            onClick: () => {
                if (selected.includes('trackerDropped')) {
                    clearSelectedTripAlertsByType(AlertTypeMapByName['tracker_dropped']);
                } else {
                    selectTripAlertsByType(AlertTypeMapByName['tracker_dropped']);
                }
            },
        },
        {
            id: 'lockTamper',
            translationKey: 'tripDetails.tripMap.lockTamper',
            onClick: () => {
                if (selected.includes('lockTamper')) {
                    clearSelectedTripAlertsByType(AlertTypeMapByName['lock_tamper']);
                } else {
                    selectTripAlertsByType(AlertTypeMapByName['lock_tamper']);
                }
            },
        },
        {
            id: 'lockOpen',
            translationKey: 'tripDetails.tripMap.lockOpen',
            onClick: () => {
                if (selected.includes('lockOpen')) {
                    clearSelectedTripAlertsByType(AlertTypeMapByName['lock_open']);
                } else {
                    selectTripAlertsByType(AlertTypeMapByName['lock_open']);
                }
            },
        },
        {
            id: 'lockConnectionLost',
            translationKey: 'tripDetails.tripMap.lockConnectionLost',
            onClick: () => {
                if (selected.includes('lockConnectionLost')) {
                    clearSelectedTripAlertsByType(AlertTypeMapByName['lock_connection_lost']);
                } else {
                    selectTripAlertsByType(AlertTypeMapByName['lock_connection_lost']);
                }
            },
        },
        {
            id: 'trackerBatteryLow',
            translationKey: 'tripDetails.tripMap.trackerBatteryLow',
            onClick: () => {
                if (selected.includes('trackerBatteryLow')) {
                    clearSelectedTripAlertsByType(AlertTypeMapByName['tracker_battery_low']);
                } else {
                    selectTripAlertsByType(AlertTypeMapByName['tracker_battery_low']);
                }
            },
        },
        {
            id: 'lockBatteryLow',
            translationKey: 'tripDetails.tripMap.lockBatteryLow',
            onClick: () => {
                if (selected.includes('lockBatteryLow')) {
                    clearSelectedTripAlertsByType(AlertTypeMapByName['lock_low_battery']);
                } else {
                    selectTripAlertsByType(AlertTypeMapByName['lock_low_battery']);
                }
            },
        },
        {
            id: 'gsmSignalLost',
            translationKey: 'tripDetails.tripMap.gsmSignalLost',
            onClick: () => {
                if (selected.includes('gsmSignalLost')) {
                    clearSelectedTripAlertsByType(AlertTypeMapByName['gsm_signal_lost']);
                } else {
                    selectTripAlertsByType(AlertTypeMapByName['gsm_signal_lost']);
                }
            },
        },
        {
            id: 'gpsSignalLost',
            translationKey: 'tripDetails.tripMap.gpsSignalLost',
            onClick: () => {
                if (selected.includes('gpsSignalLost')) {
                    clearSelectedTripAlertsByType(AlertTypeMapByName['gps_signal_lost']);
                } else {
                    selectTripAlertsByType(AlertTypeMapByName['gps_signal_lost']);
                }
            },
        },
        {
            id: 'entringGeofence',
            translationKey: 'tripDetails.tripMap.entringGeofence',
            onClick: () => {
                if (selected.includes('entringGeofence')) {
                    clearSelectedTripAlertsByType(AlertTypeMapByName['geofence_entry_breach']);
                } else {
                    selectTripAlertsByType(AlertTypeMapByName['geofence_entry_breach']);
                }
            },
        },
        {
            id: 'leavingGeofence',
            translationKey: 'tripDetails.tripMap.leavingGeofence',
            onClick: () => {
                if (selected.includes('leavingGeofence')) {
                    clearSelectedTripAlertsByType(AlertTypeMapByName['geofence_exit_breach']);
                } else {
                    selectTripAlertsByType(AlertTypeMapByName['geofence_exit_breach']);
                }
            },
        },
        {
            id: 'trackerConnectionLost',
            translationKey: 'tripDetails.tripMap.trackerConnectionLost',
            onClick: () => {
                if (selected.includes('trackerConnectionLost')) {
                    clearSelectedTripAlertsByType(AlertTypeMapByName['tracker_connection_lost']);
                } else {
                    selectTripAlertsByType(AlertTypeMapByName['tracker_connection_lost']);
                }
            },
        },
        {
            id: 'overSpeeding',
            translationKey: 'tripDetails.tripMap.overSpeeding',
            onClick: () => {
                if (selected.includes('overSpeeding')) {
                    clearSelectedTripAlertsByType(AlertTypeMapByName['over_speeding']);
                } else {
                    selectTripAlertsByType(AlertTypeMapByName['over_speeding']);
                }
            },
        },
        {
            id: 'wrongDirection',
            translationKey: 'tripDetails.tripMap.wrongDirection',
            onClick: () => {
                if (selected.includes('wrongDirection')) {
                    clearSelectedTripAlertsByType(AlertTypeMapByName['wrong_direction']);
                } else {
                    selectTripAlertsByType(AlertTypeMapByName['wrong_direction']);
                }
            },
        },
        {
            id: 'suspiciousArea',
            translationKey: 'tripDetails.tripMap.suspiciousArea',
            onClick: () => {
                if (selected.includes('suspiciousArea')) {
                    clearSelectedTripAlertsByType(AlertTypeMapByName['suspected_area']);
                } else {
                    selectTripAlertsByType(AlertTypeMapByName['suspected_area']);
                }
            },
        },
        {
            id: 'fourHourExceeded',
            translationKey: 'tripDetails.tripMap.fourHourExceeded',
            onClick: () => {
                if (selected.includes('fourHourExceeded')) {
                    clearSelectedTripAlertsByType(AlertTypeMapByName['4_hours_exceeded']);
                } else {
                    selectTripAlertsByType(AlertTypeMapByName['4_hours_exceeded']);
                }
            },
        },
    ];

    // 4 - Toggle selection for a single item
    const toggleSelect = (id: IconName) => {
        setSelected((prev) => (prev.includes(id) ? prev.filter((x) => x !== id) : [...prev, id]));
    };

    // 5 - Toggle select/deselect all items
    const toggleAll = () => {
        const selectedItems = [...selected];

        if (selectedItems.length === items.length) {
            setSelected([]);

            items.forEach((i) => i.onClick?.());
        } else {
            setSelected(items.map((i) => i.id));
            items.filter((i) => !selectedItems.includes(i.id)).forEach((i) => i.onClick?.());
        }
    };

    return (
        <div className={`fixed ${positionMapper[position === 'top' ? 'top' : 'bottom']}`}>
            <div className="flex items-center gap-2 p-2 bg-white rounded-xl shadow-md overflow-x-auto ">
                {/* Button to select/unselect all items */}
                <Button variant="outline" size="sm" onClick={toggleAll} className="whitespace-nowrap">
                    {selected.length === items.length ? t('filter.deSelectAll') : t('filter.selectAll')}
                </Button>

                {/* Render the rest of the filter buttons */}
                {items.map(({ id, translationKey, onClick }) => (
                    <Tooltip key={id} tooltipMessage={t(translationKey)}>
                        <TooltipTrigger asChild>
                            <button
                                onClick={() => {
                                    toggleSelect(id);
                                    onClick?.();
                                }}
                                className={cn(
                                    'p-2 rounded-md border transition',
                                    selected.includes(id)
                                        ? 'border-red-500 bg-gray-100'
                                        : 'border-transparent hover:bg-gray-50',
                                )}>
                                <Icon name={id} className="w-5 h-5 text-red-500" />
                            </button>
                        </TooltipTrigger>
                    </Tooltip>
                ))}
            </div>
        </div>
    );
}
