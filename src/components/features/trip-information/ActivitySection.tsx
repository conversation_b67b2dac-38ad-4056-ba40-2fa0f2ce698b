import { useTranslation } from 'react-i18next';

import { formatLocalizedDate } from '@/shared/utils/format-date.utils';
import type { TripActivity } from '@/infrastructure/api/trip-activities/types';

interface ActivitySectionProps {
    activities: TripActivity[];
}

export function ActivitySection({ activities }: ActivitySectionProps) {
    const { t } = useTranslation();

    // Helper functions to format data
    const formatDate = (dateValue: string | Date | null | undefined): string => {
        if (!dateValue) return 'N/A';
        try {
            const dateStr = dateValue instanceof Date ? dateValue.toISOString() : dateValue;
            return formatLocalizedDate(dateStr);
        } catch {
            return 'N/A';
        }
    };

    // Empty state handling if no activities are present
    if (!activities || activities.length === 0) {
        return (
            <div className="w-full max-w-4xl mx-auto bg-white p-4 print:p-2 print:max-w-none print:mx-0">
                <div className="mb-6 print:mb-4">
                    <h2 className="text-xl font-bold text-center mb-4 text-gray-800 print:text-lg print:mb-2">
                        {t('tripActivities.title')}
                    </h2>
                    <p className="text-center text-gray-500 py-4 print:text-gray-700">{t('common.noActivities')}</p>
                </div>
            </div>
        );
    }

    return (
        <div className="w-full max-w-4xl mx-auto bg-white p-4 print:p-2 print:max-w-none print:mx-0">
            <div className="mb-6 print:mb-4">
                <h2 className="text-xl font-bold text-center mb-4 text-gray-800 print:text-lg print:mb-2">
                    {t('tripActivities.title')}
                </h2>

                <div className="border border-gray-200 rounded overflow-hidden print:border-gray-400 print:rounded-none print:break-inside-avoid">
                    <table className="w-full border-collapse">
                        {/* Table Header */}
                        <thead className="bg-gray-50 print:bg-gray-100">
                            <tr>
                                <th className="px-3 py-2 text-center font-medium text-gray-700 border-b border-r border-gray-200 print:px-2 print:py-1 print:text-black print:border-gray-400">
                                    {t('tripActivities.employee')}
                                </th>
                                <th className="px-3 py-2 text-center font-medium text-gray-700 border-b border-r border-gray-200 print:px-2 print:py-1 print:text-black print:border-gray-400">
                                    {t('tripActivities.date')}
                                </th>
                                <th className="px-3 py-2 text-center font-medium text-gray-700 border-b border-r border-gray-200 print:px-2 print:py-1 print:text-black print:border-gray-400">
                                    {t('tripActivities.state')}
                                </th>
                                <th className="px-3 py-2 text-center font-medium text-gray-700 border-b border-r border-gray-200 print:px-2 print:py-1 print:text-black print:border-gray-400">
                                    {t('tripActivities.action')}
                                </th>
                                <th className="px-3 py-2 text-center font-medium text-gray-700 border-b border-r border-gray-200 print:px-2 print:py-1 print:text-black print:border-gray-400">
                                    {t('tripActivities.details')}
                                </th>
                                <th className="px-3 py-2 text-center font-medium text-gray-700 border-b border-r border-gray-200 print:px-2 print:py-1 print:text-black print:border-gray-400">
                                    {t('tripActivities.location')}
                                </th>
                            </tr>
                        </thead>
                        {/* Table Body */}
                        <tbody>
                            {activities.map((activity) => (
                                <tr key={activity.id} className="border-b border-gray-200 print:border-gray-400">
                                    {/* Employee */}
                                    <td className="px-3 py-2 text-center text-gray-600 border-r border-gray-200 print:px-2 print:py-1 print:text-black print:border-gray-400">
                                        {activity.origin}
                                    </td>
                                    {/* Date */}
                                    <td className="px-3 py-2 text-center text-gray-600 border-r border-gray-200 print:px-2 print:py-1 print:text-black print:border-gray-400">
                                        {formatDate(activity.createdAt)}
                                    </td>
                                    {/* State */}
                                    <td className="px-3 py-2 text-center text-gray-600 border-r border-gray-200 print:px-2 print:py-1 print:text-black print:border-gray-400">
                                        {activity.status?.name.english}
                                    </td>
                                    {/* Action */}
                                    <td className="px-3 py-2 text-center text-gray-600 border-r border-gray-200 print:px-2 print:py-1 print:text-black print:border-gray-400">
                                        {activity.action?.name.english}
                                    </td>
                                    {/* Details */}
                                    <td className="px-3 py-2 text-center text-gray-600 border-r border-gray-200 print:px-2 print:py-1 print:text-black print:border-gray-400">
                                        {activity.details}
                                    </td>
                                    {/* Location */}
                                    <td className="px-3 py-2 text-center text-gray-600 print:px-2 print:py-1 print:text-black">
                                        {activity.location?.latitude}, {activity.location?.longitude}
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    );
}
