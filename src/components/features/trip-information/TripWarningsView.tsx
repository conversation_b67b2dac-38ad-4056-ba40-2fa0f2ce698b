import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { useTranslation } from 'react-i18next';
import { useEffect, useState, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import type { VirtualScrollerLazyEvent } from 'primereact/virtualscroller';

import { Scope } from '@/shared/enums';
import Tooltip from '@/components/common/ui/Tooltip';
import { formatLocalizedDate } from '@/shared/utils/format-date.utils';
import { useLocalized } from '@/shared/hooks/use-localized.hook';
import i18n from '@/shared/config/i18n.config';
import { ExportExcelButton } from '@/components/common/ui/ExportExcelButton';
import { Button } from '@/components/common/ui/Button';
import { useTripAlertsStore } from '@/stores/trip-alerts.store';
import { constructTripStates } from '@/shared/utils/trips.utils';
import { Icon } from '@/components/common/ui/Icon';
import { logger } from '@/infrastructure/logging';
import { LoaderButton } from '@/components/common/ui/LoaderButton';
import EmailDialog from '@/components/common/ui/EmailDialog';
import { useTripDetailStore } from '@/stores/trip-detail.store';
import { tripWithAlertsExcelMapper } from '@/mappers/tripWithAlertsExcel.mapper';
import { TripEmailTemplate } from '@/components/common/email-templates/TripTemplate';
import { mapTripToTripEmail } from '@/mappers/emails/tripEmail.mapper';
import PrintButton from '@/components/common/printing-templates/PrintButton';
import AlertReportPrintable from '@/components/common/printing-templates/AlertReportPrintable';
import { ExportPdfButton } from '@/components/common/ui/ExportPdfButton';

export default function TripAlertsReportView() {
    const { t } = useTranslation();
    const { localized, dir } = useLocalized();
    const params = useParams();
    const navigate = useNavigate();

    // Get tripId from route parameters (e.g., /trips/:Id/details)
    const tripId = params.id;

    // Use the store
    const tripAlerts = useTripAlertsStore((state) => state.tripAlerts);
    const pagination = useTripAlertsStore((s) => s.pagination);
    const isLoading = useTripAlertsStore((s) => s.isLoading);
    const loadTripAlerts = useTripAlertsStore((s) => s.loadTripAlerts);
    const loadMoreTripAlerts = useTripAlertsStore((s) => s.loadMoreTripAlerts);
    const acknowledgeTripAlert = useTripAlertsStore((s) => s.acknowledgeTripAlert);
    const ackLoadingIds = useTripAlertsStore((s) => s.ackLoadingIds);
    const acknowledgeAllTripAlerts = useTripAlertsStore((s) => s.acknowledgeAllTripAlerts);
    const [isAnyAckLoading, setIsAnyAckLoading] = useState(ackLoadingIds.length > 0);
    const tripDetail = useTripDetailStore((s) => s.tripDetail);

    // Create ref to avoid function dependency issues
    const loadTripAlertsRef = useRef(loadTripAlerts);
    loadTripAlertsRef.current = loadTripAlerts;

    // Load trip stops when component mounts or tripId changes
    useEffect(() => {
        if (tripId) {
            loadTripAlertsRef.current({ tripId, PageNumber: 1, PageSize: 1000 });
        }
    }, [tripId]);
    const PAGE_SIZE = 5;

    const handleAcknowledge = async (alertId: string | number) => {
        if (!tripId) return;
        const alertKey = String(alertId);
        try {
            await acknowledgeTripAlert(tripId, alertKey);
            logger.info('[TripAlertsReportView] Acknowledge successful');
        } catch (error) {
            logger.error('[TripAlertsReportView] Acknowledge failed: ', error as Error);
        }
    };

    // Acknowledge All button handler
    const handleAcknowledgeAll = async () => {
        if (!tripId) return;
        setIsAnyAckLoading(true);
        try {
            await acknowledgeAllTripAlerts(tripId);
            logger.info('[TripAlertsReportView] Acknowledge successful');
            // show toast success if you have one
        } catch (error) {
            logger.error('[TripAlertsReportView] Acknowledge failed: ', error as Error);
            // show toast error
        } finally {
            setIsAnyAckLoading(false);
        }
    };

    // Button disabled when all alerts already acknowledged or when ackLoadingIds not empty
    const allAcknowledged = tripAlerts.every((a) => !!a.acknowledgedAt);

    const excelTables = tripWithAlertsExcelMapper(tripDetail, tripAlerts, t, localized);
    return (
        <div className="w-full">
            {/* Header */}
            <div className="flex items-center justify-between flex-wrap px-4 gap-1 py-3" dir={dir}>
                <h1 className="text-xl font-bold py-0">{t('common.tripWarnings')}</h1>
                <div className="flex flex-wrap gap-3">
                    {/* <PrintTableButton
                        tableId="tripAlertsTable"
                        title="Trip Alerts Report"
                        tripId={tripId}
                        rowsPerPage={10}
                    /> */}
                    <ExportExcelButton
                        fileName={`trip_${tripId}_Alerts_fullReport`}
                        sheetName="Trip Alerts Report"
                        tables={excelTables}
                        horizontal={true}
                        rtl={dir === 'rtl'}
                    />
                    <PrintButton
                        contentRenderer={AlertReportPrintable} // Dynamic component for alerts
                        contentProps={{ details: tripDetail, alerts: tripAlerts }} // Pass the alerts data as props
                        dir={dir}
                        label="Print Trip Alerts"
                        className="p-button-outlined bg-white border !border-green-600 rounded-sm px-4 py-2 text-green-700"
                    />
                    <ExportPdfButton
                        tripDetail={tripDetail}
                        tripAlerts={tripAlerts}
                        fileName={`trip-${tripId}-alerts-PDF`}
                    />
                    {allAcknowledged && (
                        <Button
                            variant="secondary"
                            disabled={true}
                            className={`p-button-outlined bg-white border !border-green-600 rounded-sm  px-4 py-2 text-green-700`}>
                            {t('tripDetails.allAlertsAcknowledged')}
                        </Button>
                    )}
                    {!allAcknowledged && (
                        <LoaderButton
                            loading={isAnyAckLoading}
                            isLoadingText={t('tripDetails.AcknowledgingAllAlerts')}
                            defaultText={t('tripDetails.acknowledgeAllAlerts')}
                            icon={<Icon name="ackAlert" className="h-6 w-6" />}
                            variant="outline"
                            size="sm"
                            onClick={() => handleAcknowledgeAll()}
                            className={`p-button-outlined bg-white border !border-green-600 rounded-sm  px-4 py-2 text-green-700`}
                        />
                    )}
                </div>
            </div>

            {/* DataTable */}
            <div className="_table_container flex-grow-1">
                <DataTable
                    id="tripAlertsTable"
                    key={i18n.language} // Force re-render when language changes
                    dataKey="id" // <-- IMPORTANT: unique field name for each row
                    value={tripAlerts}
                    dir={dir}
                    scrollable
                    scrollHeight="81vh"
                    loading={isLoading}
                    tableStyle={{ minWidth: '50rem' }}
                    className="shadow-md border border-gray-200"
                    lazy
                    totalRecords={pagination.totalCount}
                    virtualScrollerOptions={{
                        itemSize: 80,
                        showLoader: true,
                        loading: isLoading,
                        onLazyLoad: (event: VirtualScrollerLazyEvent) => {
                            const nextPage = Math.floor((event.first as number) / PAGE_SIZE) + 1;
                            loadMoreTripAlerts({ tripId, PageNumber: nextPage, PageSize: PAGE_SIZE });
                        },
                    }}
                    emptyMessage={isLoading ? t('loading') : t('common.noDataExist')}>
                    <Column
                        body={(rowData) => (
                            <p className="flex items-center  justify-center gap-2  whitespace-nowrap">
                                {localized(rowData.alertType.name)}
                            </p>
                        )}
                        header={t('common.alertType')}
                        bodyClassName="text-center"
                        headerClassName="bg-gray-100 font-semibold text-sm"
                    />
                    <Column
                        body={(rowData) => (
                            <>
                                <p className="flex items-center  justify-center gap-2  whitespace-nowrap">
                                    {formatLocalizedDate(rowData.fromState.trackerDateTime)}
                                </p>
                                <p className="flex items-center  justify-center gap-2  whitespace-nowrap">
                                    {rowData.toState ? formatLocalizedDate(rowData.toState.trackerDateTime) : '-'}
                                </p>
                            </>
                        )}
                        header={t('alert.timeStamp')}
                        bodyClassName="text-center"
                        headerClassName="bg-gray-100 font-semibold text-sm"
                    />
                    <Column
                        body={(rowData) => (
                            <div
                                onClick={() => {
                                    navigate(`/trips/${tripId}/details?alertId=${rowData.id}&isOpen=true&view=map`, {
                                        replace: true,
                                    });
                                }}
                                title={t('tripActivities.clickToViewOnMap')}>
                                <p className="flex items-center justify-center gap-2 whitespace-nowrap cursor-pointer text-blue-600 hover:text-blue-800 hover:underline">
                                    ({rowData.fromState.lat} , {rowData.fromState.long})
                                </p>
                                <p className="flex items-center justify-center gap-2 whitespace-nowrap cursor-pointer text-blue-600 hover:text-blue-800 hover:underline">
                                    {rowData.toState ? `(${rowData.toState.lat} , ${rowData.toState.long})` : '-'}
                                </p>
                            </div>
                        )}
                        header={t('common.location')}
                        bodyClassName="text-center"
                        headerClassName="bg-gray-100 font-semibold text-sm"
                    />
                    <Column
                        body={(rowData) => (
                            <>
                                <p className="flex items-center  justify-center gap-2  whitespace-nowrap">
                                    {rowData.fromState.currentSpeed}
                                </p>
                                <p className="flex items-center  justify-center gap-2  whitespace-nowrap">
                                    {rowData.toState ? `${rowData.toState.currentSpeed}` : '-'}
                                </p>
                            </>
                        )}
                        header={t('common.speed')}
                        bodyClassName="text-center"
                        headerClassName="bg-gray-100 font-semibold text-sm"
                    />
                    <Column
                        body={(rowData) => (
                            <>
                                <p className="flex items-center  justify-center gap-2  whitespace-nowrap">
                                    {localized(rowData.fromState.address)}
                                </p>
                                <p className="flex items-center  justify-center gap-2  whitespace-nowrap">
                                    {rowData.toState ? `${localized(rowData.toState.address)}` : '-'}
                                </p>
                            </>
                        )}
                        header={t('common.address')}
                        bodyClassName="text-center"
                        headerClassName="bg-gray-100 font-semibold text-sm"
                    />
                    <Column
                        body={(rowData) => (
                            <>
                                <div className="flex gap-2 items-center justify-center">
                                    {rowData.fromState
                                        ? constructTripStates(rowData.fromState).map(
                                              ({ id, textColor, icon, tooltipKey, tooltip }) => (
                                                  <Tooltip key={id} tooltipMessage={tooltip ? tooltip : t(tooltipKey)}>
                                                      <Icon
                                                          name={icon}
                                                          className={`${textColor} cursor-pointer size-5`}
                                                      />
                                                  </Tooltip>
                                              ),
                                          )
                                        : '-'}
                                </div>
                                <div className="flex gap-2 items-center justify-center">
                                    {rowData.toState
                                        ? constructTripStates(rowData.toState).map(
                                              ({ id, textColor, icon, tooltipKey }) => (
                                                  <Tooltip key={id} tooltipMessage={t(tooltipKey)}>
                                                      <Icon
                                                          name={icon}
                                                          className={`${textColor} cursor-pointer size-5`}
                                                      />
                                                  </Tooltip>
                                              ),
                                          )
                                        : '-'}
                                </div>
                            </>
                        )}
                        header={t('common.state')}
                        bodyClassName="text-center"
                        headerClassName="bg-gray-100 font-semibold text-sm"
                    />

                    {/* Procedures column (existing) */}
                    <Column
                        key={`procedures-col-${ackLoadingIds.join(',')}`}
                        body={(rowData) => {
                            const isAck = !!rowData.acknowledgedAt;
                            const isRowLoading = ackLoadingIds.includes(String(rowData.id));
                            // console.log('rendering row', rowData.id, 'isRowLoading', isRowLoading);

                            return (
                                <div className="flex gap-1 items-center justify-center">
                                    <Tooltip
                                        tooltipMessage={
                                            isAck
                                                ? t('tripDetails.alertAcknowledged')
                                                : t('tripDetails.acknowledgeAlert')
                                        }>
                                        <LoaderButton
                                            key={`${rowData.id}-${isRowLoading ? 'loading' : 'idle'}`}
                                            loading={isRowLoading}
                                            isLoadingText=""
                                            defaultText=""
                                            disabled={isAck || isRowLoading}
                                            icon={<Icon name="ackAlert" className="h-6 w-6" />}
                                            variant="outline"
                                            onClick={() => handleAcknowledge(rowData.id)}
                                            className={`p-button-outlined bg-white border rounded-sm px-3 py-2 m-1
                                            ${isAck ? '!border-gray-300 text-gray-400' : '!border-green-600 text-green-700'}`}
                                        />
                                    </Tooltip>
                                    <Tooltip tooltipMessage={t('emailDialog.title')}>
                                        <EmailDialog
                                            scope={Scope.Alert}
                                            emailBody={
                                                tripDetail ? (
                                                    <TripEmailTemplate
                                                        trip={mapTripToTripEmail({
                                                            ...tripDetail,
                                                            activeAlerts: [rowData],
                                                        })}
                                                    />
                                                ) : (
                                                    ''
                                                )
                                            }>
                                            <Button
                                                variant="secondary"
                                                className={`p-button-outlined bg-white border !border-green-600 rounded-sm  px-2 py-2 text-green-700 m-1`}>
                                                <Icon name="email" className="h-6 w-6" />
                                            </Button>
                                        </EmailDialog>
                                    </Tooltip>
                                </div>
                            );
                        }}
                        header={t('common.procedures')}
                        bodyClassName="text-center"
                        headerClassName="bg-gray-100 font-semibold text-sm"
                    />
                </DataTable>
            </div>
        </div>
    );
}
