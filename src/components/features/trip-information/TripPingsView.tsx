import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { useTranslation } from 'react-i18next';
import { useEffect } from 'react';
import { useParams } from 'react-router-dom';

import { logger } from '@/infrastructure/logging';
import { useTripPingsStore } from '@/stores/trip-ping.store';
import PrintButton from '@/components/common/printing-templates/PrintButton';
import { useTripDetailStore } from '@/stores/trip-detail.store';
import { useLocalized } from '@/shared/hooks/use-localized.hook';

import PingsReportPrintable from './PingsReportPrintable';

export default function TripPingsReportView() {
    const { t, i18n } = useTranslation();
    const { localized } = useLocalized();
    const dir = i18n.dir();
    const params = useParams();
    const tripId = params.tripId || params.id;

    // Use the store
    const { tripPings, isLoading, loadTripPings } = useTripPingsStore();
    const { tripDetail } = useTripDetailStore();
    // Load trip pings when component mounts or tripId changes
    useEffect(() => {
        if (tripId) {
            logger.info(`TripPingsReportView: loading tripId=${tripId}`);
            loadTripPings({ tripId: parseInt(tripId), page: 1, pageSize: 30 });
        } else {
            logger.info(`TripPingsReportView: no tripId provided`);
        }
    }, [tripId, loadTripPings]);

    // Print handler
    // const handlePrint = () => {
    //     window.print();
    // };

    return (
        <div className="w-full">
            {/* Title Bar */}
            <div className="flex items-center justify-between flex-wrap px-4 gap-1 py-3" dir={dir}>
                {/* Title */}
                <h1 className="text-xl font-bold py-0 text-gray-800">{t('pings.title', 'Pings Report')}</h1>
                <PrintButton
                    contentRenderer={PingsReportPrintable}
                    contentProps={{ details: tripDetail, pings: tripPings }}
                    dir={dir}
                    label="Print Pings Report"
                    className="p-button-outlined bg-white border !border-green-600 rounded-sm px-4 py-2 text-green-700"
                />
            </div>

            {/* DataTable */}
            <div className="_table_container flex-grow-1">
                <DataTable
                    key={i18n.language}
                    value={tripPings}
                    dir={dir}
                    scrollable
                    scrollHeight="72vh"
                    loading={isLoading}
                    tableStyle={{ minWidth: '60rem' }}
                    className="shadow-md border border-gray-200 rounded-b-md text-sm"
                    emptyMessage={isLoading ? t('loading') : t('common.noDataExist')}>
                    <Column
                        header={t('common.address', 'Adress')}
                        body={(row) => localized(row.address)}
                        align="center"
                    />

                    <Column
                        header={t('pings.lat', 'Latitude')}
                        body={(row) => row.location.latitude.toFixed(4)}
                        align="center"
                    />
                    <Column
                        header={t('pings.long', 'Longitude')}
                        body={(row) => row.location.longitude.toFixed(4)}
                        align="center"
                    />
                    <Column
                        header={t('pings.time', 'Time')}
                        body={(row) =>
                            new Intl.DateTimeFormat(i18n.language, {
                                year: 'numeric',
                                month: '2-digit',
                                day: '2-digit',
                                hour: '2-digit',
                                minute: '2-digit',
                                second: '2-digit',
                                hour12: false,
                            }).format(new Date(row.trackerDateTime))
                        }
                        align="center"
                    />
                    <Column header={t('pings.speed', 'Speed')} body={(row) => `${row.speed} km/h`} align="center" />
                </DataTable>
            </div>
        </div>
    );
}
