// src/components/features/trip-information/TripAlertChart.tsx
import { useRef, useEffect, useState, useMemo } from 'react';
import { Timeline, type TimelineTimeAxisScaleType } from 'vis-timeline/standalone';
import { DataSet } from 'vis-data';
import { ZoomIn, ZoomOut, ChevronLeft, ChevronRight, Clock, RefreshCcw } from 'lucide-react';
import { t } from 'i18next';

import { Button } from '@/components/common/ui/Button';
import { Card } from '@/components/common/ui/Card';
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from '@/components/common/ui/DropdownMenu';
import { Radio } from '@/components/common/ui/Radio';
// Import SVG icons for different alert types,
import truckStopped from '@/assets/imgs/svg/truckStopped.svg';
import trackerTamper from '@/assets/imgs/svg/trackerTamper.svg';
import trackerDropped from '@/assets/imgs/svg/trackerDropped.svg';
import lockTamper from '@/assets/imgs/svg/lockTamper.svg';
import lockOpen from '@/assets/imgs/svg/lockOpen.svg';
import lockConnectionLost from '@/assets/imgs/svg/lockConnectionLost.svg';
import trackerBatteryLow from '@/assets/imgs/svg/trackerBatteryLow.svg';
import lockBatteryLow from '@/assets/imgs/svg/lockBatteryLow.svg';
import gsmSignalLost from '@/assets/imgs/svg/gsmSignalLost.svg';
import gpsSignalLost from '@/assets/imgs/svg/gpsSignalLost.svg';
import entringGeofence from '@/assets/imgs/svg/entringGeofence.svg';
import trackerConnectionLost from '@/assets/imgs/svg/trackerConnectionLost.svg';
import overSpeeding from '@/assets/imgs/svg/overSpeeding.svg';
import wrongDirection from '@/assets/imgs/svg/wrongDirection.svg';
import suspiciousArea from '@/assets/imgs/svg/suspiciousArea.svg';
import alert from '@/assets/imgs/svg/alert-2.svg';
import { useLocalized } from '@/shared/hooks/use-localized.hook';
import type { MapAlert } from '@/components/common/google-map/BaseMap';
import { Checkbox } from '@/components/common/ui/Checkbox';
import { Icon } from '@/components/common/ui/Icon';
import { formatLocalizedDate } from '@/shared/utils/format-date.utils';
import type { AlertTypeId } from '@/shared/utils/alerts.utils';
import { useTripAlertsStore } from '@/stores/trip-alerts.store';

// Type definition for an alert event
type AlertEvent = {
    id: string;
    type: number;
    start: Date;
    end: Date;
};

// Mapping each alert type to an icon and a label
export const ALERT_TYPES: Partial<Record<AlertTypeId | 'default', { key: string; icon: string }>> = {
    10015: { key: 'tripDetails.tripMap.chargerStatusChanged', icon: alert },
    10021: { key: 'tripDetails.tripMap.entringGeofence', icon: entringGeofence },
    10000: { key: 'tripDetails.tripMap.trackerTamper', icon: trackerTamper },
    10001: { key: 'tripDetails.tripMap.trackerDropped', icon: trackerDropped },
    10002: { key: 'tripDetails.tripMap.lockTamper', icon: lockTamper },
    10003: { key: 'tripDetails.tripMap.lockOpen', icon: lockOpen },
    10004: { key: 'tripDetails.tripMap.lockConnectionLost', icon: lockConnectionLost },
    10005: { key: 'tripDetails.tripMap.trackerBatteryLow', icon: trackerBatteryLow },
    10007: { key: 'tripDetails.tripMap.lockBatteryLow', icon: lockBatteryLow },
    10060: { key: 'tripDetails.tripMap.truckStopped', icon: truckStopped },
    10008: { key: 'tripDetails.tripMap.gsmSignalLost', icon: gsmSignalLost },
    10009: { key: 'tripDetails.tripMap.gpsSignalLost', icon: gpsSignalLost },
    10023: { key: 'tripDetails.tripMap.trackerConnectionLost', icon: trackerConnectionLost },
    10040: { key: 'tripDetails.tripMap.overSpeeding', icon: overSpeeding },
    10061: { key: 'tripDetails.tripMap.wrongDirection', icon: wrongDirection },
    10066: { key: 'tripDetails.tripMap.suspiciousArea', icon: suspiciousArea },

    // Default fallback for unknown alerts
    default: { key: 'tripDetails.tripMap.defaultAlert', icon: alert },
};

// Timeline interval options (in minutes)
const INTERVAL_OPTIONS = [
    { value: 30, labelKey: 'tripDetails.timeline.interval.30m' },
    { value: 60, labelKey: 'tripDetails.timeline.interval.1h' },
    { value: 180, labelKey: 'tripDetails.timeline.interval.3h' },
    { value: 360, labelKey: 'tripDetails.timeline.interval.6h' },
    { value: 720, labelKey: 'tripDetails.timeline.interval.12h' },
    { value: 1440, labelKey: 'tripDetails.timeline.interval.24h' },
];

type TripAlertChartProps = {
    tripAlerts: MapAlert[];
    portalContainer?: HTMLElement;
};

export const TripAlertChart = ({ tripAlerts, portalContainer }: TripAlertChartProps) => {
    // Localization hooks for multi-language support
    const { dir } = useLocalized();

    const containerRef = useRef<HTMLDivElement>(null);
    const timelineRef = useRef<Timeline | null>(null);
    const selectTripAlertById = useTripAlertsStore((state) => state.selectTripAlertById);

    // Map raw API data to AlertEvent type
    const alerts = useMemo(
        () =>
            tripAlerts.map((alert, idx) => ({
                id: alert.id?.toString() || String(idx + 1),
                type: Number(alert.alertType.id),
                from: alert.fromState.address,
                to: alert.toState?.address || alert.fromState.address,
                start: new Date(alert.fromState.trackerDateTime),
                end: alert.toState ? new Date(alert.toState.trackerDateTime) : new Date(),
            })),
        [tripAlerts],
    );

    const [interval, setInterval] = useState<number>(720);
    const [selectedTypes, setSelectedTypes] = useState<number[]>(alerts.map((a) => a.type));
    const [selectedAlert, setSelectedAlert] = useState<AlertEvent | null>(null);

    // Calculate min and max dates dynamically from alerts, memoized for stable references
    const minDate = useMemo(
        () => (alerts.length > 0 ? new Date(Math.min(...alerts.map((a) => a.start.getTime()))) : new Date()),
        [alerts],
    );
    const maxDate = useMemo(
        () => (alerts.length > 0 ? new Date(Math.max(...alerts.map((a) => a.end.getTime()))) : new Date()),
        [alerts],
    );

    // Store initial window in ref (use actual alerts range)
    const initialWindowRef = useRef({
        start: minDate,
        end: maxDate,
    });

    // Control the types dropdown open state so we can prevent it from closing on checkbox clicks
    const [typesMenuOpen, setTypesMenuOpen] = useState(false);

    useEffect(() => {
        if (!selectedAlert) return;
        selectTripAlertById(Number(selectedAlert.id), { mode: 'range', isOpen: true });
    }, [selectedAlert, selectTripAlertById]);

    useEffect(() => {
        if (!containerRef.current) return;

        const filtered = alerts.filter((a) => selectedTypes.includes(a.type));

        const items = new DataSet(
            filtered.map((a) => ({
                id: a.id,
                content: `<img src="${ALERT_TYPES[a.type]?.icon || ALERT_TYPES.default?.icon}" style:"height:5px;" />`,
                start: a.start,
                end: a.end,
                group: a.type,
                style: 'border-radius: 10px;padding: 0px;background-color:  rgba(253, 243, 243, 1); border-color: rgba(253, 243, 243, 1); color:rgba(255, 255, 255, 1); height:30px; align-items: center; justify-content: center; display: flex;',
            })),
        );

        const groups = new DataSet(
            Array.from(new Set(filtered.map((a) => a.type))).map((type) => ({
                id: type,
                content: `<img src="${ALERT_TYPES[type]?.icon || ALERT_TYPES.default?.icon}" />`,
                className: ' h-[25px]flex items-center justify-center',
                title: `${t(ALERT_TYPES[type]?.key || '') || t(ALERT_TYPES['default']?.key || '')}`,
            })),
        );

        if (!timelineRef.current) {
            timelineRef.current = new Timeline(containerRef.current, items, groups, {
                stack: true, // disable stacking ( otherwise the items will overlap )
                horizontalScroll: true,
                verticalScroll: true,
                zoomKey: 'ctrlKey',
                orientation: { axis: 'bottom' },
                selectable: true,
                multiselect: false,
                min: minDate, // dynamic min
                max: maxDate, // dynamic max
                zoomMin: 1000 * 60 * 60,
                zoomMax: 1000 * 60 * 60 * 24,
                tooltip: {
                    followMouse: false, // tooltip follows the cursor
                    overflowMethod: 'none', // allow to show tooltips outside the visible area', // keeps tooltip inside the timeline
                    delay: 0, // show immediately
                },
            });

            // Use the stored initial window (ensures resetZoom uses the exact same values)
            timelineRef.current.setWindow(initialWindowRef.current.start, initialWindowRef.current.end);

            timelineRef.current.on('select', (props) => {
                if (props.items.length > 0) {
                    const itemId = props.items[0];
                    const found = filtered.find((a) => a.id === itemId);
                    if (found) {
                        setSelectedAlert(found);
                    }
                }
            });
        } else {
            timelineRef.current.setItems(items);
            timelineRef.current.setGroups(groups);
        }

        // Define strongly typed options for time axis scale and step
        const TIME_AXIS_OPTIONS: Record<number, { scale: TimelineTimeAxisScaleType; step: number }> = {
            30: { scale: 'minute', step: 30 },
            60: { scale: 'hour', step: 1 },
            180: { scale: 'hour', step: 3 },
            360: { scale: 'hour', step: 6 },
            720: { scale: 'hour', step: 12 },
            1440: { scale: 'day', step: 1 },
        };
        if (!timelineRef.current) return;
        const axis = TIME_AXIS_OPTIONS[interval] || { scale: 'hour', step: 1 };
        timelineRef.current.setOptions({
            timeAxis: axis,
        });
        timelineRef.current.redraw();
    }, [selectedTypes, interval, alerts, minDate, maxDate]);

    // Auto adjust zoom when interval changes
    useEffect(() => {
        const INTERVAL_WINDOWS: Record<number, number> = {
            30: 1000 * 60 * 60 * 2,
            60: 1000 * 60 * 60 * 4,
            180: 1000 * 60 * 60 * 12,
            360: 1000 * 60 * 60 * 24,
            720: 1000 * 60 * 60 * 48,
            1440: 1000 * 60 * 60 * 72,
        };
        if (!timelineRef.current) return;

        const range = timelineRef.current.getWindow();
        const center = new Date((range.start.valueOf() + range.end.valueOf()) / 2);
        const windowSize = INTERVAL_WINDOWS[interval] || 1000 * 60 * 60 * 6;
        const start = new Date(center.getTime() - windowSize / 2);
        const end = new Date(center.getTime() + windowSize / 2);

        timelineRef.current.setWindow(start, end);
    }, [interval]);

    const zoom = (percent: number) => {
        if (!timelineRef.current) return;
        const range = timelineRef.current.getWindow();
        const interval = range.end.valueOf() - range.start.valueOf();
        timelineRef.current.setWindow(
            range.start.valueOf() - (interval * percent) / 2,
            range.end.valueOf() + (interval * percent) / 2,
        );
    };

    const move = (percent: number) => {
        if (!timelineRef.current) return;
        const range = timelineRef.current.getWindow();
        const interval = range.end.valueOf() - range.start.valueOf();
        timelineRef.current.setWindow(
            range.start.valueOf() + interval * percent,
            range.end.valueOf() + interval * percent,
        );
    };

    const resetZoom = () => {
        if (!timelineRef.current) return;

        // Use the same reference as used on creation
        timelineRef.current.setWindow(initialWindowRef.current.start, initialWindowRef.current.end);

        // Reset interval to default 3 hours
        setInterval((prev) => (prev === 180 ? prev : 180));
    };

    // Helpers for the types filter
    const allTypes = useMemo(() => Array.from(new Set(alerts.map((a) => a.type))), [alerts]);
    const toggleAll = () => {
        if (selectedTypes.length === allTypes.length) {
            setSelectedTypes([]);
        } else {
            setSelectedTypes(allTypes);
        }
    };

    const updateSelectedAlertTypes = (id: number) => {
        setSelectedTypes((prev) => (prev.includes(id) ? prev.filter((sid) => sid !== id) : [...prev, id]));
    };

    const [hoveredAlert, setHoveredAlert] = useState<AlertEvent | null>(null);
    const [tooltipPos, setTooltipPos] = useState<{ x: number; y: number }>({ x: 0, y: 0 });
    interface TimelineSelectEvent {
        event: MouseEvent;
        item?: string | number;
        items: (string | number)[];
    }
    useEffect(() => {
        if (!timelineRef.current) return;

        const handleOver = (props: TimelineSelectEvent) => {
            const item = alerts.find((a) => a.id === props.item);
            if (item) {
                setHoveredAlert(item);
                setTooltipPos({ x: props.event.clientX, y: props.event.clientY });
            }
        };

        const handleOut = () => {
            setHoveredAlert(null);
        };

        const timeline = timelineRef.current;
        timeline.on('itemover', handleOver);
        timeline.on('itemout', handleOut);

        return () => {
            timeline.off('itemover', handleOver);
            timeline.off('itemout', handleOut);
        };
    }, [alerts]);

    return (
        <Card className="w-[600px] p-3 shadow-lg rounded-2xl gap-3 bg-white">
            {/* Controls */}
            <div className="flex items-start justify-between gap-3 flex-wrap">
                {/* Interval Menu */}
                <DropdownMenu dir={dir}>
                    <DropdownMenuTrigger asChild dir={dir}>
                        <Button variant="outline" size="sm" className="flex items-center gap-1">
                            <Clock className="w-4 h-4" />
                            {t('tripDetails.timeline.interval.title')}
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="max-h-64  p-2" portalContainer={portalContainer}>
                        {INTERVAL_OPTIONS.map((opt) => (
                            <Radio
                                key={opt.value}
                                label={t(opt.labelKey)}
                                checked={interval === opt.value}
                                onClick={() => setInterval(opt.value)}
                                className="flex items-center gap-2 cursor-pointer"
                            />
                        ))}
                    </DropdownMenuContent>
                </DropdownMenu>

                {/* Zoom / Move / Reset */}
                <div className="flex gap-1">
                    <Button variant="outline" size="sm" className="w-6 h-8" onClick={() => zoom(-0.4)}>
                        <ZoomIn className="w-2 h-2" />
                    </Button>
                    <Button variant="outline" size="sm" className="w-6 h-8" onClick={() => zoom(0.4)}>
                        <ZoomOut className="w-2 h-2" />
                    </Button>
                    <Button variant="outline" size="sm" onClick={resetZoom}>
                        <RefreshCcw className="w-2 h-2" />
                    </Button>
                    <Button variant="outline" size="sm" className="w-6 h-8" onClick={() => move(-0.2)}>
                        <ChevronLeft className="w-2 h-2" />
                    </Button>
                    <Button variant="outline" size="sm" className="w-6 h-8" onClick={() => move(0.2)}>
                        <ChevronRight className="w-2 h-2" />
                    </Button>
                </div>

                {/* Type Filters with Select All / Deselect All */}
                <DropdownMenu open={typesMenuOpen} onOpenChange={setTypesMenuOpen} dir={dir}>
                    <DropdownMenuTrigger asChild dir={dir}>
                        <Button variant="outline" size="sm" className="flex items-center gap-1">
                            <Icon name="filter" className="w-4 h-4" />
                            {t('common.filter')}
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="max-h-64 overflow-y-auto p-2" portalContainer={portalContainer}>
                        {/* Button to select/unselect all types */}
                        <Button variant="outline" size="sm" onClick={toggleAll} className="whitespace-nowrap">
                            {selectedTypes.length === allTypes.length ? t('filter.deSelectAll') : t('filter.selectAll')}
                        </Button>

                        <div className="border-t my-2" />

                        {allTypes.map((type) => (
                            <Checkbox
                                key={type}
                                label={t(ALERT_TYPES[type]?.key || '') || t(ALERT_TYPES['default']?.key || '')}
                                checked={selectedTypes.includes(type) ?? false}
                                onChange={() => updateSelectedAlertTypes(type)}
                            />
                        ))}
                    </DropdownMenuContent>
                </DropdownMenu>
            </div>

            {/* Timeline */}
            <div ref={containerRef} className="relative w-full h-full">
                {hoveredAlert && (
                    <div
                        style={{
                            position: 'fixed',
                            top: tooltipPos.y + 10,
                            left: tooltipPos.x + 10,
                            background: '#fff',
                            padding: 12,
                            borderRadius: 8,
                            boxShadow: '0 2px 10px rgba(0,0,0,0.2)',
                            zIndex: 9999,
                            minWidth: 220,
                            maxWidth: 320,
                        }}
                        dir={dir}>
                        <div style={{ display: 'flex', alignItems: 'center', gap: 8, marginBottom: 8 }}>
                            <img src={ALERT_TYPES[hoveredAlert.type]?.icon} style={{ width: 22, height: 22 }} />
                            <span style={{ fontWeight: 600 }}>{t(ALERT_TYPES[hoveredAlert.type]?.key || '')}</span>
                        </div>
                        <div style={{ color: '#555', fontSize: 12 }}>
                            <div>{/* <b>{t('common.from')}:</b> {hoveredAlert.from} */}</div>
                            <div>{/* <b>{t('common.to')}:</b> {hoveredAlert.to} */}</div>
                            <div>
                                <b>{t('common.start')}:</b> {formatLocalizedDate(hoveredAlert.start)}
                            </div>
                            <div>
                                <b>{t('common.end')}:</b> {formatLocalizedDate(hoveredAlert.end)}
                            </div>
                            <div>
                                <b>{t('common.duration')}:</b>{' '}
                                {Math.round((hoveredAlert.end.getTime() - hoveredAlert.start.getTime()) / 60000)}{' '}
                                {t('common.minutes')}
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </Card>
    );
};
