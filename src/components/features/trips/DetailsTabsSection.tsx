import { useTranslation } from 'react-i18next';
import { useRef, useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/common/ui/Tabs';
import type { IconName } from '@/components/common/ui/Icon';
import { Icon } from '@/components/common/ui/Icon';
import { useLocalized } from '@/shared/hooks/use-localized.hook';

import TripActivitiesReportView from '../trip-information/TripActivitiesReportView';
import TripEventsReportView from '../trip-information/TripEventsReportView';
import TripMovementReportView from '../trip-information/TripStopsReportView';
import TripPingsView from '../trip-information/TripPingsView';
import TripWarningsView from '../trip-information/TripWarningsView';
import TripMap from '../trip-information/TripMap';

type TripDetailTabs =
    | 'TripMap'
    | 'TripActivitiesReport'
    | 'TripEventsReport'
    | 'TripPings'
    | 'TripWarnings'
    | 'MovementReport';
export default function DetailsTabsSection() {
    const { t } = useTranslation();
    const { dir } = useLocalized();
    const mapRef = useRef<HTMLDivElement>(null);
    const [queryParams] = useSearchParams();
    const [activeTab, setActiveTab] = useState<string>('TripMap');

    interface TabConfig {
        value: TripDetailTabs;
        icon: IconName;
        label: string;
        component: JSX.Element;
    }

    const tabsConfig: TabConfig[] = [
        {
            value: 'TripMap',
            icon: 'mapLocation',
            label: t('common.tripMap'),
            component: (
                <div ref={mapRef} id="printable-map" className="w-full h-full">
                    <TripMap />
                </div>
            ),
        },
        { value: 'TripWarnings', icon: 'alert', label: t('common.warnings'), component: <TripWarningsView /> },
        { value: 'TripPings', icon: 'tripPings', label: t('common.pings'), component: <TripPingsView /> },
        {
            value: 'MovementReport',
            icon: 'movementReport',
            label: t('common.movementReport'),
            component: <TripMovementReportView />,
        },
        {
            value: 'TripActivitiesReport',
            icon: 'activitiesReport',
            label: t('common.activitiesReport'),
            component: <TripActivitiesReportView />,
        },
        {
            value: 'TripEventsReport',
            icon: 'filter',
            label: t('common.eventsReport'),
            component: <TripEventsReportView />,
        },
    ];

    useEffect(() => {
        const alertId = queryParams.get('alertId');
        const activityId = queryParams.get('activityId');
        const view = queryParams.get('view');

        if ((alertId || activityId) && view?.toLowerCase() === 'map') {
            setActiveTab('TripMap');
        }
    }, [queryParams]);

    return (
        <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="relative w-full h-full bg-white rounded-lg shadow-sm overflow-hidden">
            <div className="w-full h-full flex flex-col">
                <TabsList className="w-full rtl:flex-row-reverse">
                    {tabsConfig.map((tab) => (
                        <TabsTrigger
                            key={tab.value}
                            value={tab.value}
                            dir={dir}
                            className="
                                text-base
                                font-medium
                                transition-all
                                duration-200
                                ease-in-out
                                hover:bg-primary-50/50
                                data-[state=active]:bg-primary-50/30
                                data-[state=active]:text-primary-700
                                data-[state=active]:border-primary-700
                                data-[state=active]:font-semibold
                                text-neutral-600
                                hover:text-neutral-800
                            ">
                            <Icon name={tab.icon} className="size-5" />
                            <span className="ml-2">{tab.label}</span>
                        </TabsTrigger>
                    ))}
                </TabsList>

                {tabsConfig.map((tab) => (
                    <TabsContent key={tab.value} value={tab.value} className="flex-1 w-full h-full p-0 m-0 bg-white">
                        {tab.component}
                    </TabsContent>
                ))}
            </div>
        </Tabs>
    );
}
