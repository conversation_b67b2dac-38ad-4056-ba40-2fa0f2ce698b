// src/components/features/trips/TripsDetails.tsx
import { useNavigate, useParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useEffect, useRef } from 'react';

import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/common/ui/Dialog';
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from '@/components/common/ui/Resizable';
import { useTripPingsStore } from '@/stores/trip-ping.store';
import { useTripAlertsStore } from '@/stores/trip-alerts.store';
import { useTripDetailStore } from '@/stores/trip-detail.store';
import { useTripActivitiesStore } from '@/stores/trip-activities.store';
import { useTripStopsStore } from '@/stores/trip-stops.store';
import { useTripLogsStore } from '@/stores/trip-logs.store';
import { useTripReplayStore } from '@/stores/trip-route-replay.store';

import DetailsInfoSection from './DetailsInfoSection';
import DetailsTabsSection from './DetailsTabsSection';

export default function TripsDetails() {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const params = useParams();

    const listenToLocationUpdates = useTripPingsStore((s) => s.listenToLocationUpdates);
    const listenToAlerts = useTripAlertsStore((s) => s.listenToAlerts);
    const unListenToLocationUpdates = useTripPingsStore((s) => s.unListenToLocationUpdates);
    const unListenToAlerts = useTripAlertsStore((s) => s.unListenToAlerts);

    const tripId = params.id;

    // Create refs to avoid function dependency issues
    const listenToLocationUpdatesRef = useRef(listenToLocationUpdates);
    const listenToAlertsRef = useRef(listenToAlerts);
    const unListenToLocationUpdatesRef = useRef(unListenToLocationUpdates);
    const unListenToAlertsRef = useRef(unListenToAlerts);

    // Update refs when functions change
    listenToLocationUpdatesRef.current = listenToLocationUpdates;
    listenToAlertsRef.current = listenToAlerts;
    unListenToLocationUpdatesRef.current = unListenToLocationUpdates;
    unListenToAlertsRef.current = unListenToAlerts;

    useEffect(() => {
        if (tripId) {
            listenToLocationUpdatesRef.current(Number(tripId));
            listenToAlertsRef.current(Number(tripId));
        }

        return () => {
            // Cleanup SignalR connections
            unListenToLocationUpdatesRef.current();
            unListenToAlertsRef.current();

            // Reset all entity stores to prevent state interference
            useTripDetailStore.getState().reset();
            useTripAlertsStore.getState().reset();
            useTripActivitiesStore.getState().reset();
            useTripPingsStore.getState().reset();
            useTripStopsStore.getState().reset();
            useTripLogsStore.getState().reset();
            useTripReplayStore.getState().reset();
        };
    }, [tripId]);

    const close = () => navigate(-1);

    return (
        // dialog is always open because route matched. onOpenChange closes via navigate.
        <Dialog open onOpenChange={(open) => !open && close()}>
            {/* No DialogTrigger here — this component is the dialog content */}
            <DialogContent className="grid-rows-[40px_1fr] w-screen h-screen max-w-none max-h-none p-0 [&>button>svg]:h-8 [&>button>svg]:w-6">
                <DialogHeader className="px-3 py-3 max-h-[100px] flex items-start">
                    <DialogTitle>
                        <span className="text-2xl text-[20px] text-green-700 flex items-center gap-1">
                            {t('common.tripDetails')}
                        </span>
                    </DialogTitle>
                </DialogHeader>

                <div className="flex flex-grow-1  h-full">
                    <ResizablePanelGroup direction="horizontal" className="h-full">
                        <ResizablePanel defaultSize={25}>
                            <DetailsInfoSection tripId={Number(tripId)} />
                        </ResizablePanel>

                        <ResizableHandle withHandle />

                        <ResizablePanel>
                            <DetailsTabsSection />
                        </ResizablePanel>
                    </ResizablePanelGroup>
                </div>
            </DialogContent>
        </Dialog>
    );
}
