// src/components/features/trips/TripsDetailsLauncher.tsx
import { LucideFullscreen } from 'lucide-react';
import { useNavigate, useLocation } from 'react-router-dom';

import Tooltip from '@/components/common/ui/Tooltip';

type Props = {
    tripId: string | number;
};

export default function TripsDetailsLauncher({ tripId }: Props) {
    const navigate = useNavigate();
    const location = useLocation();

    const open = () => {
        // pass current location as background so the modal opens on top of it
        navigate(`/trips/${tripId}/details`, { state: { background: location } });
    };

    return (
        <Tooltip tooltipMessage="common.viewDetails">
            <span className="cursor-pointer inline-block" onClick={open}>
                <LucideFullscreen className="size-5 text-[#179FCA] min-w-fit m-auto" />
            </span>
        </Tooltip>
    );
}
