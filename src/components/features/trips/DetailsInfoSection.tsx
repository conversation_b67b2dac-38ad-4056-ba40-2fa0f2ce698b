import { useEffect, useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import { useTripDetailStore } from '@/stores/trip-detail.store';
import { constructTripStates } from '@/shared/utils/trips.utils';
import { useLocalized } from '@/shared/hooks/use-localized.hook';
import { formatLocalizedDate } from '@/shared/utils/format-date.utils';
import { TripStatesSection } from '@/components/features/trips/trip-details/TripStatesSection';
import { TripAlertsSection } from '@/components/features/trips/trip-details/TripAlertsSection';
import { TripFromToSection } from '@/components/features/trips/trip-details/TripFromToSection';
import { Button } from '@/components/common/ui/Button';
import InfoRow from '@/components/common/ui/InfoRow';
import { Icon } from '@/components/common/ui/Icon';
import { Tag } from '@/components/common/ui/Tag';
import { logger } from '@/infrastructure/logging';
import { Input } from '@/components/common/ui/Input';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/common/ui/Dialog';
import { printMap } from '@/shared/utils/map-printer.utils';
import { showError, showSuccess } from '@/components/common/ui/Toast';
import type { TripDetail } from '@/infrastructure/api/trips/types';

import { DetailsInfoSectionFooter } from './DetailsInfoSectionFooter';

type TripDetailsInfoWindowProps = {
    tripId: number;
};

export default function DetailsInfoSection({ tripId }: TripDetailsInfoWindowProps) {
    const { t } = useTranslation();
    const localized = useLocalized();
    const navigate = useNavigate();
    const [reasonDialogVisible, setReasonDialogVisible] = useState(false);
    const [reason, setReason] = useState('');
    const [error, setError] = useState('');

    const { tripDetail, isLoading, loadTripDetail } = useTripDetailStore();
    const { endTrip, markAsFocused, markAsSuspicious, updateSecurityNotes } = useTripDetailStore();
    const TripStates = tripDetail ? constructTripStates(tripDetail.currentState) : [];

    // Create ref to avoid function dependency issues
    const loadTripDetailRef = useRef(loadTripDetail);
    loadTripDetailRef.current = loadTripDetail;

    const [notesDialogVisible, setNotesDialogVisible] = useState(false);
    const [securityNotes, setSecurityNotes] = useState(tripDetail?.securityNotes ?? '');
    const [savingNotes, setSavingNotes] = useState(false);

    useEffect(() => {
        if (tripId) loadTripDetailRef.current({ id: tripId });
    }, [tripId]);

    if (!tripId) return null;

    const title = `${tripDetail?.transitNumber ?? 'N/A'}/\u200E#${tripDetail?.id}`;
    const subtitle = formatLocalizedDate(tripDetail?.startDate);

    const handlePrint = (tripDetail: TripDetail | null) => {
        printMap('printable-map', tripDetail);
    };
    const handleOpenEndTripDialog = () => {
        setReason('');
        setError('');
        setReasonDialogVisible(true);
    };

    const handleConfirmEndTrip = async () => {
        if (!reason.trim()) {
            setError(t('tripDetails.endTripReasonRequired'));
            return;
        }

        try {
            await endTrip(tripId, reason);
            setReasonDialogVisible(false);
            showSuccess(t('common.success'), t('tripDetails.tripEndedSuccessfully'));
        } catch (error: unknown) {
            logger.error(
                '[DetailsInfoSection.handleConfirmEndTrip] Error occurred while ending trip. Error: ',
                error as Error,
            );
            setError(t('tripDetails.FailedToEndTrip'));
            showError(t('common.error'), t('tripDetails.failedToEndTrip'));
        }
    };

    const handleOpenNotesDialog = () => {
        setSecurityNotes(tripDetail?.securityNotes ?? '');
        setNotesDialogVisible(true);
    };

    const handleSaveNotes = async () => {
        try {
            setSavingNotes(true);
            logger.info('Saving security notes: ', { securityNotes });
            await updateSecurityNotes(tripId, securityNotes || null);
            setNotesDialogVisible(false);
            showSuccess(t('common.success'), t('tripDetails.securityNotesUpdated'));
        } catch (error: unknown) {
            logger.error('[DetailsInfoSection.handleSaveNotes] Failed: ', error as Error);
            showError(t('common.error'), t('tripDetails.securityNotesUpdateFailed'));
        } finally {
            setSavingNotes(false);
        }
    };

    // ================== (1) title ==================
    const titleSection = title && subtitle && (
        <div className="space-y-4 border-t pt-3">
            {/* Section: Trip */}
            <span className="text-2xl text-[20px] text-green-700 flex items-center gap-3">
                <Icon name="truck-2" className="size-8 bg-green-700 text-white rounded-full p-1" />
                <div className="space-y-1 justify-center items-center flex  gap-2">
                    {/* Title and Subtitle Container */}
                    <div className={`flex flex-col  flex-1 min-w-0`}>
                        <h2 className={`text-sm font-semibold leading-tight  truncate`}>{title}</h2>
                        {subtitle && <p className={`text-xs mt-0.5 leading-tight  truncate`}>{subtitle}</p>}
                    </div>
                </div>
            </span>
            <div className="flex flex-wrap gap-2">
                {/* Moving / Stopped */}
                {tripDetail?.currentState && (
                    <Tag
                        bgColor={tripDetail.currentState.currentSpeed > 0 ? 'bg-[#1B83541A]' : 'bg-[#179FCA1A]'}
                        textColor={tripDetail.currentState.currentSpeed > 0 ? 'text-[#1B8354]' : 'text-[#179FCA]'}
                        className="inline-flex max-w-max cursor-pointer focus:outline-none">
                        {tripDetail.currentState.currentSpeed > 0 ? t('tripDetails.moving') : t('tripDetails.stopped')}
                    </Tag>
                )}

                {/* Arrival Tracking / Trip is Focused */}
                {tripDetail?.isFocused && (
                    <Tag
                        bgColor={'bg-[#F978161A]'}
                        textColor={'text-[#F97816]'}
                        className="inline-flex max-w-max cursor-pointer focus:outline-none">
                        {t('tripDetails.arrivalTracking')}
                    </Tag>
                )}

                {/* Suspected Trip */}
                {tripDetail?.isSuspicious && (
                    <Tag
                        bgColor={'bg-[#FCE4E4]'}
                        textColor={'text-[#E4514F]'}
                        className="inline-flex max-w-max cursor-pointer focus:outline-none">
                        {t('tripDetails.suspectedTrip')}
                    </Tag>
                )}
            </div>
        </div>
    );

    // ================== (2) FROM�TO SECTION ==================
    const fromToSection = tripDetail && (
        <TripFromToSection
            fromLabel={tripDetail.entryPort?.name ? localized(tripDetail.entryPort.name) : 'N/A'}
            toLabel={tripDetail.exitPort?.name ? localized(tripDetail.exitPort.name) : 'N/A'}
            startDate={tripDetail.startDate}
            endDate={tripDetail.endDate}
        />
    );

    // ================== (3) DETAILS ==================
    const detailsSection = !isLoading && tripDetail && (
        <div className="space-y-4 border-t pt-3">
            {/* Section: Trip */}
            <div>
                <h3 className="font-semibold text-gray-800 mb-2">{t('tripDetails.aboutTrip')}</h3>
                <div className="space-y-1">
                    <InfoRow label={t('tripDetails.transitType')} value={tripDetail.transitTypeName ?? 'N/A'} />
                    <InfoRow
                        label={t('common.owner')}
                        value={
                            tripDetail.ownerType || tripDetail.ownerDesc
                                ? `${tripDetail.ownerType ?? ''} - ${tripDetail.ownerDesc ?? ''}`
                                : '-'
                        }
                    />
                    <InfoRow
                        label={t('filter.entryPort')}
                        value={tripDetail.entryPort?.name ? localized(tripDetail.entryPort.name) : 'N/A'}
                    />
                    <InfoRow
                        label={t('filter.exitPort')}
                        value={tripDetail.exitPort?.name ? localized(tripDetail.exitPort.name) : 'N/A'}
                    />
                </div>
            </div>

            {/* Section: Shipment */}
            <div className="border-t pt-3">
                <h3 className="font-semibold text-gray-800 mb-2">{t('tripDetails.aboutShipment')}</h3>
                <div className="space-y-1">
                    <InfoRow
                        label={t('tripDetails.trackingSerialNumber')}
                        value={tripDetail.tracker.serialNumber ?? 'N/A'}
                        isLink={true}
                        onClick={() => {
                            const trackerNumber = tripDetail.tracker.serialNumber;
                            if (trackerNumber) {
                                const params = new URLSearchParams();
                                params.set('trackerNumber', trackerNumber);
                                navigate(`/trips?${params.toString()}`);
                            }
                        }}
                    />
                    <InfoRow
                        label={t('tripDetails.elockSerialNumber')}
                        value={tripDetail.eLocks?.map((eLock) => eLock.serialNumber).join(', ') ?? 'N/A'}
                    />
                    <InfoRow
                        label={t('tripDetails.shipmentDescription')}
                        value={tripDetail.shipmentDescription ?? 'N/A'}
                    />
                </div>
            </div>

            {/* Section: Vehicle */}
            <div className="border-t pt-3">
                <h3 className="font-semibold text-gray-800 mb-2">{t('tripDetails.aboutVehicle')}</h3>
                <div className="space-y-1">
                    <InfoRow
                        label={t('tripDetails.aboutVehicle')}
                        value={[tripDetail.vehicle?.plateNo ?? '1234-XYZ', tripDetail.vehicle?.id ?? 'N/A'].join(' , ')}
                        isLink={true}
                        onClick={() => {
                            const plateNumber = tripDetail.vehicle?.plateNo;
                            if (plateNumber) {
                                const params = new URLSearchParams();
                                params.set('plateNumber', plateNumber);
                                navigate(`/trips?${params.toString()}`);
                            }
                        }}
                    />
                    <InfoRow
                        label={t('tripDetails.trackingSerialNumber')}
                        value={tripDetail.tracker.serialNumber ?? 'N/A'}
                        isLink={true}
                        onClick={() => {
                            const trackerNumber = tripDetail.tracker.serialNumber;
                            if (trackerNumber) {
                                const params = new URLSearchParams();
                                params.set('trackerNumber', trackerNumber);
                                navigate(`/trips?${params.toString()}`);
                            }
                        }}
                    />
                </div>
            </div>

            {/* Section: Driver */}
            <div className="border-t pt-3">
                <h3 className="font-semibold text-gray-800 mb-2">{t('tripDetails.aboutDriver')}</h3>
                <div className="space-y-1">
                    <InfoRow
                        label={t('tripDetails.driverName')}
                        value={tripDetail.driver?.name ?? 'N/A'}
                        isLink={true}
                        onClick={() => {
                            const driverName = tripDetail.driver?.name;
                            if (driverName) {
                                const params = new URLSearchParams();
                                params.set('driverName', driverName);
                                navigate(`/trips?${params.toString()}`);
                            }
                        }}
                    />
                    <InfoRow
                        label={t('tripDetails.driverNationality')}
                        value={tripDetail.driver?.passportCountry ?? ''}
                        isLink={true}
                        onClick={() => {
                            const driverNationality = tripDetail.driver?.passportCountry;
                            if (driverNationality) {
                                const params = new URLSearchParams();
                                params.set('driverNationality', driverNationality);
                                navigate(`/trips?${params.toString()}`);
                            }
                        }}
                    />
                    <InfoRow
                        label={t('tripDetails.driverContactNo')}
                        value={tripDetail.driver?.mobileNo ?? 'N/A'}
                        isLink={true}
                        onClick={() => {
                            const driverMobileNo = tripDetail.driver?.mobileNo;
                            if (driverMobileNo) {
                                const params = new URLSearchParams();
                                params.set('driverMobileNo', driverMobileNo);
                                navigate(`/trips?${params.toString()}`);
                            }
                        }}
                    />
                    <InfoRow
                        label={t('tripDetails.driverPassportNumber')}
                        value={tripDetail.driver?.passportNumber ?? ''}
                        isLink={true}
                        onClick={() => {
                            const driverPassportNumber = tripDetail.driver?.passportNumber;
                            if (driverPassportNumber) {
                                const params = new URLSearchParams();
                                params.set('driverPassportNumber', driverPassportNumber);
                                navigate(`/trips?${params.toString()}`);
                            }
                        }}
                    />
                </div>
            </div>

            {/* Section: Distances */}
            <div className="border-t pt-3">
                <h3 className="font-semibold text-gray-800 mb-2">{t('tripDetails.distances')}</h3>
                <div className="space-y-1">
                    <InfoRow
                        label={t('tripDetails.remainingDistance')}
                        value={tripDetail.currentState?.remainingDistanceInMeters ?? 'N/A'}
                    />
                    <InfoRow
                        label={t('tripDetails.completeDistance')}
                        value={tripDetail.currentState?.completeDistanceInMeters ?? ''}
                    />
                </div>
            </div>
        </div>
    );

    // ================== (4) STATES ==================
    const statesSection = !isLoading && tripDetail && <TripStatesSection states={TripStates} />;

    // ================== (5) ALERTS ==================
    const alertsTagSection = !isLoading && tripDetail && <TripAlertsSection alerts={tripDetail.activeAlerts} />;

    // ================== (6) Notes ==================
    const notesSection = !isLoading && tripDetail && (
        <div className="space-y-4 border-t pt-3">
            <div>
                <h3 className="font-semibold text-gray-800 mb-2">{t('tripDetails.securityNotes')}</h3>
                <InfoRow
                    label={tripDetail.securityNotes ?? t('tripDetails.noSecurityNotes')}
                    value={''}
                    className="w-full"
                />
                <div className="mt-3 justify-end items-center flex  gap-2">
                    <Button
                        onClick={handleOpenNotesDialog}
                        className="flex text-white rounded-md px-4 py-2 bg-green-700">
                        {t('tripDetails.editSecurityNotes')}
                    </Button>
                </div>
            </div>
        </div>
    );

    return (
        <div className="flex flex-col w-full h-full justify-between bg-white z-2 relative">
            {/* Content (scrollable) */}
            <div className="flex-1 overflow-y-scroll space-y-3 pr-1 min-h-0 max-h-[88vh]">
                {titleSection}
                {fromToSection}
                {detailsSection}
                {statesSection}
                {alertsTagSection}
                {notesSection}
            </div>

            {/* Footer (sticky) */}

            <DetailsInfoSectionFooter
                tripDetail={tripDetail}
                // isFocused={isFocused ?? false}
                // isSuspicious={isSuspicious ?? false}
                onMarkAsSuspiciousClicked={() => {
                    try {
                        markAsSuspicious(tripId, !tripDetail?.isSuspicious);
                        showSuccess(t('common.success'), t('tripDetails.tripStatusChangeSuccess'));
                        // setIsSuspicious(!isSuspicious);
                    } catch {
                        logger.error('[DetailsInfoSection] Error occurred while suspending trip');
                        showError(t('common.error'), t('tripDetails.tripStatusChangeFailed'));
                    }
                }}
                onMarkAsFocusedClicked={() => {
                    try {
                        markAsFocused(tripId, !tripDetail?.isFocused);
                        showSuccess(t('common.success'), t('tripDetails.tripStatusChangeSuccess'));
                        // setIsFocused(!isFocused);
                    } catch {
                        logger.error('[DetailsInfoSection] Error occurred while suspending trip');
                        showError(t('common.error'), t('tripDetails.tripStatusChangeFailed'));
                    }
                }}
                onPrintClicked={() => handlePrint(tripDetail)}
                onOpenEndTripClicked={handleOpenEndTripDialog}
            />

            {/* Dialog for End Trip Reason */}
            <Dialog open={reasonDialogVisible} onOpenChange={setReasonDialogVisible}>
                <DialogContent className=" h-auto max-h-[80vh] p-0 rounded-2xl shadow-lg">
                    <DialogHeader className="px-3 py-3 border-b-2">
                        <DialogTitle className="text-xl flex items-center gap-2">
                            <div className="flex items-center gap-1  text-green-700">
                                <Icon name="endTrip" className="size-6  text-green-700" />
                                <span>{t('tripDetails.endingTrip')}</span>
                            </div>
                        </DialogTitle>
                    </DialogHeader>

                    <div className="px-3">
                        <label htmlFor="reason" className="mb-2">
                            {t('tripDetails.reason')}
                        </label>
                        <Input
                            id="reason"
                            value={reason}
                            placeholder={t('tripDetails.enterReason')}
                            onChange={(e) => {
                                setReason(e.target.value);
                                if (error) setError('');
                            }}
                        />
                        {error && <p className="text-red-500 text-sm mt-1">{error}</p>}
                    </div>

                    <DialogFooter className="px-3 py-3  border-t-2">
                        <Button variant="outline" onClick={() => setReasonDialogVisible(false)}>
                            {t('common.cancel')}
                        </Button>
                        <Button className="px-4 py-2 bg-green-700 text-white rounded" onClick={handleConfirmEndTrip}>
                            {t('common.confirm')}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            {/* Dialog for Add/Edit Security Notes */}
            <Dialog open={notesDialogVisible} onOpenChange={setNotesDialogVisible}>
                <DialogContent className="h-auto max-h-[80vh] p-0 rounded-2xl shadow-lg">
                    <DialogHeader className="px-3 py-3 border-b-2">
                        <DialogTitle className="text-xl flex items-center gap-2">
                            <div className="flex items-center gap-1 text-green-700">
                                {/* <Icon name="notification" className="size-6 text-green-700" /> */}
                                <span>{t('tripDetails.editSecurityNotes')}</span>
                            </div>
                        </DialogTitle>
                    </DialogHeader>

                    <div className="px-3 py-3">
                        <label htmlFor="securityNotes" className="mb-2 block">
                            {t('tripDetails.securityNotes')}
                        </label>
                        <textarea
                            id="securityNotes"
                            className="w-full border rounded-md p-2 text-sm"
                            rows={5}
                            value={securityNotes}
                            onChange={(e) => setSecurityNotes(e.target.value)}
                            placeholder={t('tripDetails.enterSecurityNotes')}
                        />
                    </div>

                    <DialogFooter className="px-3 py-3 border-t-2">
                        <Button variant="outline" onClick={() => setNotesDialogVisible(false)}>
                            {t('common.cancel')}
                        </Button>
                        <Button
                            className="px-4 py-2 bg-green-700 text-white rounded"
                            onClick={handleSaveNotes}
                            disabled={savingNotes}>
                            {savingNotes ? t('common.saving') : t('common.save')}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    );
}
