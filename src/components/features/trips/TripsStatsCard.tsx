import { t } from 'i18next';

import SummaryCard from '@/components/common/ui/SummaryCard';
import type { IconName } from '@/components/common/ui/Icon';

export type TripsStatsCardType =
    | 'ActiveTripsWithActiveAlerts'
    | 'ActiveTripsWithoutActiveAlerts'
    | 'ActiveTrips'
    | 'InActiveTrips';

export interface TripsStatsCardProps {
    type: TripsStatsCardType;
    value: number;
}
export type TripsStatsCardTheme = {
    title: string;
    iconName: IconName;
    iconColor?: string;
    iconBgColor?: string;
};

const getTripsStatsCardTheme = (type: TripsStatsCardType): TripsStatsCardTheme => {
    switch (type) {
        case 'ActiveTripsWithActiveAlerts':
            return {
                title: t('common.activeTripsWithAlerts'),
                iconName: 'notification',
                iconColor: '#1B8354',
                iconBgColor: '#1B83541A',
            };
        case 'ActiveTripsWithoutActiveAlerts':
            return {
                title: t('common.activeTripsWithoutAlerts'),
                iconName: 'notificationMuted',
                iconColor: '#F97816',
                iconBgColor: '#F978161A',
            };
        case 'ActiveTrips':
            return {
                title: t('common.totalActiveTrips'),
                iconName: 'mapLocation',
                iconColor: '#179FCA',
                iconBgColor: '#179FCA1A',
            };
        case 'InActiveTrips':
            return {
                title: t('common.totalClosedTrips'),
                iconName: 'noMap',
                iconColor: '#E4514F',
                iconBgColor: '#FCE4E4',
            };
        default:
            return {
                title: t('common.totalClosedTrips'),
                iconName: 'noMap',
                iconColor: '#E4514F',
                iconBgColor: '#FCE4E4',
            };
    }
};
export default function TripsStatsCard({ type, value }: TripsStatsCardProps) {
    const theme = getTripsStatsCardTheme(type);
    return (
        <SummaryCard
            details={{
                ...theme,
                value: Number(value),
            }}
            key={type}
        />
    );
}
