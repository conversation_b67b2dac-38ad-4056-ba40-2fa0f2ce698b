import { useTranslation } from 'react-i18next';

import { Tag } from '@/components/common/ui/Tag';

import { TripAlertCarousel } from '@/components/features/trips/trip-details/TripAlertCarousel';
import { useLocalized } from '@/shared/hooks/use-localized.hook';
import type { Alert } from '@/infrastructure/api/trips/types';

type TripAlertsSectionProps = {
    alerts: Array<Alert>;
    showCarousel?: boolean; // if true → shows carousel, otherwise shows tags
};

export function TripAlertsSection({ alerts, showCarousel = false }: TripAlertsSectionProps) {
    const { t } = useTranslation();
    const localized = useLocalized();

    if (!alerts || alerts.length === 0) {
        return <p className="text-gray-500 text-xs">{t('tripDetails.noAlerts')}</p>;
    }

    return (
        <div className={`border-t pt-2 text-xs w-[99%]`}>
            {showCarousel ? (
                <TripAlertCarousel alerts={alerts} />
            ) : (
                <>
                    <h3 className="font-semibold text-gray-800 mb-2">{t('tripDetails.alerts')}</h3>

                    <div className="flex flex-wrap gap-2">
                        {alerts.map((alert, idx) => (
                            <Tag key={idx} bgColor="bg-red-100" textColor="text-red-700" icon="alert">
                                {localized(alert.alertType.name)}
                            </Tag>
                        ))}
                    </div>
                </>
            )}
        </div>
    );
}
