import { useState } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { useLocalized } from '@/shared/hooks/use-localized.hook';
import { formatLocalizedDate } from '@/shared/utils/format-date.utils';
import type { Alert } from '@/infrastructure/api/trips/types';

type TripAlertCarouselProps = {
    alerts?: Alert[];
};

export function TripAlertCarousel({ alerts }: TripAlertCarouselProps) {
    const { t, i18n } = useTranslation();
    const localized = useLocalized();
    const [currentIndex, setCurrentIndex] = useState(0);

    if (!alerts || alerts.length === 0) return null;

    const prevAlert = () => {
        setCurrentIndex((prev) => (prev === 0 ? alerts.length - 1 : prev - 1));
    };

    const nextAlert = () => {
        setCurrentIndex((prev) => (prev === alerts.length - 1 ? 0 : prev + 1));
    };

    const currentAlert = alerts[currentIndex];

    // ✅ Detect RTL vs LTR
    const isRTL = i18n.dir() === 'rtl';

    return (
        <div className="relative bg-red-50 border border-red-300 text-red-700 text-sm p-3 rounded">
            {/* Left Arrow (Prev) */}
            <button
                onClick={isRTL ? nextAlert : prevAlert}
                className="absolute top-1/3 -translate-y-1/2 left-2 p-1 rounded-full hover:bg-red-200">
                <ChevronLeft size={18} />
            </button>

            {/* Right Arrow (Next) */}
            <button
                onClick={isRTL ? prevAlert : nextAlert}
                className="absolute top-1/3 -translate-y-1/2 right-2 p-1 rounded-full hover:bg-red-200">
                <ChevronRight size={18} />
            </button>

            {/* Alert Content */}
            <div className="text-center">
                <div className="font-medium">🚨 {localized(currentAlert.alertType?.name)}</div>
                <p className="text-xs text-gray-600 mt-1">
                    {t('tripDetails.tripAlerts.lastUpdate')}: {t('tripDetails.tripAlerts.from')}{' '}
                    <span className="font-semibold text-gray-800">
                        {formatLocalizedDate(currentAlert.fromState?.timestamp)}
                    </span>{' '}
                    {t('tripDetails.tripAlerts.to')}{' '}
                    <span className="font-semibold text-gray-800">
                        {formatLocalizedDate(currentAlert.toState?.timestamp)}
                    </span>
                </p>
            </div>
        </div>
    );
}
