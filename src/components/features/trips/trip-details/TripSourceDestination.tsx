import { useTranslation } from 'react-i18next';

import { Icon } from '../../../common/ui/Icon';

type TripSourceDestinationProps = {
    fromLabel: string;
    toLabel: string;
};

export function TripSourceDestination({ fromLabel, toLabel }: TripSourceDestinationProps) {
    const { t } = useTranslation();

    return (
        <div className="flex flex-col text-xs text-gray-700">
            {/* From / To labels */}
            <div className="grid grid-cols-2 gap-[1.75rem] mb-2">
                {/* From */}
                <div className="flex items-center gap-4 w-50 break-words">
                    <span className="font-medium">{t('common.from')}</span>
                    <span className="font-bold text-gray-900">{fromLabel}</span>
                </div>

                {/* To */}
                <div className="flex items-center gap-4 w-50 break-words">
                    <span className="font-medium">{t('common.to')}</span>
                    <span className="font-bold text-gray-900">{toLabel}</span>
                </div>
            </div>

            {/* Progress bar */}
            <div className="relative w-full h-10 flex items-center">
                {/* Base line */}
                <div className="absolute inset-x-0 top-1/2 -translate-y-1/2 h-1 bg-gray-300">
                    {/* Completed portion (half) */}
                    <div className="h-1 bg-green-700 w-1/2"></div>
                </div>

                {/* Start circle */}
                <div className="absolute ltr:left-0 rtl:right-0 top-1/2 -translate-y-1/2 w-4 h-4 rounded-full bg-green-700 shadow-md z-10"></div>

                {/* Middle truck circle */}
                <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 flex items-center justify-center z-10">
                    {/* Outer circle */}
                    <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                        {/* Inner circle */}
                        <div className="w-9 h-9 rounded-full bg-green-700 flex items-center justify-center shadow-md">
                            <Icon name="truck" className="w-6 h-6 text-white rtl:rotate-y-180" />
                        </div>
                    </div>
                </div>

                {/* End circle */}
                <div className="absolute ltr:right-0 rtl:left-0 top-1/2 -translate-y-1/2 w-4 h-4 rounded-full bg-gray-300 shadow-md z-10"></div>
            </div>
        </div>
    );
}
