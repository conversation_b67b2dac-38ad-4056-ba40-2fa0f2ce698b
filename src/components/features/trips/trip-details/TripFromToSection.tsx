import { useTranslation } from 'react-i18next';

import { formatLocalizedDate } from '@/shared/utils/format-date.utils';

import { TripSourceDestination } from './TripSourceDestination';

type TripFromToSectionProps = {
    fromLabel: string;
    toLabel: string;
    startDate?: string;
    endDate?: string | null;
};

export function TripFromToSection({ fromLabel, toLabel, startDate, endDate }: TripFromToSectionProps) {
    const { t } = useTranslation();

    return (
        <div className="border-t text-xs py-4 w-[99%]">
            {/* From → To labels */}
            <TripSourceDestination fromLabel={fromLabel} toLabel={toLabel} />

            {/* Dates section */}
            <div className="flex justify-between text-gray-500 mt-2">
                {/* Started */}
                <div className="flex flex-col items-start">
                    <span>{t('tripDetails.startedAtDate')}</span>
                    <span className="font-bold">{formatLocalizedDate(startDate)}</span>
                </div>

                {/* Ended */}
                <div className="flex flex-col items-end">
                    <span>{t('tripDetails.endedAtDate')}</span>
                    <span className="font-bold">{endDate ? formatLocalizedDate(endDate) : '-'}</span>
                </div>
            </div>
        </div>
    );
}
