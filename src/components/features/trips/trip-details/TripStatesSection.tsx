import { useTranslation } from 'react-i18next';

import type { TripStates } from '@/shared/utils/trips.utils';

import Tooltip from '../../../common/ui/Tooltip';
import { TooltipTrigger } from '../../../common/ui/LibTooltip';
import { Tag } from '../../../common/ui/Tag';

interface TripStatesSectionProps {
    states: TripStates[];
}

export function TripStatesSection({ states }: TripStatesSectionProps) {
    const { t } = useTranslation();

    if (!states.length) return null;

    return (
        <div className="border-t pt-2 text-xs w-[99%]">
            <h3 className="font-semibold text-gray-800 mb-2">{t('tripDetails.status')}</h3>

            <div className="flex flex-wrap gap-2">
                {states.map(({ id, value, bgColor, textColor, icon, tooltipKey, tooltip }) => (
                    <Tooltip key={id} tooltipMessage={tooltip ? tooltip : t(tooltipKey)}>
                        <TooltipTrigger asChild>
                            <Tag
                                bgColor={bgColor}
                                textColor={textColor}
                                icon={icon}
                                className="inline-flex max-w-max cursor-pointer">
                                {value}
                            </Tag>
                        </TooltipTrigger>
                    </Tooltip>
                ))}
            </div>
        </div>
    );
}
