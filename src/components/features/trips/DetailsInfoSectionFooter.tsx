import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';

import Tooltip from '@/components/common/ui/Tooltip';
import { Button } from '@/components/common/ui/Button';
import { Icon } from '@/components/common/ui/Icon';
// import EmailDialog from '@/components/common/ui/EmailDialog';
// import { TripEmailTemplate } from '@/components/common/email-templates/TripTemplate';
// import { mapTripToTripEmail } from '@/mappers/emails/tripEmail.mapper';
import type { TripDetail } from '@/infrastructure/api/trips/types';
import { Scope } from '@/shared/enums';
import TripEmailDialog from '@/components/common/ui/TripEmailDialog';

export type DetailsInfoSectionFooterProps = {
    tripDetail: TripDetail | null;

    onMarkAsSuspiciousClicked: () => void;
    onMarkAsFocusedClicked: () => void;
    onPrintClicked: () => void;
    onOpenEndTripClicked: () => void;
};
export function DetailsInfoSectionFooter({
    tripDetail,

    onMarkAsSuspiciousClicked,
    onMarkAsFocusedClicked,
    onPrintClicked,
    onOpenEndTripClicked,
}: DetailsInfoSectionFooterProps) {
    const { t } = useTranslation();

    const isFocused = tripDetail?.isFocused ?? false;
    const isSuspicious = tripDetail?.isSuspicious ?? false;

    useEffect(() => {}, [tripDetail?.isFocused, tripDetail?.isSuspicious]);

    return (
        <div className="sticky bottom-0 bg-white border-t p-2 flex justify-end items-center gap-1 shadow-sm">
            <Tooltip
                tooltipMessage={isSuspicious ? t('tripDetails.unmarkSuspicious') : t('tripDetails.markSuspicious')}>
                <Button onClick={onMarkAsSuspiciousClicked} variant={'outline'}>
                    {isSuspicious ? t('tripDetails.unSuspicious') : t('tripDetails.Suspicious')}
                </Button>
            </Tooltip>
            <Tooltip tooltipMessage={t('common.print')}>
                <Button onClick={onPrintClicked} variant={'outline'} className="inline-flex max-w-max cursor-pointer">
                    <Icon name="print" />
                </Button>
            </Tooltip>
            <Tooltip tooltipMessage={t('emailDialog.title')}>
                {/* <EmailDialog
                    scope={Scope.Trip}
                    emailBody={tripDetail ? <TripEmailTemplate trip={mapTripToTripEmail(tripDetail)} /> : ''}>
                    <Button variant={'outline'}>
                        <Icon name="email" className="size-6" />
                    </Button>
                </EmailDialog> */}
                <TripEmailDialog scope={Scope.Trip}>
                    <Button variant={'outline'}>
                        <Icon name="email" className="size-6" />
                    </Button>
                </TripEmailDialog>
            </Tooltip>

            {tripDetail && tripDetail.status != 'Ended' && (
                <Tooltip tooltipMessage={t('tripDetails.endingTrip')}>
                    <Button variant={'outline'} onClick={onOpenEndTripClicked}>
                        <Icon name="endTrip" className="size-6" />
                    </Button>
                </Tooltip>
            )}
            <Tooltip tooltipMessage={isFocused ? t('tripDetails.unTracking') : t('tripDetails.tracking')}>
                <Button variant={'outline'} onClick={onMarkAsFocusedClicked}>
                    {isFocused ? t('tripDetails.unTracking') : t('tripDetails.tracking')}
                </Button>
            </Tooltip>
        </div>
    );
}
