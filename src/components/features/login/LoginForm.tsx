import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FaEye, FaEyeSlash } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';

import { useLoginStore } from '@/stores/login.store';
import { Button } from '@/components/common/ui/Button';
import { Input } from '@/components/common/ui/Input';
import { Label } from '@/components/common/ui/Label';
import { Checkbox } from '@/components/common/ui/Checkbox';
import Loader from '@/components/common/ui/Loader';

export default function LoginForm() {
    const [showPassword, setShowPassword] = useState<boolean>(false);
    const [rememberMe, setRememberMe] = useState<boolean>(false);
    const { t } = useTranslation();
    const navigate = useNavigate();
    const isDataLoading = useLoginStore((state) => state.isDataLoading);
    const setUsername = useLoginStore((state) => state.setUsername);
    const setPassword = useLoginStore((state) => state.setPassword);
    const submit = useLoginStore((state) => state.submit);

    return (
        <form className="space-y-6">
            {/* Username Field */}
            <div className="space-y-2">
                <Label className="block text-gray-700">{t('login.username')}</Label>
                <div className="relative">
                    {/* <div className="absolute inset-y-0 start-0 ps-3 flex items-center pointer-events-none">
                        <HiOutlineMail className="size-5 text-gray-400" />
                    </div> */}
                    <Input
                        type="text"
                        onChange={(e) => setUsername(e.target.value)}
                        placeholder={t('login.usernamePlaceholder')}
                    />
                </div>
            </div>

            {/* Password Field */}
            <div className="space-y-2">
                <Label className="block text-gray-700">{t('login.password')}</Label>
                <div className="relative">
                    {/* <div className="absolute inset-y-0 start-0 ps-3 flex items-center pointer-events-none">
                        <HiMiniLockClosed className="size-5 text-gray-400" />
                    </div> */}
                    <Input
                        type={showPassword ? 'text' : 'password'}
                        onChange={(e) => setPassword(e.target.value)}
                        placeholder={t('login.passwordPlaceholder')}
                    />
                    <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute inset-y-0 end-0 pe-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors"
                        aria-label={showPassword ? t('login.hidePassword') : t('login.showPassword')}>
                        {showPassword ? <FaEyeSlash className="size-5" /> : <FaEye className="size-5" />}
                    </button>
                </div>
            </div>

            {/* Remember Me and Forgot Password */}
            <div className="flex items-center justify-between">
                <Checkbox
                    label={t('login.rememberMe')}
                    checked={rememberMe}
                    onChange={setRememberMe}
                    className="hover:bg-transparent p-0"
                />
                <button
                    type="button"
                    className="text-sm text-blue-500 hover:text-blue-600 hover:underline transition-colors"
                    onClick={(e) => e.preventDefault()}>
                    {t('login.forgotPassword')}
                </button>
            </div>

            {/* Login Button */}
            <Button
                type="submit"
                disabled={isDataLoading}
                className="w-full bg-green-700 hover:bg-green-800 text-white"
                onClick={(e) => {
                    e.preventDefault();
                    submit(navigate);
                }}>
                <Loader isLoading={isDataLoading} />
                {t('login.loginButton')}
            </Button>
        </form>
    );
}
