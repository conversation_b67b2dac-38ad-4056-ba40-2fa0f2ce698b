import { t } from 'i18next';

import SummaryCard from '@/components/common/ui/SummaryCard';
import type { IconName } from '@/components/common/ui/Icon';

import { TimeCard } from './TimeCard';

export type AlertsStatsCardType = 'time' | 'totalAck' | 'totalNotAck' | 'totalActiveTrips' | 'totalInActiveTrips';

export interface AlertsStatsCardProps {
    type: AlertsStatsCardType;
    value: number;
}
export type AlertsStatsCardTheme = {
    title: string;
    iconName: IconName;
    iconColor?: string;
    iconBgColor?: string;
};

const getAlertsStatsCardTheme = (type: AlertsStatsCardType): AlertsStatsCardTheme => {
    switch (type) {
        case 'time':
            return {
                title: t('alerts.time'),
                iconName: 'clock',
                iconColor: '#1B8354',
                iconBgColor: '#1B83541A',
            };
        case 'totalAck':
            return {
                title: t('alerts.totalNotAckAlerts'),
                iconName: 'ackAlert',
                iconColor: '#F97816',
                iconBgColor: '#F978161A',
            };
        case 'totalNotAck':
            return {
                title: t('alerts.totalNotAckAlerts'),
                iconName: 'notAckAlert',
                iconColor: '#179FCA',
                iconBgColor: '#179FCA1A',
            };
        case 'totalActiveTrips':
            return {
                title: t('alerts.totalActiveTrips'),
                iconName: 'mapLocation',
                iconColor: '#E4514F',
                iconBgColor: '#FCE4E4',
            };
        case 'totalInActiveTrips':
            return {
                title: t('alerts.totalInActiveTrips'),
                iconName: 'noMap',
                iconColor: '#E4514F',
                iconBgColor: '#FCE4E4',
            };
    }
};
export default function AlertsStatsCard({ type, value }: AlertsStatsCardProps) {
    if (type == 'time') return <TimeCard />;

    const theme = getAlertsStatsCardTheme(type);
    return (
        <SummaryCard
            details={{
                ...theme,
                value: Number(value),
            }}
            key={type}
        />
    );
}
