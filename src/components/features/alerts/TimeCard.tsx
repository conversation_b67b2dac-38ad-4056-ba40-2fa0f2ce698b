import { useState, useEffect } from 'react';
import { t } from 'i18next';

import SummaryCard from '@/components/common/ui/SummaryCard';
import { formatLocalizedDate } from '@/shared/utils/format-date.utils';

export function TimeCard() {
    const [currentTime, setCurrentTime] = useState(new Date());
    const ONE_SECOND = 1000;

    useEffect(() => {
        const interval = setInterval(() => {
            setCurrentTime(new Date());
        }, ONE_SECOND);

        return () => clearInterval(interval);
    }, []);

    return (
        <SummaryCard
            details={{
                title: t('alerts.time'),
                iconName: 'clock',
                iconColor: '#1B8354',
                iconBgColor: '#1B83541A',
                value: formatLocalizedDate(currentTime),
            }}
        />
    );
}
