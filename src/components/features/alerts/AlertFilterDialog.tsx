import '@/components/common/trip-filter-dialog/TripFilterDialog.css';
import { Accordion, AccordionTab } from 'primereact/accordion';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ChevronDown, ChevronUp, FilterIcon, RefreshCwIcon } from 'lucide-react';

import { Button } from '@/components/common/ui/Button';
import { Checkbox } from '@/components/common/ui/Checkbox';
import { Radio } from '@/components/common/ui/Radio';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/common/ui/Dialog';
import { useAlertFiltersStore } from '@/stores/alert-filters.store';
import { useAlertTypeLookups, useAlertTypeStore } from '@/stores/alert-type.store';
import { useLocalized } from '@/shared/hooks/use-localized.hook';
import { Icon } from '@/components/common/ui/Icon';
import { AlertAcknowledgement } from '@/shared/enums';
import { LoaderButton } from '@/components/common/ui/LoaderButton';

interface LocalProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    onApply: () => void;
    portalContainer?: HTMLElement;
}

export default function AlertFilterDialog({ open, onOpenChange, onApply, portalContainer }: Readonly<LocalProps>) {
    const { t } = useTranslation();
    const { t: tWarnings } = useTranslation('warnings');
    const alertTypes = useAlertTypeLookups();
    const loadAlertTypes = useAlertTypeStore((state) => state.loadAlertTypes);
    const localized = useLocalized();

    // Load alert types when component mounts
    useEffect(() => {
        loadAlertTypes({ PageNumber: 1 });
    }, [loadAlertTypes]);

    // Store selectors (fine-grained)
    const filters = useAlertFiltersStore((state) => state.filters);
    const appliedFiltersCount = useAlertFiltersStore((state) => state.appliedFiltersCount);
    const updateListSection = useAlertFiltersStore((state) => state.updateListSection);
    const setFilter = useAlertFiltersStore((state) => state.setFilter);
    const resetFilters = useAlertFiltersStore((state) => state.resetFilters);
    const applyFilters = useAlertFiltersStore((state) => state.applyFilters);

    // UI-only state
    const [activeIndex, setActiveIndex] = useState<number | number[]>([0, 1, 2]);
    const [isApplying, setIsApplying] = useState(false);

    const handleApply = async () => {
        setIsApplying(true);
        // Simulate loading
        await new Promise((resolve) => setTimeout(resolve, 2000));

        onApply();
        // Call store's applyFilters
        applyFilters();
        setIsApplying(false);
        onOpenChange(false);
    };

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent
                portalContainer={portalContainer}
                className="filter-container w-full max-w-[95vw] h-auto max-h-[80vh] p-0 rounded-2xl shadow-lg">
                <DialogHeader className="px-6 py-3 pb-3 border-b-2">
                    <DialogTitle className="text-xl flex items-center gap-3">
                        <div className="flex items-center gap-1">
                            <Icon name="filter" />
                            <span>{t('common.advancedFilter')}</span>
                        </div>
                    </DialogTitle>
                </DialogHeader>
                <DialogDescription />
                <div className="overflow-y-auto px-3">
                    <Accordion
                        className="flex flex-wrap gap-3 justify-center"
                        multiple
                        activeIndex={activeIndex}
                        onTabChange={(e) => setActiveIndex(e.index)}>
                        {/* Alert Types */}
                        <AccordionTab
                            headerClassName="accordion-header"
                            className="md:w-[91vw] lg:w-[93vw]"
                            contentClassName="accordion-body"
                            header={
                                <div className="flex items-center justify-between gap-1">
                                    <div className="flex items-center gap-1">
                                        <Icon name="alert" />
                                        <span>{t('filter.warnings')}</span>
                                    </div>
                                    <div>
                                        {Array.isArray(activeIndex) && activeIndex.includes(0) ? (
                                            <ChevronUp className="w-4 h-4" />
                                        ) : (
                                            <ChevronDown className="w-4 h-4 text-gray-400" />
                                        )}
                                    </div>
                                </div>
                            }>
                            {/* Active/All Alerts */}
                            <div className="flex items-center justify-start gap-3 border-b p-3">
                                <Radio
                                    label={tWarnings('activeWarnings')}
                                    name="alerts-radio-group"
                                    onChange={() => setFilter('activeAlertsOnly', true)}
                                    checked={filters.activeAlertsOnly === true}
                                />
                                <Radio
                                    label={tWarnings('allWarnings')}
                                    name="alerts-radio-group"
                                    onChange={() => setFilter('activeAlertsOnly', false)}
                                    checked={filters.activeAlertsOnly === false}
                                />
                            </div>

                            {/* Acknowledgement Filter */}
                            <div className="flex items-center justify-start gap-3 border-b p-3">
                                <Radio
                                    label={tWarnings('acknowledgeWarnings')}
                                    name="acknowledgement-radio-group"
                                    onChange={() =>
                                        setFilter('alertAcknowledgement', AlertAcknowledgement.ACKNOWLEDGED)
                                    }
                                    checked={filters.alertAcknowledgement === AlertAcknowledgement.ACKNOWLEDGED}
                                />
                                <Radio
                                    label={tWarnings('notAcknowledgedWarnings')}
                                    name="acknowledgement-radio-group"
                                    onChange={() =>
                                        setFilter('alertAcknowledgement', AlertAcknowledgement.NOT_ACKNOWLEDGED)
                                    }
                                    checked={filters.alertAcknowledgement === AlertAcknowledgement.NOT_ACKNOWLEDGED}
                                />
                                <Radio
                                    label={tWarnings('allWarnings')}
                                    name="acknowledgement-radio-group"
                                    onChange={() => setFilter('alertAcknowledgement', AlertAcknowledgement.ALL)}
                                    checked={filters.alertAcknowledgement === AlertAcknowledgement.ALL}
                                />
                            </div>

                            {/* Alert Types Checkboxes */}
                            <div className="grid md:grid-cols-3 lg:grid-cols-5 gap-x-3 gap-y-1">
                                <Checkbox
                                    isSelectAll={true}
                                    label={t('filter.selectAll')}
                                    checked={filters.alertTypes?.length === alertTypes.length && alertTypes.length > 0}
                                    onChange={(checked: boolean) => {
                                        if (checked) {
                                            alertTypes.forEach((p) => updateListSection('alertTypes', p.id, true));
                                        } else {
                                            alertTypes.forEach((p) => updateListSection('alertTypes', p.id, false));
                                        }
                                    }}
                                />
                            </div>
                            <div className="grid md:grid-cols-4 lg:grid-cols-4 gap-x-3 gap-y-1">
                                {alertTypes.map((item) => (
                                    <Checkbox
                                        key={item.id}
                                        label={localized(item.name)}
                                        checked={filters.alertTypes?.includes(item.id) ?? false}
                                        onChange={(checked: boolean) =>
                                            updateListSection('alertTypes', item.id, checked)
                                        }
                                    />
                                ))}
                            </div>
                        </AccordionTab>
                    </Accordion>
                </div>

                {/* Footer Buttons */}
                <div className="ms-auto pb-4 px-10 flex items-center gap-3">
                    <Button variant="outline" className="mx-3" onClick={() => resetFilters()}>
                        <RefreshCwIcon /> {t('filter.reset')}
                    </Button>
                    <LoaderButton
                        loading={isApplying}
                        defaultText={`${t('filter.applyFilter')} (${appliedFiltersCount})`}
                        isLoadingText={`${t('filter.applyingFilter')} (${appliedFiltersCount})`}
                        icon={<FilterIcon />}
                        variant="success"
                        onClick={handleApply}
                    />
                </div>
            </DialogContent>
        </Dialog>
    );
}
