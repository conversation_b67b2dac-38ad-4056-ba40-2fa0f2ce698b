import { useTranslation } from 'react-i18next';
import { useEffect, memo } from 'react';

import { Icon } from '@/components/common/ui/Icon';
import { useLocalized } from '@/shared/hooks/use-localized.hook';
import { formatLocalizedDate } from '@/shared/utils/format-date.utils';
import { constructTripStates } from '@/shared/utils/trips.utils';
import Tooltip from '@/components/common/ui/Tooltip';
import { InfoRow } from '@/components/common/ui/InfoRow';
import { Button } from '@/components/common/ui/Button';
import { cn } from '@/shared/utils/class-name.utils';
import type { Alert } from '@/infrastructure/api/alerts/types';

interface AlertDrawerProps {
    isOpen: boolean;
    onClose: () => void;
    alert: Alert | null;
    dir: 'ltr' | 'rtl';
}

function AlertDrawer({ isOpen, onClose, alert, dir }: AlertDrawerProps) {
    const { t } = useTranslation();
    const { localized } = useLocalized();

    // Handle escape key to close drawer
    useEffect(() => {
        const handleEscape = (event: KeyboardEvent) => {
            if (event.key === 'Escape' && isOpen) {
                onClose();
            }
        };

        if (isOpen) {
            document.addEventListener('keydown', handleEscape);
            // Prevent body scroll when drawer is open
            document.body.style.overflow = 'hidden';
        }

        return () => {
            document.removeEventListener('keydown', handleEscape);
            document.body.style.overflow = 'unset';
        };
    }, [isOpen, onClose]);

    if (!isOpen || !alert) {
        return null;
    }

    const drawerPosition = dir === 'rtl' ? 'right-0' : 'left-0';
    const slideDirection = dir === 'rtl' ? 'translate-x-full' : '-translate-x-full';

    return (
        <>
            <div
                className="fixed inset-0 bg-black/30 z-40 transition-opacity duration-300 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0"
                onClick={onClose}
            />

            <div
                className={cn(
                    'fixed top-0 h-full w-96 bg-white shadow-xl z-50 transform transition-transform duration-300 ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left',
                    drawerPosition,
                    isOpen ? 'translate-x-0' : slideDirection,
                )}
                dir={dir}
                role="dialog"
                aria-modal="true"
                aria-labelledby="alert-drawer-title">
                {/* Header */}
                <div className="border-b border-gray-200 bg-gray-50 px-4 py-3">
                    <div className="flex items-center justify-between">
                        <h2 id="alert-drawer-title" className="text-lg font-semibold text-gray-800">
                            {t('common.alertsDetails')}
                        </h2>
                        <Button
                            variant="ghost"
                            size="icon"
                            onClick={onClose}
                            className="h-8 w-8"
                            aria-label={t('common.close')}>
                            <Icon name="close" className="h-4 w-4" />
                        </Button>
                    </div>
                </div>

                {/* Content */}
                <div className="flex flex-col h-full">
                    {/* Scrollable Content */}
                    <div className="flex-1 overflow-y-auto space-y-3 p-4 min-h-0">
                        {/* Alert Title Section */}
                        <div className="space-y-4 border-t pt-3">
                            <div className="flex items-center gap-3">
                                <Icon name="alert" className="size-8 bg-red-500 text-white rounded-full p-1" />
                                <div className="space-y-1">
                                    <h3 className="text-sm font-semibold leading-tight">
                                        {localized(alert.alertType.name)}
                                    </h3>
                                    <p className="text-xs text-gray-500 leading-tight">Alert ID: {alert.id}</p>
                                </div>
                            </div>

                            {/* Status Badge */}
                            <div className="flex items-center gap-2">
                                {alert.acknowledgedAt ? (
                                    <div className="flex items-center gap-2">
                                        <Icon name="ackAlert" className="w-4 h-4 text-green-600" />
                                        <span className="px-2 py-1 bg-green-100 text-green-700 rounded-md text-sm font-medium">
                                            {t('events.acknowledge')}
                                        </span>
                                    </div>
                                ) : (
                                    <div className="flex items-center gap-2">
                                        <Icon name="notAckAlert" className="w-4 h-4 text-red-500" />
                                        <span className="px-2 py-1 bg-red-100 text-red-700 rounded-md text-sm font-medium">
                                            {t('common.notAcknowledged')}
                                        </span>
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Alert Details Section */}
                        <div className="space-y-4 border-t pt-3">
                            <h3 className="font-semibold text-gray-800 mb-2">{t('common.alertsDetails')}</h3>
                            <div className="space-y-1">
                                <InfoRow label={t('common.alertId')} value={alert.id} />
                                <InfoRow
                                    label={t('alert.tripId')}
                                    value={alert.tripId}
                                    isLink={true}
                                    onClick={() => {
                                        // Could navigate to trip details
                                        // console.log('Navigate to trip:', alert.tripId);
                                    }}
                                />
                                <InfoRow label={t('common.alertType')} value={localized(alert.alertType.name)} />

                                {alert.acknowledgedBy && (
                                    <InfoRow label={t('events.user')} value={alert.acknowledgedBy.name} />
                                )}

                                {alert.acknowledgedAt && (
                                    <InfoRow
                                        label={`${t('events.acknowledge')} ${t('alert.timeStamp')}`}
                                        value={formatLocalizedDate(alert.acknowledgedAt)}
                                    />
                                )}
                            </div>
                        </div>

                        {/* From State Section */}
                        <div className="space-y-4 border-t pt-3">
                            <h3 className="font-semibold text-gray-800 mb-2 flex items-center gap-2">
                                <Icon name="locationPin" className="w-5 h-5 text-green-600" />
                                {t('common.fromState')}
                            </h3>
                            <div className="space-y-1">
                                <InfoRow
                                    label={t('alert.timeStamp')}
                                    value={formatLocalizedDate(alert.fromState.trackerDateTime)}
                                />
                                <InfoRow
                                    label={t('common.location')}
                                    value={`(${alert.fromState.lat}, ${alert.fromState.long})`}
                                />
                                <InfoRow label={t('common.address')} value={localized(alert.fromState.address)} />
                                <InfoRow label={t('common.speed')} value={`${alert.fromState.currentSpeed} km/h`} />

                                {/* States */}
                                <div className="flex flex-col gap-2">
                                    <span className="text-sm font-medium text-gray-600">{t('common.state')}:</span>
                                    <div className="flex gap-2 items-center flex-wrap">
                                        {constructTripStates(alert.fromState).map(
                                            ({ id, textColor, icon, tooltipKey, tooltip }) => (
                                                <Tooltip key={id} tooltipMessage={tooltip ? tooltip : t(tooltipKey)}>
                                                    <Icon
                                                        name={icon}
                                                        className={`${textColor} cursor-pointer size-5`}
                                                    />
                                                </Tooltip>
                                            ),
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* To State Section */}
                        {alert.toState && (
                            <div className="space-y-4 border-t pt-3">
                                <h3 className="font-semibold text-gray-800 mb-2 flex items-center gap-2">
                                    <Icon name="locationPin" className="w-5 h-5 text-blue-600" />
                                    {t('common.toState')}
                                </h3>
                                <div className="space-y-1">
                                    <InfoRow
                                        label={t('alert.timeStamp')}
                                        value={formatLocalizedDate(alert.toState.trackerDateTime)}
                                    />
                                    <InfoRow
                                        label={t('common.location')}
                                        value={`(${alert.toState.lat}, ${alert.toState.long})`}
                                    />
                                    <InfoRow label={t('common.address')} value={localized(alert.toState.address)} />
                                    <InfoRow label={t('common.speed')} value={`${alert.toState.currentSpeed} km/h`} />

                                    {/* States */}
                                    <div className="flex flex-col gap-2">
                                        <span className="text-sm font-medium text-gray-600">{t('common.state')}:</span>
                                        <div className="flex gap-2 items-center flex-wrap">
                                            {constructTripStates(alert.toState).map(
                                                ({ id, textColor, icon, tooltipKey }) => (
                                                    <Tooltip key={id} tooltipMessage={t(tooltipKey)}>
                                                        <Icon
                                                            name={icon}
                                                            className={`${textColor} cursor-pointer size-5`}
                                                        />
                                                    </Tooltip>
                                                ),
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>

                    {/* Footer Actions */}
                    <div className="sticky bottom-0 bg-white border-t border-gray-200 p-4 flex justify-end items-center gap-2 shadow-sm">
                        <Button variant="outline" onClick={onClose}>
                            {t('common.close')}
                        </Button>
                        {/* Add more action buttons here if needed */}
                    </div>
                </div>
            </div>
        </>
    );
}

export default memo(AlertDrawer);
