import { t } from 'i18next';

import SummaryCard from '@/components/common/ui/SummaryCard';
import type { IconName } from '@/components/common/ui/Icon';

export type UsersStatsCardType = 'totalUsers' | 'totalActiveUsers' | 'totalInActiveUsers';

export interface UsersStatsCardProps {
    type: UsersStatsCardType;
    value: number;
}
export type UsersStatsCardTheme = {
    title: string;
    iconName: IconName;
    iconColor?: string;
    iconBgColor?: string;
};

const getUsersStatsCardTheme = (type: UsersStatsCardType): UsersStatsCardTheme => {
    switch (type) {
        case 'totalUsers':
            return {
                title: t('users.totalUsers'),
                iconName: 'ackAlert',
                iconColor: '#1B8354',
                iconBgColor: '#1B83541A',
            };
        case 'totalActiveUsers':
            return {
                title: t('users.totalActiveUsers'),
                iconName: 'notAckAlert',
                iconColor: '#F97816',
                iconBgColor: '#F978161A',
            };
        case 'totalInActiveUsers':
            return {
                title: t('users.totalInActiveUsers'),
                iconName: 'notAckAlert',
                iconColor: '#179FCA',
                iconBgColor: '#179FCA1A',
            };
    }
};
export default function UsersStatsCard({ type, value }: UsersStatsCardProps) {
    const theme = getUsersStatsCardTheme(type);
    return (
        <SummaryCard
            details={{
                ...theme,
                value: Number(value),
            }}
            key={type}
        />
    );
}
