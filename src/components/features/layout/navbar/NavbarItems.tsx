import './Navbar.css';
import { useTranslation } from 'react-i18next';
import { NavLink } from 'react-router-dom';

export default function NavbarItems() {
    const { t } = useTranslation();

    return (
        <div className="nav-items-container">
            <NavLink
                className={({ isActive }) => ['item', isActive || location.pathname === '/' ? 'active' : ''].join(' ')}
                to="/monitor-board">
                {' '}
                {t('navbar.monitoring')}{' '}
            </NavLink>
            <NavLink className={`item ${(isActive: boolean) => (isActive ? 'active' : '')}`} to="/trips">
                {' '}
                {t('navbar.trips')}{' '}
            </NavLink>
            <NavLink className={`item ${(isActive: boolean) => (isActive ? 'active' : '')}`} to="/alerts">
                {' '}
                {t('reports.alerts')}{' '}
            </NavLink>
            <NavLink className={`item ${(isActive: boolean) => (isActive ? 'active' : '')}`} to="/dashboard">
                {' '}
                {t('navbar.dashboard')}{' '}
            </NavLink>
            <NavLink className={`item ${(isActive: boolean) => (isActive ? 'active' : '')}`} to="/ports">
                {' '}
                {t('navbar.ports')}{' '}
            </NavLink>
            <NavLink className={`item ${(isActive: boolean) => (isActive ? 'active' : '')}`} to="/settings">
                {' '}
                {t('navbar.settings')}{' '}
            </NavLink>
            <NavLink className={`item ${(isActive: boolean) => (isActive ? 'active' : '')}`} to="/users">
                {' '}
                {t('navbar.usersManagement')}{' '}
            </NavLink>
        </div>
    );
}
