import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { IoLogOutOutline } from 'react-icons/io5';

import userAvatar from '@imgs/avatar.png';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/common/ui/DropdownMenu';
import { useAuthStore } from '@/stores/auth.store';

export default function UserDetails() {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const { user, logout } = useAuthStore();

    const handleLogout = () => {
        logout();
        navigate('/login');
    };

    return (
        <>
            <DropdownMenu>
                <DropdownMenuTrigger className="outline-none">
                    <div className="flex items-center gap-3 ">
                        <p className="m-0 font-semibold text-[15px] whitespace-nowrap">{user?.userName || 'User'}</p>
                        <img
                            className="inline-block size-6.5 rounded-full ring-2 ring-[#aaaaaa3d]"
                            src={userAvatar}
                            alt="User Avatar"
                        />
                    </div>
                </DropdownMenuTrigger>

                <DropdownMenuContent sideOffset={8} className="text-center _effect mx-4">
                    <DropdownMenuItem
                        className="cursor-pointer w-[100%] text-[red] flex gap-3 items-center justify-center hover:bg-gray-100"
                        onClick={handleLogout}>
                        <span className="text-[red]"> {t('navbar.logout')} </span>
                        <IoLogOutOutline className="size-[18px] text-[red]" />
                    </DropdownMenuItem>
                </DropdownMenuContent>
            </DropdownMenu>
        </>
    );
}
