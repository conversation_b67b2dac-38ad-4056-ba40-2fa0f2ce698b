import { useTranslation } from 'react-i18next';

import logo from '@imgs/logo.png';
import { Card, CardContent } from '@/components/common/ui/Card';

import LoginForm from '../features/login/LoginForm';
import LanguageButton from '../features/layout/navbar/LanguageButton';

export default function Login() {
    const { t } = useTranslation();

    return (
        <div className="min-h-screen bg-gray-100 flex items-center justify-center p-4">
            <div className="w-full max-w-md">
                <Card className="bg-white shadow-lg border-0 rounded-xl">
                    <CardContent className="p-8">
                        {/* Logo Section */}
                        <div className="text-center">
                            <img src={logo} alt="logo" className="h-16 w-auto mx-auto mb-6" />
                            <h1 className="text-2xl font-bold text-green-600 mb-2">{t('login.welcomeBack')}</h1>
                            <p className="text-gray-500 text-sm mb-8">{t('login.pleaseLoginToContinue')}</p>
                        </div>

                        {/* Login Form */}
                        <LoginForm />
                    </CardContent>
                </Card>

                {/* Language Switcher */}
                <div className="text-center mt-6">
                    <LanguageButton withLabel={true} />
                </div>
            </div>
        </div>
    );
}
