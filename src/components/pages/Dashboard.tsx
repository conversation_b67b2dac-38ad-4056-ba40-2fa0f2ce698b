import ComingSoon from '@/components/common/ui/ComingSoon';
// import { TripEmailTemplate } from '../common/email-templates/TripEmailTemplate';
// import { mapTripToTripEmail } from '@/mappers/emails/tripEmail.mapper';
// import { TripDetailMock } from '@/infrastructure/mocks/api/trip-detail.mock';
// import TripEmailDialog from '../common/ui/TripEmailDialog';
// import { Button } from '../common/ui/Button';
// import { Icon } from '../common/ui/Icon';
// import { Scope } from '@/shared/enums';
// // import type { TripAlert } from '@/infrastructure/api/trip-alerts/types';

// import { TripDetailsSection } from '../common/printing-templates/TripDetailsSection';
// import { SaudiCustomsTuHeader } from '../common/printing-templates/SaudiCustomsTuHeader';
// import { AlertsSection } from '../common/printing-templates/AlertsSection';

export default function Dashboard() {
    return (
        <section className="grid place-items-center w-[100%] h-[100%]">
            <ComingSoon />
        </section>
        // <>
        //     <SaudiCustomsTuHeader />
        //     <TripDetailsSection tripDetail={TripDetailMock} />
        //     <AlertsSection alerts={(TripDetailMock.activeAlerts as unknown as TripAlert[]) || []} />
        // </>
        // <>
        //     <TripEmailDialog scope={Scope.Trip}>
        //         <Button variant={'outline'}>
        //             <Icon name="email" className="size-6" />
        //         </Button>
        //     </TripEmailDialog>
        //     <TripEmailTemplate trip={mapTripToTripEmail(TripDetailMock)} />
        // </>
    );
}
