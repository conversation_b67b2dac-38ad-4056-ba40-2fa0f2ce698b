import { useTranslation } from 'react-i18next';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useNavigate } from 'react-router-dom';

import { useInfiniteScroll } from '@/shared/hooks/use-infinite-scroll.hook';
import DraggableList from '@/components/common/ui/DraggableList';
import Tooltip from '@/components/common/ui/Tooltip';
import { useLocalized } from '@/shared/hooks/use-localized.hook';
import { constructTripStates, getPortIconColor } from '@/shared/utils/trips.utils';
import { formatLocalizedDate } from '@/shared/utils/format-date.utils';
import { useAlertStore } from '@/stores/alert.store';
import type { Alert } from '@/infrastructure/api/alerts/types';
import { useAlertStatsStore } from '@/stores/alerts-stats.store';
import type { AlertFilters } from '@/stores/alert.store';
import { alertsTableExcelMapper } from '@/mappers/alertsTableExcel.mapper';
import { getZoneLabel } from '@/shared/utils/alerts.utils';
import { useTripFiltersStore, type FiltersShape } from '@/stores/trip-filters.store';

import { Icon } from '../common/ui/Icon';
import FilterButton from '../common/ui/FilterButton';
import type { AlertsStatsCardType } from '../features/alerts/AlertsStatsCard';
import AlertsStatsCard from '../features/alerts/AlertsStatsCard';
import AlertDrawer from '../features/alerts/AlertDrawer';
import TripsDetailsLauncher from '../features/trips/TripsDetailsLauncher';
import { ExportExcelButton } from '../common/ui/ExportExcelButton';
import Loader from '../common/ui/Loader';
import TripFilterDialog from '../common/trip-filter-dialog/TripFilterDialog';

const PAGE_SIZE = 50;

const mapTripFiltersToAlertFilters = (tripFilters: FiltersShape): Partial<AlertFilters> => {
    return {
        tripId: null,
        fromServerDate: tripFilters.tripStartDate,
        toServerDate: tripFilters.tripEndDate,
        fromTrackerDate: tripFilters.tripStartDate,
        toTrackerDate: tripFilters.tripEndDate,
        inPorts: tripFilters.inPorts,
        outPorts: tripFilters.outPorts,
        alertTypes: tripFilters.alertTypes,
        activeAlertsOnly: tripFilters.activeAlertsOnly,
        transitNumber: tripFilters.transitNumber,
        transitSeqNumber: tripFilters.transitSeqNumber,
        driverName: tripFilters.driverName,
        driverNationality: tripFilters.driverNationality,
        driverMobileNo: tripFilters.driverMobileNo,
        driverPassportNumber: tripFilters.driverPassportNumber,
        plateNumber: tripFilters.plateNumber,
        trackerNumber: tripFilters.trackerNumber,
        tipCode: tripFilters.tipCode,
        tripCategory: tripFilters.tripCategory,
        tripLocations: tripFilters.tripLocations,
        activeTripsOnly: tripFilters.activeTripsOnly,
        tripStartDate: tripFilters.tripStartDate,
        tripEndDate: tripFilters.tripEndDate,
        transDate: tripFilters.transDate,
        alertAcknowledgement: tripFilters.alertAcknowledgement,
        pageNumber: 1,
        pageSize: PAGE_SIZE,
    };
};
export default function Alerts() {
    const { t, i18n } = useTranslation();
    const { localized, dir } = useLocalized();
    const navigate = useNavigate();

    // Store selectors
    const alertsStats = useAlertStatsStore((s) => s.stats);
    const loadAlertStats = useAlertStatsStore((s) => s.loadAlertStatsSummary);

    const alerts = useAlertStore((s) => s.apiAlerts);
    const pagination = useAlertStore((s) => s.pagination);
    const isLoading = useAlertStore((s) => s.isLoading);
    const loadAlerts = useAlertStore((s) => s.loadAlerts);
    const loadMoreAlerts = useAlertStore((s) => s.loadMoreAlerts);
    // Create refs to avoid function dependency issues
    const loadAlertsRef = useRef(loadAlerts);
    const loadMoreAlertsRef = useRef(loadMoreAlerts);

    // Update refs when functions change
    loadAlertsRef.current = loadAlerts;
    loadMoreAlertsRef.current = loadMoreAlerts;

    // Local state
    const [visible, setVisible] = useState(false);
    const [drawerOpen, setDrawerOpen] = useState(false);
    const [selectedAlert, setSelectedAlert] = useState<Alert | null>(null);

    // const filters = useAlertFiltersStore((s) => s.filters);
    const applyFilter = useCallback(() => {
        // TripFilterDialog already committed draft filters to store before calling this
        // So we can read the committed filters directly from the store
        // TODO:: This has drawback ok performance because it reads the state from the store on every call, hw it works for now -- @0x03r00
        const committedFilters = useTripFiltersStore.getState().filters;
        const apiParams = mapTripFiltersToAlertFilters(committedFilters);
        loadAlertsRef.current({ ...apiParams, pageSize: PAGE_SIZE, pageNumber: 1 });
        setVisible(false);
    }, []);

    const handleRowClick = useCallback((alert: Alert) => {
        setSelectedAlert(alert);
        setDrawerOpen(true);
    }, []);

    const handleCloseDrawer = useCallback(() => {
        setDrawerOpen(false);
        setSelectedAlert(null);
    }, []);

    useEffect(() => {
        loadAlertsRef.current({ pageSize: PAGE_SIZE });
        loadAlertStats();
    }, [loadAlertStats]);

    const defaultOrder: AlertsStatsCardType[] = useMemo(
        () => ['time', 'totalAck', 'totalNotAck', 'totalActiveTrips', 'totalInActiveTrips'],
        [],
    );

    const excelTables = alertsTableExcelMapper(alerts, t, localized);

    //---  Attach infinite scroll hook
    const { loaderRef } = useInfiniteScroll({
        loading: isLoading,
        hasMore: pagination.hasNext,
        onLoadMore: () => {
            // const nextPage = Math.floor(alerts.length / PAGE_SIZE) + 1;
            const nextPage = pagination.currentPage + 1;
            const committedFilters = useTripFiltersStore.getState().filters;
            const apiParams = mapTripFiltersToAlertFilters(committedFilters);
            loadMoreAlertsRef.current({ ...apiParams, pageNumber: nextPage, pageSize: PAGE_SIZE });
        },
    });

    return (
        <div className="flex flex-col pt-2">
            {/* Header */}
            <div className="flex items-center justify-between flex-wrap px-4 gap-1" dir={i18n.dir()}>
                <h1 className="text-xl font-bold py-0">{t('alerts.alerts')}</h1>
                <div className="flex flex-wrap gap-3">
                    <ExportExcelButton
                        fileName={`Alerts_Report`}
                        sheetName="Alerts Report"
                        tables={excelTables}
                        horizontal={true}
                        rtl={dir === 'rtl'}
                    />
                    <FilterButton onClick={() => setVisible(true)} />
                    <TripFilterDialog
                        onApply={applyFilter}
                        open={visible}
                        onOpenChange={setVisible}
                        showTruck={false}
                        showDate={false}
                        showSorting={false}
                    />
                </div>
            </div>

            {/* Summary cards */}
            <DraggableList
                className="flex gap-2 flex-wrap py-2 px-2"
                items={defaultOrder}
                storageKey="tripsCardOrder"
                renderItem={(type) => (
                    <AlertsStatsCard
                        type={type}
                        value={
                            type === 'time'
                                ? alertsStats?.totalActiveTrips || 0
                                : type === 'totalAck'
                                  ? alertsStats?.totalAcknowledgedAlerts || 0
                                  : type === 'totalNotAck'
                                    ? alertsStats?.totalUnacknowledgedAlerts || 0
                                    : type === 'totalActiveTrips'
                                      ? alertsStats?.totalActiveTrips || 0
                                      : alertsStats?.totalInActiveTrips || 0
                        }
                    />
                )}
            />
            {/* DataTable */}
            <div className="_table_container flex-grow-1">
                <DataTable
                    id="tripAlertsTable"
                    key={i18n.language}
                    dataKey="id"
                    value={alerts}
                    dir={dir}
                    scrollable
                    scrollHeight="73vh"
                    // loading={isLoading}
                    tableStyle={{ minWidth: '50rem', marginBottom: '3.5rem' }}
                    className="shadow-md border border-gray-200 "
                    // lazy
                    // totalRecords={pagination.totalCount}
                    onRowClick={(e) => handleRowClick(e.data as Alert)}
                    rowClassName={() => 'cursor-pointer hover:bg-gray-50'}
                    // virtualScrollerOptions={virtualScrollerOptions}
                    emptyMessage={isLoading ? t('loading') : t('common.noDataExist')}>
                    <Column
                        header={t('common.details')}
                        body={(rowData, { rowIndex }) => {
                            const isLast = rowIndex === alerts.length - 3; //-- fire loading before end by 3 elements
                            return (
                                <div
                                    ref={isLast ? loaderRef : null}
                                    onClick={(e) => {
                                        e.stopPropagation();
                                    }}>
                                    <TripsDetailsLauncher tripId={rowData.tripId} />
                                </div>
                            );
                        }}
                        style={{ width: '5rem', textAlign: 'center' }}
                        headerClassName="bg-gray-100 font-semibold text-sm"
                    />
                    <Column
                        header={t('tripDetails.tripId')}
                        body={(rowData) => (
                            <p className="flex items-center  justify-center gap-2  whitespace-nowrap">
                                {rowData.tripId}
                            </p>
                        )}
                    />
                    <Column
                        body={(row) => (
                            <>
                                <p className="flex gap-1 whitespace-nowrap border-b border-gray-300">
                                    <Icon
                                        name="enteringGeoFence"
                                        className={`size-4 ${getPortIconColor(row.entryPort.type)}`}
                                    />
                                    {localized(row.entryPort.name)}
                                </p>
                                <p className="flex gap-1 whitespace-nowrap">
                                    <Icon
                                        name="leavingGeofence"
                                        className={`size-4 ${getPortIconColor(row.exitPort.type)}`}
                                    />
                                    {localized(row.exitPort.name)}
                                </p>
                            </>
                        )}
                        header={`${t('common.portIn')} - ${t('common.portOut')}`}
                        style={{ width: '15rem' }}
                    />
                    <Column
                        body={(rowData) => (
                            <p className="flex items-center  justify-center gap-2  whitespace-nowrap">
                                {localized(rowData.alertType.name)}
                            </p>
                        )}
                        header={t('common.alertType')}
                        bodyClassName="text-center"
                        headerClassName="bg-gray-100 font-semibold text-sm"
                    />
                    <Column
                        body={(rowData) => (
                            <>
                                <p className="flex items-center  justify-center gap-2  whitespace-nowrap">
                                    {formatLocalizedDate(rowData.fromState.trackerDateTime)}
                                </p>
                                <p className="flex items-center  justify-center gap-2  whitespace-nowrap">
                                    {rowData.toState ? formatLocalizedDate(rowData.toState.trackerDateTime) : '-'}
                                </p>
                            </>
                        )}
                        header={t('alert.timeStamp')}
                        bodyClassName="text-center"
                        headerClassName="bg-gray-100 font-semibold text-sm"
                    />

                    <Column
                        body={(rowData) => (
                            <div
                                onClick={(e) => {
                                    e.stopPropagation();
                                    navigate(
                                        `/trips/${rowData.tripId}/details?alertId=${rowData.id}&isOpen=true&view=map`,
                                    );
                                }}
                                title={t('tripActivities.clickToViewOnMap')}>
                                <p className="flex items-center justify-center gap-2 whitespace-nowrap cursor-pointer text-blue-600 hover:text-blue-800 hover:underline">
                                    ({rowData.fromState.lat} , {rowData.fromState.long})
                                </p>
                                <p className="flex items-center justify-center gap-2 whitespace-nowrap cursor-pointer text-blue-600 hover:text-blue-800 hover:underline">
                                    {rowData.toState ? `(${rowData.toState.lat} , ${rowData.toState.long})` : '-'}
                                </p>
                            </div>
                        )}
                        header={t('common.location')}
                        bodyClassName="text-center"
                        headerClassName="bg-gray-100 font-semibold text-sm"
                    />
                    <Column
                        body={(row) => {
                            return (
                                <>
                                    <p className="flex gap-1 whitespace-nowrap border-b border-gray-300">
                                        {row.fromState.routeZone
                                            ? t(`filter.${getZoneLabel(row.fromState.routeZone)}`)
                                            : '-'}
                                    </p>
                                    <p className="flex gap-1 whitespace-nowrap">
                                        {row.toState?.routeZone
                                            ? t(`filter.${getZoneLabel(row.toState.routeZone)}`)
                                            : '-'}
                                    </p>
                                </>
                            );
                        }}
                        header={t('common.latestInformationAboutTheTarget')}
                        style={{ width: '10rem' }}
                    />
                    <Column
                        body={(rowData) => (
                            <>
                                <p className="flex items-center  justify-center gap-2  whitespace-nowrap">
                                    {rowData.fromState.currentSpeed}
                                </p>
                                <p className="flex items-center  justify-center gap-2  whitespace-nowrap">
                                    {rowData.toState ? `${rowData.toState.currentSpeed}` : '-'}
                                </p>
                            </>
                        )}
                        header={t('common.speed')}
                        bodyClassName="text-center"
                        headerClassName="bg-gray-100 font-semibold text-sm"
                    />
                    <Column
                        body={(rowData) => (
                            <>
                                <p className="flex items-center  justify-center gap-2  whitespace-nowrap">
                                    {localized(rowData.fromState.address)}
                                </p>
                                <p className="flex items-center  justify-center gap-2  whitespace-nowrap">
                                    {rowData.toState ? `${localized(rowData.toState.address)}` : '-'}
                                </p>
                            </>
                        )}
                        header={t('common.address')}
                        bodyClassName="text-center"
                        headerClassName="bg-gray-100 font-semibold text-sm"
                    />
                    <Column
                        body={(rowData) => (
                            <>
                                <div className="flex gap-2 items-center justify-center">
                                    {rowData.fromState
                                        ? constructTripStates(rowData.fromState).map(
                                              ({ id, textColor, icon, tooltipKey, tooltip }) => (
                                                  <Tooltip key={id} tooltipMessage={tooltip ? tooltip : t(tooltipKey)}>
                                                      <Icon
                                                          name={icon}
                                                          className={`${textColor} cursor-pointer size-5`}
                                                      />
                                                  </Tooltip>
                                              ),
                                          )
                                        : '-'}
                                </div>
                                <div className="flex gap-2 items-center justify-center">
                                    {rowData.toState
                                        ? constructTripStates(rowData.toState).map(
                                              ({ id, textColor, icon, tooltipKey }) => (
                                                  <Tooltip key={id} tooltipMessage={t(tooltipKey)}>
                                                      <Icon
                                                          name={icon}
                                                          className={`${textColor} cursor-pointer size-5`}
                                                      />
                                                  </Tooltip>
                                              ),
                                          )
                                        : '-'}
                                </div>
                            </>
                        )}
                        header={t('common.state')}
                        bodyClassName="text-center"
                        headerClassName="bg-gray-100 font-semibold text-sm"
                    />
                </DataTable>
                {/* Scroll Loading */}
                {isLoading && (
                    <div className="relative bottom-[6rem] p-3 place-items-center bg-white">
                        <Loader isLoading={true} />
                    </div>
                )}
            </div>

            {/* Alert Drawer */}
            <AlertDrawer isOpen={drawerOpen} onClose={handleCloseDrawer} alert={selectedAlert} dir={dir} />
        </div>
    );
}
