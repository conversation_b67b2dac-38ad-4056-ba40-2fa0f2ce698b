import { useTranslation } from 'react-i18next';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { useEffect, useMemo, useRef } from 'react';

import { useInfiniteScroll } from '@/shared/hooks/use-infinite-scroll.hook';
import DraggableList from '@/components/common/ui/DraggableList';
import Tooltip from '@/components/common/ui/Tooltip';
import { useLocalized } from '@/shared/hooks/use-localized.hook';
import { useUsersStatsStore } from '@/stores/users-stats.store';
import { useUsersStore } from '@/stores/users.store';
import type { UserRole } from '@/infrastructure/api/users/types';

import { Icon } from '../common/ui/Icon';
import Loader from '../common/ui/Loader';
import UsersStatsCard, { type UsersStatsCardType } from '../features/users/UsersStatsCard';
import { Button } from '../common/ui/Button';

const PAGE_SIZE = 20;

export default function Alerts() {
    const { t, i18n } = useTranslation();
    const { dir } = useLocalized();

    // Store selectors
    const usersStats = useUsersStatsStore((s) => s.stats);
    const loadUsersStats = useUsersStatsStore((s) => s.loadUsersStats);

    const users = useUsersStore((s) => s.users);
    const pagination = useUsersStore((s) => s.pagination);
    const isLoading = useUsersStore((s) => s.isLoading);
    const loadUsers = useUsersStore((s) => s.loadUsers);
    const loadMoreUsers = useUsersStore((s) => s.loadMoreUsers);

    // Create refs to avoid function dependency issues
    const loadUsersRef = useRef(loadUsers);
    const loadMoreUsersRef = useRef(loadMoreUsers);

    // Update refs when functions change
    loadUsersRef.current = loadUsers;
    loadMoreUsersRef.current = loadMoreUsers;

    useEffect(() => {
        loadUsersRef.current({ pageSize: PAGE_SIZE });
        loadUsersStats();
    }, [loadUsersRef, loadUsersStats]);

    const defaultOrder: UsersStatsCardType[] = useMemo(
        () => ['totalUsers', 'totalActiveUsers', 'totalInActiveUsers'],
        [],
    );

    //---  Attach infinite scroll hook
    const { loaderRef } = useInfiniteScroll({
        loading: isLoading,
        hasMore: pagination.hasNext,
        onLoadMore: () => {
            // const nextPage = Math.floor(alerts.length / PAGE_SIZE) + 1;
            const nextPage = pagination.currentPage + 1;
            loadMoreUsersRef.current({ pageNumber: nextPage, pageSize: PAGE_SIZE });
        },
    });

    // Actions cell (edit + delete)
    const actionsTemplate = () => {
        return (
            <div className="flex gap-1 items-center justify-center">
                {/* View */}
                <Tooltip tooltipMessage={t('users.showDetails')}>
                    <Button
                        variant="outline"
                        onClick={() => {}}
                        className="p-button-outlined bg-white border !border-green-600 rounded-sm px-3 py-2 m-1 text-green-700">
                        <Icon name="viewLog" className="h-4 w-4" />
                    </Button>
                </Tooltip>

                {/* Edit */}
                <Tooltip tooltipMessage={t('users.editUser')}>
                    <Button
                        variant="outline"
                        onClick={() => {}}
                        className="p-button-outlined bg-white border !border-green-600 rounded-sm px-3 py-2 m-1 text-green-700">
                        <Icon name="edit" className="h-4 w-4" />
                    </Button>
                </Tooltip>

                {/* Delete */}
                <Tooltip tooltipMessage={t('users.deleteUser')}>
                    <Button
                        variant="outline"
                        onClick={() => {}}
                        className="p-button-outlined bg-white border !border-red-600 rounded-sm px-3 py-2 m-1 text-red-700">
                        <Icon name="delete" className="h-4 w-4" />
                    </Button>
                </Tooltip>
            </div>
        );
    };

    return (
        <div className="flex flex-col pt-2">
            {/* Header */}
            <div className="flex items-center justify-between flex-wrap px-4 gap-1" dir={i18n.dir()}>
                <h1 className="text-xl font-bold py-0">{t('navbar.usersManagement')}</h1>
                <div className="flex flex-wrap gap-3">
                    <Button
                        variant="secondary"
                        className="p-button-outlined bg-green-700 !border-green-700 rounded-sm px-4 py-2 hover:bg-green-800 text-white"
                        onClick={() => {}}>
                        <Icon name="plus" className="h-6 w-6" />
                        {t('users.addUser')}
                    </Button>
                    <Button variant="outline" onClick={() => {}}>
                        <Icon name="dots" className="h-6 w-6" />
                    </Button>
                </div>
            </div>

            {/* Summary cards */}
            <DraggableList
                className="flex gap-2 flex-wrap py-2 px-2"
                items={defaultOrder}
                storageKey="tripsCardOrder"
                renderItem={(type) => (
                    <UsersStatsCard
                        type={type}
                        value={
                            type === 'totalUsers'
                                ? usersStats?.totalUsers || 0
                                : type === 'totalActiveUsers'
                                  ? usersStats?.totalActiveUsers || 0
                                  : usersStats?.totalInActiveUsers || 0
                        }
                    />
                )}
            />

            {/* DataTable */}
            <div className="_table_container flex-grow-1">
                <DataTable
                    id="usersTable"
                    key={i18n.language}
                    dataKey="id"
                    value={users}
                    dir={dir}
                    scrollable
                    scrollHeight="73vh"
                    tableStyle={{ minWidth: '50rem', marginBottom: '3.5rem' }}
                    className="shadow-md border border-gray-200 "
                    rowClassName={() => 'cursor-pointer hover:bg-gray-50'}
                    emptyMessage={isLoading ? t('loading') : t('common.noDataExist')}>
                    <Column
                        header={''}
                        body={(rowData, { rowIndex }) => {
                            const isLast = rowIndex === users.length - 3; //-- fire loading before end by 3 elements
                            return <div ref={isLast ? loaderRef : null}>{rowData.id}</div>;
                        }}
                        style={{ width: '5rem', textAlign: 'center' }}
                        headerClassName="bg-gray-100 font-semibold text-sm"
                    />
                    <Column
                        header={t('users.userName')}
                        body={(rowData) => (
                            <p className="flex items-center  justify-center gap-2  whitespace-nowrap">
                                {rowData.username}
                            </p>
                        )}
                    />
                    <Column
                        header={t('users.email')}
                        body={(rowData) => (
                            <p className="flex items-center  justify-center gap-2  whitespace-nowrap">
                                {rowData.email}
                            </p>
                        )}
                    />
                    <Column
                        header={t('users.phone')}
                        body={(rowData) => (
                            <p className="flex items-center  justify-center gap-2  whitespace-nowrap">
                                {rowData.phone}
                            </p>
                        )}
                    />
                    <Column
                        header={t('users.status')}
                        body={(rowData) => (
                            <p className="flex items-center  justify-center gap-2  whitespace-nowrap">
                                {rowData.isActive ? t('users.active') : t('users.inactive') || '-'}
                            </p>
                        )}
                    />

                    <Column
                        header={t('users.roles')}
                        body={(rowData) => (
                            <p className="flex items-center  justify-center gap-2  whitespace-nowrap">
                                {rowData.roles.length > 0
                                    ? (rowData.roles.map((role: UserRole) => role.name) || []).join(', ')
                                    : '-'}
                            </p>
                        )}
                    />

                    <Column header={t('users.actions')} body={actionsTemplate} />
                </DataTable>
                {/* Scroll Loading */}
                {isLoading && (
                    <div className="relative bottom-[6rem] p-3 place-items-center bg-white">
                        <Loader isLoading={true} />
                    </div>
                )}
            </div>
        </div>
    );
}
