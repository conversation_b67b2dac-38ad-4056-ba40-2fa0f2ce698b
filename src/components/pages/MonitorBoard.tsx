import { useMapPointLookups } from '@/stores/map-points.store';
import { usePortMapMarkers } from '@/stores/ports.store';
import { useTripLocationsLookups } from '@/stores/trip-location.store';

import MonitorMenu from '../features/monitor-board/MonitorMenu';
import { MonitorMap } from '../common/google-map/MonitorMap';

export default function MonitorBoard() {
    const mapPoints = useMapPointLookups();
    const ports = usePortMapMarkers();
    const tripLocations = useTripLocationsLookups();

    return <MonitorMap points={mapPoints} ports={ports} tripLocations={tripLocations} menu={<MonitorMenu />} />;
}
