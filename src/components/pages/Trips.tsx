import { useTranslation } from 'react-i18next';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { useState, useEffect, useRef } from 'react';
import { useLocation } from 'react-router-dom';

import DraggableList from '@/components/common/ui/DraggableList';
import Tooltip from '@/components/common/ui/Tooltip';
import { createDefaultFilters, useTripFiltersStore, type FiltersShape } from '@/stores/trip-filters.store';
import type { AlertDto, ELock, TripsRequest } from '@/infrastructure/api/trips/types';
import { useLocalized } from '@/shared/hooks/use-localized.hook';
import { useTripsStore } from '@/stores/trips.store';
import { constructTripStates, getPortIconColor } from '@/shared/utils/trips.utils';
import { alertIconMapping, getZoneLabel } from '@/shared/utils/alerts.utils';
import { useTripsStatsStore } from '@/stores/trips-stats.store';
import type { TripsStatsCardType } from '@/components/features/trips/TripsStatsCard';
import { tripsTableExcelMapper } from '@/mappers/tripsTableExcel.mapper';
import { useInfiniteScroll } from '@/shared/hooks/use-infinite-scroll.hook';

import TripFilterDialog from '../common/trip-filter-dialog/TripFilterDialog';
import { Icon } from '../common/ui/Icon';
import { ExportExcelButton } from '../common/ui/ExportExcelButton';
import FilterButton from '../common/ui/FilterButton';
import TripsStatsCard from '../features/trips/TripsStatsCard';
import TripsDetailsLauncher from '../features/trips/TripsDetailsLauncher';
import { Tag } from '../common/ui/Tag';
import Loader from '../common/ui/Loader';

const PAGE_SIZE = 50;

export default function Trips() {
    const { t } = useTranslation();
    const { t: tWarnings } = useTranslation(['warnings']);
    const { i18n } = useTranslation();
    const { localized, dir } = useLocalized();

    const [visible, setVisible] = useState(false);

    const trips = useTripsStore((s) => s.trips);
    const pagination = useTripsStore((s) => s.pagination);
    const isLoading = useTripsStore((s) => s.isLoading);
    const loadTrips = useTripsStore((s) => s.loadTrips);
    const loadMoreTrips = useTripsStore((s) => s.loadMoreTrips);

    const tripsStats = useTripsStatsStore((s) => s.tripsStats);
    const loadTripsStats = useTripsStatsStore((s) => s.loadTripsStats);

    const location = useLocation();
    const { setFilters } = useTripFiltersStore();

    // Create refs to avoid function dependency issues
    const loadTripsRef = useRef(loadTrips);
    const loadTripsStatsRef = useRef(loadTripsStats);
    const loadMoreTripsRef = useRef(loadMoreTrips);

    // Update refs when functions change
    loadTripsRef.current = loadTrips;
    loadTripsStatsRef.current = loadTripsStats;
    loadMoreTripsRef.current = loadMoreTrips;

    const applyFilter = () => setVisible(false);

    // Load trips stats on mount
    useEffect(() => {
        loadTripsStatsRef.current();
    }, []);

    // Apply URL query parameters to filters on load and load initial data
    useEffect(() => {
        const params = new URLSearchParams(location.search);

        const hasParams = Array.from(params.keys()).length > 0; // detect if there are any params

        if (!hasParams) {
            // If no params → don't overwrite saved filters
            return;
        }

        const initialFilters: FiltersShape = {
            ...createDefaultFilters(),
            driverName: params.get('driverName') ?? null,
            driverNationality: params.get('driverNationality') ?? null,
            driverMobileNo: params.get('driverMobileNo') ?? null,
            driverPassportNumber: params.get('driverPassportNumber') ?? null,
            plateNumber: params.get('plateNumber') ?? null,
            transitNumber: params.get('transitNumber') ? Number(params.get('transitNumber')) : null,
            trackerNumber: params.get('trackerNumber') ?? null,
        };

        setFilters(initialFilters);
        // Load trips with the initial filters instead of separate calls
        loadTripsRef.current({ ...initialFilters, pageSize: PAGE_SIZE });
    }, [location.search, setFilters]);

    // Subscribe to filters changes using proper state selector
    const filters = useTripFiltersStore((s) => s.filters);
    useEffect(() => {
        // Only load if filters have changed and we're not in the initial load
        if (filters && location.search === '') {
            loadTripsRef.current(filters as TripsRequest);
        }
    }, [filters, location.search]);

    //---  Attach infinite scroll hook
    const { loaderRef } = useInfiniteScroll({
        loading: isLoading,
        hasMore: pagination.hasNext,
        onLoadMore: () => {
            // const nextPage = Math.floor(trips.length / PAGE_SIZE) + 1;
            const nextPage = pagination.currentPage + 1;
            loadMoreTripsRef.current({ pageNumber: nextPage, pageSize: PAGE_SIZE });
        },
    });

    const defaultOrder: TripsStatsCardType[] = [
        'ActiveTripsWithActiveAlerts',
        'ActiveTripsWithoutActiveAlerts',
        'ActiveTrips',
        'InActiveTrips',
    ];
    const excelTables = tripsTableExcelMapper(trips, t, localized);
    return (
        <div className="flex flex-col pt-2">
            {/* Header */}
            <div className="flex items-center justify-between flex-wrap px-4 gap-1" dir={i18n.dir()}>
                <h1 className="text-xl font-bold py-0">{t('navbar.trips')}</h1>
                <div className="flex flex-wrap gap-3">
                    <ExportExcelButton
                        fileName={`trips_Report`}
                        sheetName="Trip Alerts Report"
                        tables={excelTables}
                        horizontal={true}
                        rtl={dir === 'rtl'}
                    />
                    <FilterButton onClick={() => setVisible(true)} />
                    <TripFilterDialog onApply={applyFilter} open={visible} onOpenChange={setVisible} />
                </div>
            </div>

            {/* Summary cards */}
            <DraggableList
                className="flex gap-2 flex-wrap py-2 px-2"
                items={defaultOrder}
                storageKey="tripsCardOrder"
                renderItem={(type) => (
                    <TripsStatsCard
                        type={type}
                        value={
                            type === 'ActiveTripsWithActiveAlerts'
                                ? tripsStats?.totalActiveTripsWithActiveAlerts || 0
                                : type === 'ActiveTripsWithoutActiveAlerts'
                                  ? tripsStats?.totalActiveTripsWithoutActiveAlerts || 0
                                  : type === 'ActiveTrips'
                                    ? tripsStats?.totalActiveTrips || 0
                                    : tripsStats?.totalInActiveTrips || 0
                        }
                    />
                )}
            />

            {/* DataTable */}
            <div className="_table_container flex-grow-1">
                <DataTable
                    key={i18n.language}
                    value={trips}
                    tableStyle={{ minWidth: '50rem', marginBottom: '3.5rem' }}
                    scrollable
                    scrollHeight="73vh"
                    dir={dir}
                    className="shadow-md border border-gray-200"
                    // virtualScrollerOptions={{
                    //     itemSize: 100,
                    //     showLoader: true,
                    //     loading: isLoading,
                    //     onLazyLoad: (event: VirtualScrollerLazyEvent) => {
                    //         const nextPage = Math.floor((event.first as number) / PAGE_SIZE) + 1;
                    //         loadMoreTripsRef.current({ pageNumber: nextPage, pageSize: PAGE_SIZE });
                    //     },
                    // }}
                    emptyMessage={isLoading ? t('loading') : t('common.noDataExist')}
                    // rowClassName={(rowData) => (rowData.currentState?.currentSpeed === 0 ? 'bg-red-200' : '')}>
                    rowClassName={(rowData) =>
                        rowData.currentState?.currentSpeed === 0 ? 'border border-s-3 border-s-red-600' : ''
                    }>
                    <Column
                        header={t('common.details')}
                        body={(row, { rowIndex }) => {
                            const isLast = rowIndex === trips.length - 3; //-- fire loading before end by 3 elements
                            return (
                                <div ref={isLast ? loaderRef : null}>
                                    <TripsDetailsLauncher tripId={row.id} />
                                </div>
                            );
                        }}
                        style={{ width: '5rem', textAlign: 'center' }}
                        headerClassName="bg-gray-100 font-semibold text-sm"
                    />
                    <Column
                        body={(row) => row.transitNumber}
                        header={t('common.transitNumber')}
                        style={{ width: '8rem', textAlign: 'center' }}
                    />
                    <Column
                        body={(row) => row.shipmentDescription}
                        header={t('common.shipmentDescription')}
                        style={{ width: '15rem' }}
                    />
                    <Column
                        body={(row) => (
                            <>
                                <p className="flex gap-1 whitespace-nowrap border-b border-gray-300">
                                    <Icon
                                        name="enteringGeoFence"
                                        className={`size-4 ${getPortIconColor(row.entryPort.type)}`}
                                    />
                                    {localized(row.entryPort.name)}
                                </p>
                                <p className="flex gap-1 whitespace-nowrap">
                                    <Icon
                                        name="leavingGeofence"
                                        className={`size-4 ${getPortIconColor(row.exitPort.type)}`}
                                    />
                                    {localized(row.exitPort.name)}
                                </p>
                            </>
                        )}
                        header={`${t('common.portIn')} - ${t('common.portOut')}`}
                        style={{ width: '15rem' }}
                    />
                    <Column
                        body={(row) => {
                            return row.currentState.routeZone
                                ? t(`filter.${getZoneLabel(row.currentState.routeZone)}`)
                                : '-';
                        }}
                        header={t('common.latestInformationAboutTheTarget')}
                        style={{ width: '10rem' }}
                    />
                    <Column
                        body={(row) => (
                            <>
                                <p className="border-b border-gray-300">{row.tracker.serialNumber}</p>
                                {row.eLocks?.map((e: ELock) => (
                                    <p key={e.serialNumber} className="text-xs text-gray-500">
                                        {e.serialNumber}
                                    </p>
                                ))}
                            </>
                        )}
                        header={t('common.tracker')}
                        style={{ width: '13rem' }}
                    />
                    <Column
                        body={(row) => (
                            <>
                                <p className="font-medium">{row.driver.name}</p>
                                <p className="text-xs text-gray-500">{row.driver.passportCountry}</p>
                            </>
                        )}
                        header={t('common.driver')}
                        style={{ width: '15rem' }}
                    />
                    <Column
                        body={(row) => {
                            const vehicle = row.vehicle || {};
                            return (
                                <p className="text-xs text-gray-600">
                                    {[vehicle.type, vehicle.model, vehicle.color, vehicle.plateNo]
                                        .filter(Boolean)
                                        .join(', ')}
                                </p>
                            );
                        }}
                        header={t('common.truck')}
                        style={{ width: '14rem' }}
                    />
                    <Column
                        body={(row) => (
                            <div className="flex gap-2 items-center justify-center">
                                {row.activeAlerts?.length ? (
                                    row.activeAlerts.map((alert: AlertDto) => (
                                        <Tooltip key={alert.id} tooltipMessage={localized(alert.type.name)}>
                                            <Icon
                                                name={alertIconMapping[alert.type.id] || 'alert'}
                                                className="cursor-pointer size-5 text-red-500"
                                            />
                                        </Tooltip>
                                    ))
                                ) : (
                                    <span className="px-2 py-1 rounded-lg text-xs bg-green-100 text-green-600">
                                        {tWarnings('noWarnings')}
                                    </span>
                                )}
                            </div>
                        )}
                        header={t('common.warnings')}
                        style={{ width: '10rem', textAlign: 'center' }}
                    />
                    <Column
                        body={(row) => (
                            <div className="flex gap-2 items-center justify-center">
                                {row.currentState
                                    ? constructTripStates(row.currentState).map(
                                          ({ id, textColor, icon, tooltipKey, tooltip }) => (
                                              <Tooltip key={id} tooltipMessage={tooltip ?? t(tooltipKey)}>
                                                  <Icon name={icon} className={`${textColor} cursor-pointer size-5`} />
                                              </Tooltip>
                                          ),
                                      )
                                    : '-'}
                            </div>
                        )}
                        header={t('common.state')}
                        style={{ width: '10rem', textAlign: 'center' }}
                    />
                    {/* Trip Tags Column */}
                    <Column
                        header={t('common.tags')}
                        style={{ width: '15rem' }}
                        body={(row) => (
                            <div className="flex flex-wrap gap-2 justify-center">
                                {/* Moving / Stopped */}
                                {row.currentState && (
                                    <Tag
                                        bgColor={row.currentState.currentSpeed > 0 ? '' : 'bg-[#179FCA1A]'}
                                        textColor={row.currentState.currentSpeed > 0 ? '' : 'text-[#179FCA]'}
                                        className="inline-flex max-w-max cursor-pointer focus:outline-none">
                                        {row.currentState.currentSpeed > 0 ? '' : t('tripDetails.stopped')}
                                    </Tag>
                                )}

                                {/* Arrival Tracking / Trip is Focused */}
                                {row.isFocused && (
                                    <Tag
                                        bgColor={'bg-[#F978161A]'}
                                        textColor={'text-[#F97816]'}
                                        className="inline-flex max-w-max cursor-pointer focus:outline-none">
                                        {t('tripDetails.arrivalTracking')}
                                    </Tag>
                                )}

                                {/* Suspected Trip */}
                                {row.isSuspicious && (
                                    <Tag
                                        bgColor={'bg-[#FCE4E4]'}
                                        textColor={'text-[#E4514F]'}
                                        className="inline-flex max-w-max cursor-pointer focus:outline-none">
                                        {t('tripDetails.suspectedTrip')}
                                    </Tag>
                                )}
                            </div>
                        )}
                    />
                </DataTable>
                {/* Scroll Loading */}
                {isLoading && (
                    <div className="relative bottom-[6rem] p-3 place-items-center bg-white">
                        <Loader isLoading={true} />
                    </div>
                )}
            </div>
        </div>
    );
}
