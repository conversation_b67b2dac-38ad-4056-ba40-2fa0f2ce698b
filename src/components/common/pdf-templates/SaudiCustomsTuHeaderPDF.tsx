import { View, Image, StyleSheet } from '@react-pdf/renderer';

import SaudiCustomsLogo from '@imgs/saudi-customs-logo.png';
import TuLogo from '@imgs/tu-logo.png';

const styles = StyleSheet.create({
    container: {
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        width: '100%',
        marginBottom: 10,
    },
    imageMain: {
        height: 80,
        width: 'auto',
        objectFit: 'contain',
        marginBottom: 6,
    },
    imageSub: {
        height: 50,
        width: 'auto',
        objectFit: 'contain',
        marginBottom: 8,
    },
    hr: {
        width: '100%',
        height: 1,
        backgroundColor: '#000',
        marginTop: 8,
        marginBottom: 4,
    },
});

export const SaudiCustomsTuHeaderPDF = () => (
    <View style={styles.container}>
        <Image src={SaudiCustomsLogo} style={styles.imageMain} />
        <Image src={TuLogo} style={styles.imageSub} />
        <View style={styles.hr} />
    </View>
);
