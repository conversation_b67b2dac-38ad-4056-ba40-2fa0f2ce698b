// src/components/common/pdf-templates/ActivitiesSectionPDF.tsx
import { Text, View, StyleSheet } from '@react-pdf/renderer';
import { useTranslation } from 'react-i18next';
import { t } from 'i18next';

import type { TripActivity } from '@/infrastructure/api/trip-activities/types';
import { useLocalized } from '@/shared/hooks/use-localized.hook';
import { formatLocalizedDate } from '@/shared/utils/format-date.utils';

interface ActivitiesSectionPDFProps {
    activities: TripActivity[];
    columns?: {
        field: 'origin' | 'createdAt' | 'status' | 'action' | 'details' | 'location';
        label?: string;
        width?: string | number;
    }[];
    rowsPerPage?: number;
}

export const ActivitiesSectionPDF = ({
    activities = [],
    columns = [
        { field: 'origin', label: t('tripActivities.employee'), width: '15%' },
        { field: 'createdAt', label: t('tripActivities.date'), width: '15%' },
        { field: 'status', label: t('tripActivities.status'), width: '15%' },
        { field: 'action', label: t('tripActivities.action'), width: '15%' },
        { field: 'details', label: t('tripActivities.details'), width: '25%' },
        { field: 'location', label: t('tripActivities.location'), width: '15%' },
    ],
    rowsPerPage = 20,
}: ActivitiesSectionPDFProps) => {
    const { t } = useTranslation();
    const { localized, dir } = useLocalized();
    const isRTL = dir === 'rtl';

    const styles = StyleSheet.create({
        container: {
            marginTop: 10,
            padding: 4,
            borderWidth: 1,
            borderColor: '#ddd',
            borderRadius: 2,
            backgroundColor: '#fff',
            fontFamily: 'Cairo',
        },
        title: {
            fontSize: 14,
            fontWeight: 'bold',
            marginBottom: 4,
            textAlign: 'center',
        },
        headerRow: {
            flexDirection: isRTL ? 'row-reverse' : 'row',
            backgroundColor: '#f6f6f6',
            borderWidth: 1,
            borderColor: '#e6e6e6',
        },
        dataRow: {
            flexDirection: isRTL ? 'row-reverse' : 'row',
            borderLeftWidth: 1,
            borderRightWidth: 1,
            borderBottomWidth: 1,
            borderColor: '#e6e6e6',
        },
        headerCell: {
            paddingVertical: 3,
            paddingHorizontal: 4,
            fontSize: 9,
            fontWeight: 'bold',
            textAlign: isRTL ? 'right' : 'left',
        },
        cell: {
            paddingVertical: 3,
            paddingHorizontal: 4,
            fontSize: 9,
            color: '#333',
            textAlign: isRTL ? 'right' : 'left',
        },
        emptyText: {
            textAlign: 'center',
            fontSize: 10,
            color: '#666',
            marginVertical: 6,
        },
        headerSection: {
            flexDirection: dir === 'rtl' ? 'row-reverse' : 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: 6,
        },
        pageNumber: {
            fontSize: 8,
            color: '#777',
            textAlign: 'right',
        },
    });

    if (!activities.length) {
        return (
            <View style={styles.container}>
                <Text style={styles.title}>{t('tripActivities.title', 'Activities Report')}</Text>
                <Text style={styles.emptyText}>{t('common.noDataExist', 'No activities found')}</Text>
            </View>
        );
    }

    const renderCellValue = (activity: TripActivity, field: string) => {
        switch (field) {
            case 'origin':
                return activity.origin || '—';
            case 'createdAt':
                return formatLocalizedDate(activity.createdAt) || '—';
            case 'status':
                return localized(activity.status?.name) || '—';
            case 'action':
                return localized(activity.action?.name) || '—';
            case 'details':
                return activity.details || '—';
            case 'location':
                return activity.location ? `${activity.location.latitude}, ${activity.location.longitude}` : '—';
            default:
                return '—';
        }
    };

    const chunkedActivities = [];
    for (let i = 0; i < activities.length; i += rowsPerPage) {
        chunkedActivities.push(activities.slice(i, i + rowsPerPage));
    }

    return (
        <>
            {chunkedActivities.map((pageActivities, pageIndex) => (
                <View key={pageIndex} style={styles.container} break={pageIndex > 0}>
                    <View style={styles.headerSection}>
                        <Text style={styles.title}>
                            {pageIndex > 0 ? `${t('common.continue')}  - ` : ''}
                            {t('tripActivities.title')}
                        </Text>
                        <Text style={styles.pageNumber}>
                            {t('common.page', 'Page')} {pageIndex + 1} {t('common.of', 'of')} {chunkedActivities.length}
                        </Text>
                    </View>

                    {/* Table Header */}
                    <View style={styles.headerRow}>
                        {columns.map((col, idx) => (
                            <Text
                                key={idx}
                                style={[
                                    styles.headerCell,
                                    {
                                        width: col.width || '16%',
                                        flexGrow: typeof col.width === 'number' ? col.width : 0,
                                    },
                                ]}>
                                {t(col.label || col.field)}
                            </Text>
                        ))}
                    </View>

                    {pageActivities.map((activity, index) => (
                        <View key={activity.id ?? index} style={styles.dataRow}>
                            {columns.map((col, idx) => (
                                <Text
                                    key={idx}
                                    style={[
                                        styles.cell,
                                        {
                                            width: col.width || '16%',
                                            flexGrow: typeof col.width === 'number' ? col.width : 0,
                                        },
                                    ]}>
                                    {renderCellValue(activity, col.field)}
                                </Text>
                            ))}
                        </View>
                    ))}
                </View>
            ))}
        </>
    );
};
