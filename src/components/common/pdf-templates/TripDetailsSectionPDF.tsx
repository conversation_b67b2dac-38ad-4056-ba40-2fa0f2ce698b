// src/components/common/pdf-templates/TripDetailsSectionPDF.tsx
import { Text, View, StyleSheet } from '@react-pdf/renderer';
import { useTranslation } from 'react-i18next';

import { useLocalized } from '@/shared/hooks/use-localized.hook';
import { formatLocalizedDate } from '@/shared/utils/format-date.utils';
import type { TripDetail } from '@/infrastructure/api/trips/types';

interface TripDetailsSectionPDFProps {
    tripDetail: TripDetail | null;
}

/* Utility */
const fmt = (v?: string | number | null) => (v !== undefined && v !== null && String(v) !== '' ? String(v) : 'N/A');

export const TripDetailsSectionPDF = ({ tripDetail }: TripDetailsSectionPDFProps) => {
    const { t } = useTranslation();
    const { localized, dir } = useLocalized();
    const isRTL = dir === 'rtl';

    const styles = StyleSheet.create({
        container: {
            marginBottom: 4,
            padding: 4,
            borderWidth: 1,
            borderColor: '#ddd',
            borderRadius: 2,
            backgroundColor: '#fff',
            fontFamily: 'Cairo',
        },
        title: {
            fontSize: 14,
            fontWeight: 'bold',
            marginBottom: 3,
            textAlign: 'center',
            fontFamily: 'Cairo',
        },
        row: {
            flexDirection: dir === 'rtl' ? 'row-reverse' : 'row',
            alignItems: 'flex-start',
            marginBottom: 2,
            minHeight: 18,
        },
        labelCell: {
            width: '20%',
            paddingVertical: 2,
            paddingRight: 3,
            paddingLeft: 3,
            backgroundColor: '#f6f6f6',
            borderWidth: 1,
            borderColor: '#e6e6e6',
            fontSize: 9,
            fontWeight: 'bold',
            fontFamily: 'Cairo',
        },
        valueCell: {
            width: '30%',
            paddingVertical: 2,
            paddingRight: 3,
            paddingLeft: 3,
            borderWidth: 1,
            borderColor: '#e6e6e6',
            fontSize: 9,
            color: '#333',
            fontFamily: 'Cairo',
        },
        fullRow: {
            flexDirection: dir === 'rtl' ? 'row-reverse' : 'row',
            marginTop: 3,
        },
        fullLabel: {
            width: '20%',
            paddingVertical: 2,
            paddingRight: 3,
            paddingLeft: 3,
            backgroundColor: '#f6f6f6',
            borderWidth: 1,
            borderColor: '#e6e6e6',
            fontFamily: 'Cairo',
            fontSize: 9,
            fontWeight: 'bold',
        },
        fullValue: {
            width: '80%',
            paddingVertical: 2,
            paddingRight: 3,
            paddingLeft: 3,
            borderWidth: 1,
            borderColor: '#e6e6e6',
            fontSize: 9,
            color: '#333',
            fontFamily: 'Cairo',
        },
        sectionSeparator: {
            height: 8,
        },
        vehicleTitle: {
            fontSize: 13,
            fontWeight: 'bold',
            marginTop: 4,
            marginBottom: 3,
            textAlign: 'center',
            fontFamily: 'Cairo',
        },
    });

    if (!tripDetail) {
        return (
            <View style={styles.container}>
                <Text style={styles.title}>{t('common.tripDetails')}</Text>
                <Text style={{ textAlign: 'center', fontSize: 10, color: '#666', fontFamily: 'Cairo' }}>
                    {t('common.noTripDetails')}
                </Text>
            </View>
        );
    }

    const formatDate = (d?: string | null) => {
        if (!d) return 'N/A';
        try {
            return formatLocalizedDate(d);
        } catch {
            return fmt(d);
        }
    };

    const formatPort = (p?: { english: string; arabic: string } | null) => (p ? localized(p) : 'N/A');

    const formatElocks = () =>
        tripDetail.eLocks && tripDetail.eLocks.length > 0 ? tripDetail.eLocks.map((e) => e.deviceId).join(', ') : 'N/A';

    const tripStatusLabel = tripDetail.status === 'Active' ? t('tripDetails.open') : t('tripDetails.closed');

    /* Render a logical 4-col row */
    const renderFourCols = (
        label1: string,
        value1: string | number | null | undefined,
        label2?: string,
        value2?: string | number | null | undefined,
    ) => {
        const textAlign = isRTL ? 'right' : 'left';

        if (!isRTL) {
            // LTR
            return (
                <View style={styles.row} wrap={false}>
                    <Text style={[styles.labelCell, { textAlign }]}>{label1}</Text>
                    <Text style={[styles.valueCell, { textAlign }]}>{fmt(value1)}</Text>

                    {label2 ? (
                        <>
                            <Text style={[styles.labelCell, { textAlign }]}>{label2}</Text>
                            <Text style={[styles.valueCell, { textAlign }]}>{fmt(value2)}</Text>
                        </>
                    ) : (
                        <>
                            <Text
                                style={[
                                    styles.labelCell,
                                    {
                                        backgroundColor: 'transparent',
                                        borderColor: 'transparent',
                                    },
                                ]}
                            />
                            <Text
                                style={[
                                    styles.valueCell,
                                    {
                                        backgroundColor: 'transparent',
                                        borderColor: 'transparent',
                                    },
                                ]}
                            />
                        </>
                    )}
                </View>
            );
        } else {
            // RTL
            return (
                <View style={[styles.row, { flexDirection: dir === 'rtl' ? 'row' : 'row-reverse' }]} wrap={false}>
                    {label2 ? (
                        <>
                            <Text style={[styles.valueCell, { textAlign }]}>{fmt(value2)}</Text>
                            <Text style={[styles.labelCell, { textAlign }]}>{label2}</Text>
                        </>
                    ) : (
                        <>
                            <Text
                                style={[
                                    styles.valueCell,
                                    {
                                        backgroundColor: 'transparent',
                                        borderColor: 'transparent',
                                    },
                                ]}
                            />
                            <Text
                                style={[
                                    styles.labelCell,
                                    {
                                        backgroundColor: 'transparent',
                                        borderColor: 'transparent',
                                    },
                                ]}
                            />
                        </>
                    )}

                    <Text style={[styles.valueCell, { textAlign }]}>{fmt(value1)}</Text>
                    <Text style={[styles.labelCell, { textAlign }]}>{label1}</Text>
                </View>
            );
        }
    };

    return (
        <View style={styles.container}>
            <Text style={[styles.title, { fontFamily: 'Cairo' }]}>{t('common.tripDetails')}</Text>

            {renderFourCols(
                t('tripDetails.transitType'),
                tripDetail.transitTypeName,
                t('tripDetails.transitNumber'),
                tripDetail.transitNumber,
            )}
            {renderFourCols(
                t('tripDetails.movementNumber'),
                tripDetail.transSeq,
                t('tripDetails.tripCode'),
                tripDetail.id,
            )}
            {renderFourCols(
                t('tripDetails.shipmentDescription'),
                tripDetail.shipmentDescription || tripDetail.newShipmentDescription,
                t('tripDetails.owner'),
                tripDetail.ownerType || tripDetail.ownerDesc,
            )}
            {renderFourCols(
                t('tripDetails.entryPort'),
                formatPort(tripDetail.entryPort?.name),
                t('tripDetails.tripStatus'),
                tripStatusLabel,
            )}
            {renderFourCols(
                t('tripDetails.startDate'),
                formatDate(tripDetail.startDate),
                t('tripDetails.exitPort'),
                formatPort(tripDetail.exitPort?.name),
            )}
            {renderFourCols(
                t('tripDetails.endDate'),
                formatDate(tripDetail.endDate),
                t('tripDetails.transitDate'),
                formatDate(tripDetail.transitDate),
            )}
            {renderFourCols(
                t('tripDetails.elocks'),
                formatElocks(),
                t('tripDetails.trackingDevice'),
                tripDetail.tracker?.serialNumber,
            )}

            {/* Notes full-width */}
            <View style={styles.fullRow}>
                <Text style={[styles.fullLabel, { textAlign: isRTL ? 'right' : 'left' }]}>
                    {t('tripDetails.notes')}
                </Text>
                <Text style={[styles.fullValue, { textAlign: isRTL ? 'right' : 'left' }]}>
                    {fmt(tripDetail.securityNotes)}
                </Text>
            </View>

            <View style={styles.sectionSeparator} />

            <Text style={styles.vehicleTitle}>{t('tripDetails.vehicleDetails')}</Text>

            {renderFourCols(
                t('tripDetails.driverName'),
                tripDetail.driver?.name,
                t('tripDetails.vehiclePlateNumber'),
                tripDetail.vehicle?.plateNo,
            )}
            {renderFourCols(
                t('tripDetails.driverContactNo'),
                tripDetail.driver?.mobileNo,
                t('tripDetails.completeDistance'),
                tripDetail.currentState?.completeDistanceInMeters,
            )}
            {renderFourCols(
                t('tripDetails.driverPassportNumber'),
                tripDetail.driver?.passportNumber,
                t('tripDetails.remainingDistance'),
                tripDetail.currentState?.remainingDistanceInMeters,
            )}
            {/* {renderFourCols(t('tripDetails.driverNationality'), tripDetail.driver?.passportCountry, '', '')} */}
            {/* Driver Nationality full-width */}
            <View style={styles.fullRow}>
                <Text style={[styles.fullLabel, { textAlign: isRTL ? 'right' : 'left' }]}>
                    {t('tripDetails.driverNationality')}
                </Text>
                <Text style={[styles.fullValue, { textAlign: isRTL ? 'right' : 'left' }]}>
                    {fmt(tripDetail.driver?.passportCountry)}
                </Text>
            </View>
        </View>
    );
};
