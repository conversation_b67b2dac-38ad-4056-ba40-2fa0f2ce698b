// src/components/common/pdf-templates/StopsSectionPDF.tsx
import { Text, View, StyleSheet } from '@react-pdf/renderer';
import { useTranslation } from 'react-i18next';
import { t } from 'i18next';

import type { TripStop } from '@/infrastructure/api/trip-stops/types';
import { useLocalized } from '@/shared/hooks/use-localized.hook';
import { formatLocalizedDate } from '@/shared/utils/format-date.utils';

interface StopsSectionPDFProps {
    stops: TripStop[];
    columns?: {
        field: 'address' | 'fromTime' | 'toTime' | 'duration';
        label?: string;
        width?: string | number;
    }[];
    rowsPerPage?: number; // Maximum number of rows per page
}

export const StopsSectionPDF = ({
    stops = [],
    columns = [
        { field: 'address', label: t('stops.placeName'), width: '50%' },
        { field: 'fromTime', label: t('stops.from'), width: '20%' },
        { field: 'toTime', label: t('stops.to'), width: '20%' },
        { field: 'duration', label: t('stops.duration'), width: '10%' },
    ],
    rowsPerPage = 18,
}: StopsSectionPDFProps) => {
    const { t } = useTranslation();
    const { localized, dir } = useLocalized();
    const isRTL = dir === 'rtl';

    // Define PDF styles
    const styles = StyleSheet.create({
        container: {
            marginTop: 10,
            padding: 4,
            borderWidth: 1,
            borderColor: '#ddd',
            borderRadius: 2,
            backgroundColor: '#fff',
            fontFamily: 'Cairo',
        },
        title: {
            fontSize: 14,
            fontWeight: 'bold',
            marginBottom: 6,
            textAlign: 'center',
        },
        headerRow: {
            flexDirection: isRTL ? 'row-reverse' : 'row',
            backgroundColor: '#f6f6f6',
            borderWidth: 1,
            borderColor: '#e6e6e6',
        },
        dataRow: {
            flexDirection: isRTL ? 'row-reverse' : 'row',
            borderLeftWidth: 1,
            borderRightWidth: 1,
            borderBottomWidth: 1,
            borderColor: '#e6e6e6',
        },
        headerCell: {
            paddingVertical: 3,
            paddingHorizontal: 4,
            fontSize: 9,
            fontWeight: 'bold',
            textAlign: isRTL ? 'right' : 'left',
        },
        cell: {
            paddingVertical: 3,
            paddingHorizontal: 4,
            fontSize: 9,
            color: '#333',
            textAlign: isRTL ? 'right' : 'left',
        },
        emptyText: {
            textAlign: 'center',
            fontSize: 10,
            color: '#666',
            marginVertical: 6,
        },
        headerSection: {
            flexDirection: dir === 'rtl' ? 'row-reverse' : 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: 6,
        },
        pageNumber: {
            fontSize: 8,
            color: '#777',
            textAlign: 'right',
        },
    });

    // Handle empty stops
    if (!stops.length) {
        return (
            <View style={styles.container}>
                <Text style={styles.title}>{t('stops.title')}</Text>
                <Text style={styles.emptyText}>{t('common.noDataExist', 'No stops found')}</Text>
            </View>
        );
    }

    // Helper function to render each cell value based on the field
    const renderCellValue = (stop: TripStop, field: string) => {
        switch (field) {
            case 'address':
                return localized(stop.address) || '—';
            case 'fromTime':
                return formatLocalizedDate(stop.fromTime) || '—';
            case 'toTime':
                return formatLocalizedDate(stop.toTime) || '—';
            case 'duration':
                return `${stop.period?.hours || 0}h ${stop.period?.minutes || 0}m`;
            default:
                return '—';
        }
    };

    // Split the stops into chunks based on rowsPerPage
    const chunkedStops = [];
    for (let i = 0; i < stops.length; i += rowsPerPage) {
        chunkedStops.push(stops.slice(i, i + rowsPerPage));
    }

    // Render paginated stop tables
    return (
        <>
            {chunkedStops.map((pageStops, pageIndex) => (
                <View
                    key={pageIndex}
                    style={styles.container}
                    break={pageIndex > 0} // Start a new page after the first one
                >
                    <View style={styles.headerSection}>
                        <Text style={styles.title}>
                            {pageIndex > 0 ? `${t('common.continue')}  - ` : ''}
                            {t('stops.title', 'Stops Report')}
                        </Text>
                        <Text style={styles.pageNumber}>
                            {t('common.page', 'Page')} {pageIndex + 1} {t('common.of', 'of')} {chunkedStops.length}
                        </Text>
                    </View>

                    {/* Table Header */}
                    <View style={styles.headerRow}>
                        {columns.map((col, idx) => (
                            <Text
                                key={idx}
                                style={[
                                    styles.headerCell,
                                    {
                                        width: col.width || '25%',
                                        flexGrow: typeof col.width === 'number' ? col.width : 0,
                                    },
                                ]}>
                                {t(col.label || col.field)}
                            </Text>
                        ))}
                    </View>

                    {/* Table Rows */}
                    {pageStops.map((stop, index) => (
                        <View key={stop.id ?? index} style={styles.dataRow}>
                            {columns.map((col, idx) => (
                                <Text
                                    key={idx}
                                    style={[
                                        styles.cell,
                                        {
                                            width: col.width || '25%',
                                            flexGrow: typeof col.width === 'number' ? col.width : 0,
                                        },
                                    ]}>
                                    {renderCellValue(stop, col.field)}
                                </Text>
                            ))}
                        </View>
                    ))}
                </View>
            ))}
        </>
    );
};
