// src/components/common/pdf-templates/AlertsSectionPDF.tsx
import { Text, View, StyleSheet } from '@react-pdf/renderer';
import { useTranslation } from 'react-i18next';

import type { TripAlert } from '@/infrastructure/api/trip-alerts/types';
import { useLocalized } from '@/shared/hooks/use-localized.hook';
import { formatLocalizedDate } from '@/shared/utils/format-date.utils';

interface AlertsSectionPDFProps {
    alerts: TripAlert[];
    columns?: {
        field: 'alertType' | 'address' | 'location' | 'timestamp' | 'speed';
        label?: string;
        width?: string | number;
    }[];
    rowsPerPage?: number; // Maximum number of rows per page
}

export const AlertsSectionPDF = ({
    alerts = [],
    columns = [
        { field: 'alertType', label: 'common.alertType', width: '20%' },
        { field: 'address', label: 'common.address', width: '25%' },
        { field: 'location', label: 'common.location', width: '20%' },
        { field: 'timestamp', label: 'common.timestamp', width: '20%' },
        { field: 'speed', label: 'common.speed', width: '15%' },
    ],
    rowsPerPage = 20,
}: AlertsSectionPDFProps) => {
    const { t } = useTranslation();
    const { localized, dir } = useLocalized();
    const isRTL = dir === 'rtl';

    const styles = StyleSheet.create({
        container: {
            marginTop: 8,
            padding: 4,
            borderWidth: 1,
            borderColor: '#ddd',
            borderRadius: 2,
            backgroundColor: '#fff',
            fontFamily: 'Cairo',
        },
        title: {
            fontSize: 14,
            fontWeight: 'bold',
            marginBottom: 6,
            textAlign: 'center',
        },
        headerRow: {
            flexDirection: isRTL ? 'row-reverse' : 'row',
            backgroundColor: '#f6f6f6',
            borderWidth: 1,
            borderColor: '#e6e6e6',
        },
        dataRow: {
            flexDirection: isRTL ? 'row-reverse' : 'row',
            borderLeftWidth: 1,
            borderRightWidth: 1,
            borderBottomWidth: 1,
            borderColor: '#e6e6e6',
        },
        headerCell: {
            paddingVertical: 3,
            paddingHorizontal: 4,
            fontSize: 9,
            fontWeight: 'bold',
            textAlign: isRTL ? 'right' : 'left',
        },
        cell: {
            paddingVertical: 3,
            paddingHorizontal: 4,
            fontSize: 9,
            color: '#333',
            textAlign: isRTL ? 'right' : 'left',
        },
        emptyText: {
            textAlign: 'center',
            fontSize: 10,
            color: '#666',
            marginVertical: 6,
        },
        headerSection: {
            flexDirection: dir === 'rtl' ? 'row-reverse' : 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: 6,
        },
        pageNumber: {
            fontSize: 8,
            color: '#777',
            textAlign: 'right',
        },
    });

    if (!alerts.length) {
        return (
            <View style={styles.container}>
                <Text style={styles.title}>{t('tripDetails.alerts')}</Text>
                <Text style={styles.emptyText}>{t('common.noWarnings')}</Text>
            </View>
        );
    }

    const renderCellValue = (alert: TripAlert, field: string) => {
        switch (field) {
            case 'alertType':
                return localized(alert.alertType?.name) || '—';
            case 'address':
                return localized(alert.fromState?.address) || '—';
            case 'location':
                return alert.fromState?.lat && alert.fromState?.long
                    ? `${alert.fromState.lat}, ${alert.fromState.long}`
                    : '—';
            case 'timestamp':
                return formatLocalizedDate(alert.fromState?.trackerDateTime) || '—';
            case 'speed':
                return alert.fromState?.currentSpeed ? `${alert.fromState.currentSpeed} km/h` : '—';
            default:
                return '—';
        }
    };

    // Split alerts into pages
    const chunkedAlerts = [];
    for (let i = 0; i < alerts.length; i += rowsPerPage) {
        chunkedAlerts.push(alerts.slice(i, i + rowsPerPage));
    }

    return (
        <>
            {chunkedAlerts.map((pageAlerts, pageIndex) => (
                <View key={pageIndex} style={styles.container} break={pageIndex > 0}>
                    <View style={styles.headerSection}>
                        <Text style={styles.title}>
                            {pageIndex > 0 ? `${t('common.continue')}  - ` : ''}
                            {t('tripDetails.alerts')}
                        </Text>
                        <Text style={styles.pageNumber}>
                            {t('common.page', 'Page')} {pageIndex + 1} {t('common.of', 'of')} {chunkedAlerts.length}
                        </Text>
                    </View>

                    {/* Header */}
                    <View style={styles.headerRow}>
                        {columns.map((col, idx) => (
                            <Text key={idx} style={[styles.headerCell, { width: col.width || '20%' }]}>
                                {t(col.label || col.field)}
                            </Text>
                        ))}
                    </View>

                    {/* Data Rows */}
                    {pageAlerts.map((alert, idx) => (
                        <View key={alert.id ?? idx} style={styles.dataRow}>
                            {columns.map((col, i) => (
                                <Text key={i} style={[styles.cell, { width: col.width || '20%' }]}>
                                    {renderCellValue(alert, col.field)}
                                </Text>
                            ))}
                        </View>
                    ))}
                </View>
            ))}
        </>
    );
};
