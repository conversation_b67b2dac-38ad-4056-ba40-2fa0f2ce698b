import { forwardRef } from 'react';
import { useTranslation } from 'react-i18next';

import type { TripAlert } from '@/infrastructure/api/trip-alerts/types';
import type { TripDetail } from '@/infrastructure/api/trips/types';

import { SaudiCustomsTuHeader } from './SaudiCustomsTuHeader';
import { TripDetailsSection } from './TripDetailsSection';
import { AlertsSection } from './AlertsSection';

type Props = {
    details: TripDetail | null;
    alerts: TripAlert[];
};

const AlertReportPrintable = forwardRef<HTMLDivElement, Props>(({ details, alerts }, ref) => {
    // Get the current language from the i18n instance
    const { i18n } = useTranslation();
    // Determine direction based on language
    const dir = i18n.language === 'ar' ? 'rtl' : 'ltr'; // Use 'rtl' for Arabic, otherwise 'ltr'

    return (
        <div ref={ref} dir={dir} className="print:bg-white">
            {/* PAGE */}
            <div
                className="mx-auto bg-white text-black"
                // style={{
                //     width: '250mm', // Increase width from 210mm to 250mm
                //     minHeight: '297mm',
                //     padding: '12mm',
                //     boxSizing: 'border-box',
                // }}
            >
                <SaudiCustomsTuHeader />

                {/* Trip details (first) */}
                {details && <TripDetailsSection tripDetail={details} />}
                <div className="print:break-before-page"></div>

                {/* Alerts table */}
                <AlertsSection alerts={alerts} />
            </div>
        </div>
    );
});

AlertReportPrintable.displayName = 'AlertReportPrintable';
export default AlertReportPrintable;
