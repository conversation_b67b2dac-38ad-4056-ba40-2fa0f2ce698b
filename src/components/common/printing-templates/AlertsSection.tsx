import { useTranslation } from 'react-i18next';
import { useState, useEffect } from 'react';

import { useLocalized } from '@/shared/hooks/use-localized.hook';
import { formatLocalizedDate } from '@/shared/utils/format-date.utils';
import type { TripAlert } from '@/infrastructure/api/trip-alerts/types';

interface AlertsSectionProps {
    alerts: TripAlert[];
}

export function AlertsSection({ alerts }: AlertsSectionProps) {
    const { t, i18n } = useTranslation();
    const localized = useLocalized();

    // Set the direction based on the current language
    const [dir, setDir] = useState<'rtl' | 'ltr'>('ltr');

    // Update direction whenever the language changes
    useEffect(() => {
        // Check if the language is RTL (Arabic) or not
        setDir(i18n.language === 'ar' ? 'rtl' : 'ltr');
    }, [i18n.language]); // Runs whenever the language changes

    // Common className patterns for reusability
    const sectionTitleClass =
        'text-xl font-bold text-center mb-4 text-gray-800 print:text-lg print:mb-2 print:break-inside-avoid';
    const tableContainerClass =
        'border border-gray-200 rounded overflow-hidden print:border-gray-400 print:rounded-none print:break-inside-avoid';
    const tableHeaderClass = 'bg-gray-50 print:bg-gray-100';
    const tableHeaderCellClass =
        'px-3 py-2 text-center font-medium text-gray-700 border-b border-r border-gray-200 print:px-2 print:py-1 print:text-black print:border-gray-400';
    const tableRowClass = 'border-b border-gray-200 print:border-gray-400 print:break-inside-avoid';
    const tableCellClass =
        'px-3 py-2 text-center text-gray-600 border-r border-gray-200 print:px-2 print:py-1 print:text-black print:border-gray-400';

    // Helper functions
    const formatValue = (value: string | number | null | undefined, fallback = 'N/A'): string => {
        return value ? String(value) : fallback;
    };

    const formatDate = (dateValue: string | Date | null | undefined): string => {
        if (!dateValue) return 'N/A';
        try {
            // Convert Date object to string if needed
            const dateStr = dateValue instanceof Date ? dateValue.toISOString() : dateValue;
            return formatLocalizedDate(dateStr);
        } catch {
            return 'N/A';
        }
    };

    const formatLocation = (lat?: number, long?: number): string => {
        if (lat !== undefined && long !== undefined) {
            return `${lat.toFixed(6)}, ${long.toFixed(6)}`;
        }
        return 'N/A';
    };

    const formatSpeed = (speed?: number): string => {
        if (speed !== undefined && speed !== null) {
            return `${speed} km/h`;
        }
        return 'N/A';
    };

    // const formatStatus = (tripSate: TripState | null | undefined): React.ReactNode => {
    //     if (!tripSate) return 'N/A';
    //     return (
    //         <div className="flex gap-1">
    //             <p>{tripSate.gsmSignalStrength ?? 0} %</p>
    //             <p>{tripSate.gpsSignalStrength ?? 0} %</p>
    //             <p>{tripSate.batteryLevelPercentage ?? 0} %</p>
    //             <p>{tripSate.chargerStatus ? 'connected' : 'disconnected'}</p>
    //         </div>
    //     );
    // };

    // Empty state handling
    if (!alerts || alerts.length === 0) {
        return (
            <div className="w-full mx-auto bg-white p-4 print:p-2 print:max-w-none print:mx-0" dir={dir}>
                <div className="mb-6 print:mb-4">
                    <h2 className={sectionTitleClass}>{t('tripDetails.alerts')}</h2>
                    <p className="text-center text-gray-500 py-4 print:text-gray-700">{t('common.noWarnings')}</p>
                </div>
            </div>
        );
    }

    return (
        <div className="w-full  mx-auto bg-white p-4 print:p-2 print:max-w-none print:mx-0" dir={dir}>
            {/* Alerts Section */}
            <div className="mb-6 print:mb-4">
                <h2 className={sectionTitleClass}>{t('tripDetails.alerts')}</h2>

                <div className={tableContainerClass}>
                    <table className="w-full border-collapse">
                        {/* Table Header */}
                        <thead className={tableHeaderClass}>
                            <tr>
                                <th className={tableHeaderCellClass}>{t('common.alertType')}</th>
                                <th className={tableHeaderCellClass}>{t('common.address')}</th>
                                <th className={tableHeaderCellClass}>{t('common.location')}</th>
                                <th className={tableHeaderCellClass}>{t('common.timestamp')}</th>
                                <th className={tableHeaderCellClass}>{t('common.speed')}</th>
                                {/* <th className={lastHeaderCellClass}>{t('common.status')}</th> */}
                            </tr>
                        </thead>
                        {/* Table Body */}
                        <tbody>
                            {alerts.map((alert, index) => (
                                <tr key={alert.id} className={index < alerts.length - 1 ? tableRowClass : ''}>
                                    {/* Alert Type */}
                                    <td className={tableCellClass}>{localized(alert.alertType.name)}</td>
                                    {/* Address */}
                                    <td className={tableCellClass}>
                                        {formatValue(localized(alert.fromState.address))}
                                    </td>
                                    {/* Location (Coordinates) */}
                                    <td className={tableCellClass}>
                                        {formatLocation(alert.fromState.lat, alert.fromState.long)}
                                    </td>
                                    {/* Timestamp */}
                                    <td className={tableCellClass}>{formatDate(alert.fromState.trackerDateTime)}</td>
                                    {/* Speed */}
                                    <td className={tableCellClass}>{formatSpeed(alert.fromState.currentSpeed)}</td>
                                    {/* Status */}
                                    {/* <td className={lastCellClass}>{formatStatus(alert.fromState)}</td> */}
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    );
}
