/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useMemo, useRef } from 'react';
import { useReactToPrint } from 'react-to-print';

import { Button } from '../ui/Button';
import { Icon } from '../ui/Icon';

type Props = {
    contentRenderer: React.ComponentType<any>; // Component to be printed (e.g., AlertsSection, PingsTable, etc.)
    contentProps: any; // Props for the component to be printed
    dir?: 'rtl' | 'ltr'; // Direction for printing (RTL/LTR)
    className?: string; // Button styles
    label?: string; // Button label text
};

export default function PrintButton({
    contentRenderer: ContentRenderer,
    contentProps,
    dir = 'rtl',
    className,
    label = 'Print / Save as PDF',
}: Props) {
    const printRef = useRef<HTMLDivElement>(null);

    const handlePrint = useReactToPrint({
        contentRef: printRef, // Set the ref to the printable content
        documentTitle: `${contentProps?.tripId ?? 'report'}_print_report`, // Dynamic document title
        pageStyle: `
      @page { size: A4 portrait; margin: 12mm; }
      @media print {
        * { -webkit-print-color-adjust: exact !important; print-color-adjust: exact !important; }
      }
    `,
    });

    // Off-screen content for printing
    const printable = useMemo(
        () => (
            <div
                style={{
                    position: 'absolute',
                    left: '-10000px',
                    top: 0,
                    width: 0,
                    height: 0,
                    overflow: 'hidden',
                }}>
                <ContentRenderer ref={printRef} {...contentProps} dir={dir} />
            </div>
        ),
        [contentProps, dir, ContentRenderer],
    );

    return (
        <>
            <Button
                onClick={handlePrint}
                variant="secondary"
                className={`p-button-outlined bg-white border !border-green-600 rounded-sm  px-4 py-2 ${className}`}>
                <Icon name="print" className="w-7 h-7 text-green-700" />
            </Button>
            {printable}
        </>
        // <Button
        //     onClick={handlePrint}
        //     variant="secondary"
        //     className={`p-button-outlined bg-white border !border-green-600 rounded-sm  px-4 py-2 ${className}`}>
        //     <Icon name="print" className="w-7 h-7 text-green-700" />
        // </Button>
    );
}
