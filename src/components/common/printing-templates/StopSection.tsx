import { useTranslation } from 'react-i18next';

import { formatLocalizedDate } from '@/shared/utils/format-date.utils';
import type { TripStop } from '@/infrastructure/api/trip-stops/types';
import { useLocalized } from '@/shared/hooks/use-localized.hook';

interface StopSectionProps {
    tripStops: TripStop[];
}

export function StopSection({ tripStops }: StopSectionProps) {
    const { t } = useTranslation();
    const { localized } = useLocalized();

    // Common className patterns for reusability
    const sectionTitleClass =
        'text-xl font-bold text-center mb-4 text-gray-800 print:text-lg print:mb-2 print:break-inside-avoid';
    const tableContainerClass =
        'border border-gray-200 rounded overflow-hidden print:border-gray-400 print:rounded-none print:break-inside-avoid';
    const tableHeaderClass = 'bg-gray-50 print:bg-gray-100';
    const tableHeaderCellClass =
        'px-3 py-2 text-center font-medium text-gray-700 border-b border-r border-gray-200 print:px-2 print:py-1 print:text-black print:border-gray-400';
    const lastHeaderCellClass =
        'px-3 py-2 text-center font-medium text-gray-700 border-b border-gray-200 print:px-2 print:py-1 print:text-black print:border-gray-400';
    const tableRowClass = 'border-b border-gray-200 print:border-gray-400 print:break-inside-avoid';
    const tableCellClass =
        'px-3 py-2 text-center text-gray-600 border-r border-gray-200 print:px-2 print:py-1 print:text-black print:border-gray-400';
    const lastCellClass = 'px-3 py-2 text-center text-gray-600 print:px-2 print:py-1 print:text-black';

    // Helper function to format duration
    const formatDuration = (hours: number, minutes: number): string => {
        return `${hours}h ${minutes}m`;
    };

    // Empty state handling
    if (!tripStops || tripStops.length === 0) {
        return (
            <div className="w-full max-w-4xl mx-auto bg-white p-4 print:p-2 print:max-w-none print:mx-0" dir="rtl">
                <div className="mb-6 print:mb-4">
                    <h2 className={sectionTitleClass}>{t('tripDetails.stops')}</h2>
                    <p className="text-center text-gray-500 py-4 print:text-gray-700">{t('common.noStops')}</p>
                </div>
            </div>
        );
    }

    return (
        <div className="w-full max-w-4xl mx-auto bg-white p-4 print:p-2 print:max-w-none print:mx-0" dir="rtl">
            {/* Stops Section */}
            <div className="mb-6 print:mb-4">
                <h2 className={sectionTitleClass}>{t('stops.title')}</h2>

                <div className={tableContainerClass}>
                    <table className="w-full border-collapse">
                        {/* Table Header */}
                        <thead className={tableHeaderClass}>
                            <tr>
                                <th className={tableHeaderCellClass}>{t('stops.title')}</th>
                                <th className={tableHeaderCellClass}>{t('stops.from')}</th>
                                <th className={tableHeaderCellClass}>{t('stops.to')}</th>
                                <th className={tableHeaderCellClass}>{t('stops.duration')}</th>
                                <th className={lastHeaderCellClass}>{t('common.address')}</th>
                            </tr>
                        </thead>
                        {/* Table Body */}
                        <tbody>
                            {tripStops.map((stop, index) => (
                                <tr key={stop.id} className={index < tripStops.length - 1 ? tableRowClass : ''}>
                                    {/* Place Name */}
                                    <td className={tableCellClass}>{localized(stop.address) || 'N/A'}</td>
                                    {/* From Time */}
                                    <td className={tableCellClass}>{formatLocalizedDate(stop.fromTime)}</td>
                                    {/* To Time */}
                                    <td className={tableCellClass}>{formatLocalizedDate(stop.toTime)}</td>
                                    {/* Duration */}
                                    <td className={tableCellClass}>
                                        {formatDuration(stop.period.hours, stop.period.minutes)}
                                    </td>
                                    {/* Address */}
                                    <td className={lastCellClass}>{localized(stop.address)}</td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    );
}
