import { useTranslation } from 'react-i18next';

import { useLocalized } from '@/shared/hooks/use-localized.hook';
import { formatLocalizedDate } from '@/shared/utils/format-date.utils';
import type { TripDetail } from '@/infrastructure/api/trips/types';

interface TripDetailsSectionProps {
    tripDetail: TripDetail | null;
}

export function TripDetailsSection({ tripDetail }: TripDetailsSectionProps) {
    const { t } = useTranslation();
    const { localized, dir } = useLocalized();

    if (!tripDetail) {
        return <div className="text-center text-gray-500 py-10">No trip details available</div>;
    }

    // Shared style classes
    const baseCell = 'px-3 py-2 break-words print:px-2 print:py-1';
    const labelCell = `${baseCell} bg-gray-50 text-gray-700 font-medium print:bg-gray-100`;
    const valueCell = `${baseCell} text-gray-600 `;
    const sectionTitle =
        'text-xl font-bold text-center mb-4 text-gray-800 print:text-lg print:mb-2 print:break-inside-avoid';
    const tableContainer =
        'border border-gray-700 rounded overflow-hidden print:border-gray-400 print:rounded-none print:break-inside-avoid';
    const rowClass = 'grid grid-cols-[1fr_2fr_1fr_2fr]  print:border-gray-400 print:break-inside-avoid';
    const lastRowClass = 'grid grid-cols-[1fr_2fr_1fr_2fr] print:break-inside-avoid';

    // Helper formatters
    const formatValue = (value: string | number | null | undefined, fallback = 'N/A'): string =>
        value ? String(value) : fallback;

    const formatDate = (dateValue: string | null | undefined): string => {
        if (!dateValue) return 'N/A';
        try {
            return formatLocalizedDate(dateValue);
        } catch {
            return formatValue(dateValue);
        }
    };

    const formatPort = (portName: { english: string; arabic: string } | null | undefined): string =>
        portName ? localized(portName) : 'N/A';

    const formatElocks = (): string =>
        tripDetail.eLocks && tripDetail.eLocks.length > 0
            ? tripDetail.eLocks.map((lock) => lock.deviceId).join(', ')
            : 'N/A';

    const formatTripStatus = (): string =>
        tripDetail.status === 'Active' ? t('tripDetails.open') : t('tripDetails.closed');

    // Helper renderer for rows
    const renderTableRow = (
        label1: string,
        value1: string | number | null | undefined,
        label2?: string,
        value2?: string | number | null | undefined,
        isLastRow = false,
    ) => (
        <div className={isLastRow ? lastRowClass : rowClass} dir={dir}>
            {/* Label 1 */}
            <div className={labelCell}>{label1}</div>
            {/* Value 1 */}
            <div className={valueCell}>{formatValue(value1)}</div>

            {/* Label 2 + Value 2 (optional) */}
            {label2 ? (
                <>
                    <div className={labelCell}>{label2}</div>
                    <div className={valueCell}>{formatValue(value2)}</div>
                </>
            ) : (
                <>
                    <div className="bg-gray-50 print:bg-gray-100" />
                    <div />
                </>
            )}
        </div>
    );

    return (
        <div className="w-full  mx-auto bg-white p-2 print:p-2 print:max-w-none print:mx-0" dir={dir}>
            {/* --- Trip Details Section --- */}
            <div className="mb-2 print:mb-2">
                <h2 className={sectionTitle}>{t('common.tripDetails')}</h2>
                <div className={tableContainer}>
                    {renderTableRow(
                        t('tripDetails.transitType'),
                        tripDetail.transitTypeName,
                        t('tripDetails.transitNumber'),
                        tripDetail.transitNumber,
                    )}
                    {renderTableRow(
                        t('tripDetails.movementNumber'),
                        tripDetail.transSeq,
                        t('tripDetails.tripCode'),
                        tripDetail.id,
                    )}
                    {renderTableRow(
                        t('tripDetails.shipmentDescription'),
                        tripDetail.shipmentDescription || tripDetail.newShipmentDescription,
                        t('tripDetails.owner'),
                        tripDetail.ownerType || tripDetail.ownerDesc,
                    )}
                    {renderTableRow(
                        t('tripDetails.entryPort'),
                        formatPort(tripDetail.entryPort?.name),
                        t('tripDetails.tripStatus'),
                        formatTripStatus(),
                    )}
                    {renderTableRow(
                        t('tripDetails.startDate'),
                        formatDate(tripDetail.startDate),
                        t('tripDetails.exitPort'),
                        formatPort(tripDetail.exitPort?.name),
                    )}
                    {renderTableRow(
                        t('tripDetails.endDate'),
                        formatDate(tripDetail.endDate),
                        t('tripDetails.transitDate'),
                        formatDate(tripDetail.transitDate),
                    )}
                    {renderTableRow(
                        t('tripDetails.elocks'),
                        formatElocks(),
                        t('tripDetails.trackingDevice'),
                        tripDetail.tracker?.serialNumber,
                    )}
                    {renderTableRow(t('tripDetails.notes'), tripDetail.securityNotes, undefined, undefined, true)}
                </div>
            </div>

            {/* Vehicle Details Section */}
            <div className="mb-6 print:mb-4">
                <h2 className={sectionTitle}>{t('tripDetails.vehicleDetails')}</h2>
                <div className={tableContainer}>
                    {/* Row 1: Driver Name & Plate Number */}
                    {renderTableRow(
                        t('tripDetails.driverName'),
                        tripDetail.driver?.name,
                        t('tripDetails.vehiclePlateNumber'),
                        tripDetail.vehicle?.plateNo,
                    )}

                    {/* Row 2: Driver Phone & Complete Distance */}
                    {renderTableRow(
                        t('tripDetails.driverContactNo'),
                        tripDetail.driver?.mobileNo,
                        t('tripDetails.completeDistance'),
                        tripDetail.currentState?.completeDistanceInMeters,
                    )}

                    {/* Row 3: Passport Number & Remaining Distance */}
                    {renderTableRow(
                        t('tripDetails.driverPassportNumber'),
                        tripDetail.driver?.passportNumber,
                        t('tripDetails.remainingDistance'),
                        tripDetail.currentState?.remainingDistanceInMeters,
                    )}

                    {/* Row 4: Driver Nationality */}
                    {renderTableRow(
                        t('tripDetails.driverNationality'),
                        tripDetail.driver?.passportCountry,
                        undefined,
                        undefined,
                        true,
                    )}
                </div>
            </div>
        </div>
    );
}
