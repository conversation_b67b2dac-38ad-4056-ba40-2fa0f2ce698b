import { forwardRef } from 'react';

import type { TripDetail } from '@/infrastructure/api/trips/types';
import type { TripStop } from '@/infrastructure/api/trip-stops/types';

import { SaudiCustomsTuHeader } from './SaudiCustomsTuHeader';
import { TripDetailsSection } from './TripDetailsSection';
import { StopSection } from './StopSection'; // Assuming you've created StopSection as before

type Props = {
    details: TripDetail | null;
    tripStops: TripStop[];
    dir?: 'rtl' | 'ltr';
};

const StopReportPrintable = forwardRef<HTMLDivElement, Props>(({ details, tripStops, dir = 'rtl' }, ref) => {
    return (
        <div ref={ref} dir={dir} className="print:bg-white">
            {/* PAGE */}
            <div
                className="mx-auto bg-white text-black"
                // style={{
                //     width: '210mm',
                //     minHeight: '297mm',
                //     padding: '12mm',
                //     boxSizing: 'border-box',
                // }}
            >
                <SaudiCustomsTuHeader />

                {/* Trip details (first) */}
                {details && <TripDetailsSection tripDetail={details} />}

                {/* Optional break before stops if you want stops to start on a new page */}
                <div className="print:break-before-page"></div>

                {/* Stops section */}
                <StopSection tripStops={tripStops} />
            </div>
        </div>
    );
});

StopReportPrintable.displayName = 'StopReportPrintable';
export default StopReportPrintable;
