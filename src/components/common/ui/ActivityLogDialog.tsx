/* eslint-disable @typescript-eslint/no-non-null-assertion */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useEffect, useMemo, useState } from 'react';
import { Dropdown } from 'primereact/dropdown';
import { useTranslation } from 'react-i18next';

import { Button } from '@/components/common/ui/Button';
import {
    Dialog,
    DialogClose,
    DialogContent,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from '@/components/common/ui/Dialog';
import { Input } from '@/components/common/ui/Input';
import { Label } from '@/components/common/ui/Label';
import { Icon } from '@/components/common/ui/Icon';
import { useActivityLookupsStore } from '@/stores/activity-lookup.store';
import { useTripActivitiesStore } from '@/stores/trip-activities.store';
import { useLocalized } from '@/shared/hooks/use-localized.hook';

type EditInitial = {
    statusId?: number | null;
    actionId?: number | null;
    details?: string | null;
    note?: string | null;
};

type Props = {
    mode: 'create' | 'edit';
    open: boolean;
    onOpenChange: (open: boolean) => void;

    /** Required for both modes */
    tripId: number;

    /** Only for edit mode */
    activityId?: number;
    initial?: EditInitial;

    /** Optional callbacks */
    onSuccess?: () => void;
    onError?: (e: unknown) => void;
};

export default function ActivityLogDialog({
    mode,
    open,
    onOpenChange,
    tripId,
    activityId,
    initial,
    onSuccess,
    onError,
}: Props) {
    const { t } = useTranslation();
    const { localized } = useLocalized();

    // lookups
    const statuses = useActivityLookupsStore((s: any) => s.statuses);
    const actions = useActivityLookupsStore((s: any) => s.actions);
    const lookupLoading = useActivityLookupsStore((s: any) => s.isLoading);
    const lookupLoaded = useActivityLookupsStore((s: any) => s.loaded);
    const loadLookups = useActivityLookupsStore((s: any) => s.loadLookups);

    // store
    const isCreating = useTripActivitiesStore((s) => s.isCreating);
    const createTrip = useTripActivitiesStore((s) => s.createTripActivity);
    const isEditing = useTripActivitiesStore((s: any) => s.isEditing ?? false);
    const editTripActivity = useTripActivitiesStore((s: any) => s.editTripActivity);

    // local form state
    const [statusId, setStatusId] = useState<number | null>(initial?.statusId ?? null);
    const [actionId, setActionId] = useState<number | null>(initial?.actionId ?? null);
    const [details, setDetails] = useState<string>(initial?.details ?? '');
    const [reportNumber, setReportNumber] = useState<string>(mode === 'create' ? '' : (initial?.note ?? '')); // create uses "notes", edit uses "note"

    // preload lookups when opening
    useEffect(() => {
        if (open && !lookupLoaded && !lookupLoading) {
            void loadLookups();
        }
    }, [open, lookupLoaded, lookupLoading, loadLookups]);

    // when switching to edit with new initial, sync fields
    useEffect(() => {
        if (mode === 'edit' && open) {
            setStatusId(initial?.statusId ?? null);
            setActionId(initial?.actionId ?? null);
            setDetails(initial?.details ?? '');
            setReportNumber(initial?.note ?? '');
        }
    }, [mode, open, initial?.statusId, initial?.actionId, initial?.details, initial?.note]);

    const statusOptions = useMemo(
        () => statuses.map((s: any) => ({ label: localized(s.name), value: s.id })),
        [statuses, localized],
    );
    const actionOptions = useMemo(
        () => actions.map((a: any) => ({ label: localized(a.name), value: a.id })),
        [actions, localized],
    );

    const creating = mode === 'create' ? isCreating : isEditing;

    // Validation
    const canCreate = Boolean(mode === 'create' && statusId && actionId && details.trim() && Number.isFinite(tripId));
    const canEdit = Boolean(mode === 'edit' && Number.isFinite(tripId) && Number.isFinite(activityId ?? NaN));
    const canSubmit = mode === 'create' ? canCreate : canEdit;

    const resetForm = () => {
        setStatusId(null);
        setActionId(null);
        setDetails('');
        setReportNumber('');
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!canSubmit) return;

        try {
            if (mode === 'create') {
                await createTrip(tripId, {
                    statusId: statusId!, // required
                    actionId: actionId!, // required
                    details: details.trim(), // required
                    notes: reportNumber?.toString().trim() || undefined, // (رقم البلاغ) create expects "notes"
                });
                resetForm();
            } else {
                // Edit: only send changed / non-empty values
                const payload: Record<string, unknown> = {};
                if (statusId !== undefined && statusId !== null && statusId !== initial?.statusId)
                    payload.statusId = statusId;
                if (actionId !== undefined && actionId !== null && actionId !== initial?.actionId)
                    payload.actionId = actionId;
                if (details !== undefined && details !== initial?.details && details.trim() !== '')
                    payload.details = details.trim();
                // edit expects "note" (singular)
                if (reportNumber !== undefined && reportNumber !== initial?.note && reportNumber.trim() !== '')
                    payload.note = reportNumber.trim();

                // If nothing changed, you can early-return or still call backend; here we prevent no-op.
                if (Object.keys(payload).length === 0) {
                    onOpenChange(false);
                    onSuccess?.();
                    return;
                }

                await editTripActivity(tripId, activityId!, payload);
            }

            onOpenChange(false);
            onSuccess?.();
        } catch (err) {
            onError?.(err);
        }
    };

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-[520px] w-full rounded-md p-4">
                <form onSubmit={handleSubmit} className="flex flex-col gap-4">
                    {/* Header */}
                    <DialogHeader className="border-b pb-2">
                        <DialogTitle className="text-lg font-semibold text-gray-800 flex justify-between items-center">
                            <div className="flex items-center gap-2">
                                <Icon
                                    name={mode === 'create' ? 'createActivity' : 'edit'}
                                    className={mode === 'create' ? 'text-green-700 w-7 h-7' : 'text-cyan-600 w-7 h-7'}
                                />
                                <span>{mode === 'create' ? t('tripActivities.create') : t('tripActivities.edit')}</span>
                            </div>
                        </DialogTitle>
                    </DialogHeader>

                    {/* Fields */}
                    <div className="grid gap-3">
                        {/* Status */}
                        <div className="grid gap-1">
                            <Label htmlFor="status">{t('tripActivities.status')}</Label>
                            <Dropdown
                                id="status"
                                value={statusId}
                                options={statusOptions}
                                onChange={(e) => setStatusId(e.value)}
                                optionLabel="label"
                                optionValue="value"
                                placeholder={t('tripActivities.statusPlaceholder')}
                                className="w-full border border-gray-300 rounded-md p-2 text-sm"
                                loading={lookupLoading}
                                disabled={lookupLoading}
                                appendTo="self"
                            />
                        </div>

                        {/* Action */}
                        <div className="grid gap-1">
                            <Label htmlFor="action">{t('tripActivities.action')}</Label>
                            <Dropdown
                                id="action"
                                value={actionId}
                                options={actionOptions}
                                onChange={(e) => setActionId(e.value)}
                                optionLabel="label"
                                optionValue="value"
                                placeholder={t('tripActivities.actionPlaceholder')}
                                className="w-full border border-gray-300 rounded-md p-2 text-sm"
                                loading={lookupLoading}
                                disabled={lookupLoading}
                                appendTo="self"
                            />
                        </div>

                        {/* Details */}
                        <div className="grid gap-1">
                            <Label htmlFor="details">{t('tripActivities.details')}</Label>
                            <textarea
                                id="details"
                                value={details}
                                onChange={(e) => setDetails(e.target.value)}
                                placeholder={t('tripActivities.detailsPlaceholder')}
                                className="h-[91px] w-full border border-gray-300 rounded-md p-2 resize-none text-sm align-top"
                            />
                        </div>

                        {/* Report Number / Note */}
                        <div className="grid gap-1">
                            <Label htmlFor="reportNumber">{t('tripActivities.reportNumber')}</Label>
                            <Input
                                id="reportNumber"
                                type="text"
                                value={reportNumber}
                                onChange={(e) => setReportNumber(e.target.value)}
                                placeholder={t('tripActivities.reportNumberPlaceholder')}
                                className="w-full border border-gray-300 rounded-md p-2"
                            />
                        </div>
                    </div>

                    {/* Footer */}
                    <DialogFooter className="flex justify-end gap-2 mt-4">
                        <DialogClose asChild>
                            <Button
                                type="button"
                                variant="outline"
                                className="bg-white border border-gray-400 text-gray-700 rounded-sm px-4 py-2"
                                disabled={creating}>
                                {t('common.cancel')}
                            </Button>
                        </DialogClose>

                        <Button
                            type="submit"
                            className={`${mode === 'create' ? 'bg-green-700' : 'bg-cyan-600'} text-white rounded-sm px-4 py-2`}
                            disabled={!canSubmit || creating}>
                            {creating
                                ? t('common.saving')
                                : mode === 'create'
                                  ? t('tripActivities.create')
                                  : t('tripActivities.edit')}
                        </Button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    );
}
