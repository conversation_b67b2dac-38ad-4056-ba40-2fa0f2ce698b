// components/common/PrintTableButton.tsx
import { Button } from '@/components/common/ui/Button';
import { logger } from '@/infrastructure/logging';

import { Icon } from './Icon';

interface PrintTableButtonProps {
    tableId: string; // ID of the table you want to print
    className?: string;
    title?: string; // Optional report title
    tripId?: string | number; // Trip ID for printing
    rowsPerPage?: number; // Number of rows per page. Default: 20
}

export const PrintTableButton = ({
    tableId,
    className = '',
    title = 'Trip Report',
    tripId,
    rowsPerPage = 20,
}: PrintTableButtonProps) => {
    const handlePrint = () => {
        const tableElement = document.getElementById(tableId);

        if (!tableElement) {
            logger.error(`Table with id "${tableId}" not found`);
            return;
        }

        // Get all rows and header
        const rows = Array.from(tableElement.querySelectorAll('tbody tr'));
        const header = tableElement.querySelector('thead')?.outerHTML || '';

        // Split rows into chunks of rowsPerPage
        const chunks: string[][] = [];
        for (let i = 0; i < rows.length; i += rowsPerPage) {
            const chunk = rows.slice(i, i + rowsPerPage).map((r) => r.outerHTML);
            chunks.push(chunk);
        }

        const now = new Date();
        const formattedDateTime = now.toLocaleString();

        const printWindow = window.open('', '_blank', 'width=900,height=700');

        if (printWindow) {
            let content = `
                <html>
                    <head>
                        <title>${title}</title>
                        <style>
                            @media print {
                                body {
                                    -webkit-print-color-adjust: exact;
                                    font-family: Arial, sans-serif;
                                    padding: 20px;
                                    color: #333;
                                }
                                h1 {
                                    text-align: center;
                                    margin-bottom: 10px;
                                    font-size: 22px;
                                    color: #2c3e50;
                                }
                                .meta {
                                    text-align: center;
                                    margin-bottom: 20px;
                                    font-size: 14px;
                                    color: #555;
                                }
                                table {
                                    border-collapse: collapse;
                                    width: 100%;
                                    margin-top: 20px;
                                    page-break-after: always;
                                }
                                th, td {
                                    border: 1px solid #ccc;
                                    padding: 8px;
                                    text-align: center;
                                    font-size: 13px;
                                }
                                th {
                                    background-color: #f5f5f5;
                                    font-weight: bold;
                                    font-size: 14px;
                                }
                                tr:nth-child(even) {
                                    background-color: #fafafa;
                                }
                                .page-break {
                                    page-break-before: always;
                                }
                            }
                        </style>
                    </head>
                    <body>
                        <h1>${title}</h1>
                        <div class="meta">
                            ${tripId ? `<div><strong>Trip ID:</strong> ${tripId}</div>` : ''}
                            <div><strong>Date:</strong> ${formattedDateTime}</div>
                        </div>
            `;

            chunks.forEach((chunk, pageIndex) => {
                content += `
                    <table>
                        ${header}
                        <tbody>
                            ${chunk.join('')}
                        </tbody>
                    </table>
                    ${pageIndex < chunks.length - 1 ? '<div class="page-break"></div>' : ''}
                `;
            });

            content += `</body></html>`;

            printWindow.document.body.innerHTML = content;
            printWindow.document.close();

            printWindow.onload = () => {
                printWindow.focus();
                printWindow.print();
                printWindow.onafterprint = () => {
                    printWindow.close();
                };
            };
        }
    };

    return (
        <Button
            onClick={handlePrint}
            variant="secondary"
            className={`p-button-outlined bg-white border !border-green-600 rounded-sm  px-4 py-2 ${className}`}>
            <Icon name="print" className="w-7 h-7 text-green-700" />
        </Button>
    );
};
