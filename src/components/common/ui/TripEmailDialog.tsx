import React, { useEffect, useState, useRef } from 'react';
import ReactDOMServer from 'react-dom/server';
import { useTranslation } from 'react-i18next';
import { Editor } from 'primereact/editor';

// import juice from 'juice';
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from '@/components/common/ui/DropdownMenu';
import type { Alert, TripDetail } from '@/infrastructure/api/trips/types';
import { logger } from '@/infrastructure/logging';
import { mapTripToTripEmail } from '@/mappers/emails/tripEmail.mapper';
import { showError, showSuccess } from '@/components/common/ui/Toast';
import type { Scope } from '@/shared/enums';
import { useLocalized } from '@/shared/hooks/use-localized.hook';
import { useEmailStore } from '@/stores/email.store';
import { useTripAlertsStore } from '@/stores/trip-alerts.store';
import { useTripDetailStore } from '@/stores/trip-detail.store';

import { TripEmailTemplate } from '../email-templates/TripTemplate';
// import { TripEmailTemplate } from '../email-templates/TripEmailTemplate';

import { Button } from './Button';
import { Checkbox } from './Checkbox';
import {
    Dialog,
    DialogClose,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from './Dialog';
import { Icon } from './Icon';
import { Input } from './Input';
import { Label } from './Label';

interface TripEmailDialogProps {
    children?: React.ReactNode;
    scope: Scope; // Trip=1, TripAlert=2
}

export default function TripEmailDialog({ children, scope }: TripEmailDialogProps) {
    const { t } = useTranslation();
    const { dir } = useLocalized();
    const [isOpen, setIsOpen] = useState(false);
    const [htmlContent, setHtmlContent] = useState<string>('');
    const [to, setToValue] = useState<string>('');
    const [cc, setCcValue] = useState<string>('');
    const [subject, setSubjectValue] = useState<string>('');
    const [isSending, setIsSending] = useState<boolean>(false);
    const [isFilterOpen, setIsFilterOpen] = useState(false);
    const [selectedFilters, setSelectedFilters] = useState<number[]>([]);
    const dropdownRef = useRef<HTMLDivElement>(null);

    const { setTo, setCc, setSubject, setBody, setScope, sendEmail } = useEmailStore();
    const tripAlerts = useTripAlertsStore((state) => state.tripAlerts);
    const tripDetail = useTripDetailStore((state) => state.tripDetail);
    const { localized } = useLocalized();

    useEffect(() => {
        setSelectedFilters(tripAlerts.map((opt) => opt.id));
    }, [tripAlerts]);

    useEffect(() => {
        if (!tripDetail) return;
        const modifiedTrip: TripDetail = {
            ...tripDetail,
            activeAlerts: tripAlerts
                ? (tripAlerts.filter((x) => selectedFilters.includes(x.id)) as unknown as Alert[])
                : [],
        };

        const html = ReactDOMServer.renderToStaticMarkup(<TripEmailTemplate trip={mapTripToTripEmail(modifiedTrip)} />);
        // const html = ReactDOMServer.renderToStaticMarkup(<TripEmailTemplate tripDetail={modifiedTrip} />);
        // const html = ReactDOMServer.renderToString(<TripEmailTemplate tripDetail={modifiedTrip} />);

        // const rawHtml = ReactDOMServer.renderToString(<TripEmailTemplate tripDetail={modifiedTrip} />);
        // const html = juice(rawHtml); // ✅ يقوم بدمج CSS inline

        setHtmlContent(html);
        setBody(html);
    }, [selectedFilters, tripDetail, tripAlerts, setBody]);

    // Click outside handler to close dropdown
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsFilterOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    /** Validate input fields before sending */
    const validateFields = (): boolean => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

        if (!to || !emailRegex.test(to)) {
            showError(t('common.error'), t('validation.emailInvalid'));
            return false;
        }

        if (cc && !emailRegex.test(cc)) {
            showError(t('common.error'), t('validation.emailInvalid'));
            return false;
        }

        if (!subject.trim()) {
            showError(t('common.error'), t('validation.subjectRequired'));
            return false;
        }

        if (!htmlContent.trim()) {
            showError(t('common.error'), t('validation.descriptionRequired'));
            return false;
        }

        return true;
    };

    /** Handle form submission */
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!validateFields()) return;
        try {
            setIsSending(true);
            setScope(scope);
            setTo([to]);
            if (cc) setCc([cc]);
            setSubject(subject);
            setBody(htmlContent);

            await sendEmail();

            showSuccess(t('common.success'), t('emailDialog.sendSuccess'));
            setIsOpen(false);
        } catch (error) {
            logger.error('[ EmailDialog.handleSubmit] Error occurred while sending email. Error: ', error as Error);
            showError(t('common.error'), t('emailDialog.sendFailed'));
        } finally {
            setIsSending(false);
        }
    };

    /** Toggle individual filter selection */
    const toggleFilter = (value: number) => {
        setSelectedFilters((prev) => {
            if (prev.includes(value)) {
                return prev.filter((filter) => filter !== value);
            } else {
                return [...prev, value];
            }
        });
    };

    const toggleAll = () => {
        if (selectedFilters.length === tripAlerts.length) {
            setSelectedFilters([]);
        } else {
            const newFilters = tripAlerts.map((opt) => opt.id);

            setSelectedFilters(newFilters);
        }
    };

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>{children}</DialogTrigger>
            <DialogDescription></DialogDescription>
            <DialogContent className="sm:max-w-[500px] md:min-w-[700px] lg:min-w-[1600px] md:min-h-[600px] lg:min-h-[900px]">
                <form onSubmit={handleSubmit}>
                    <DialogHeader className="mb-3">
                        <DialogTitle>
                            <div className="flex items-center gap-3 text-green-700">
                                <Icon name="email" className="size-6" />
                                {t('emailDialog.title')}
                            </div>
                        </DialogTitle>
                    </DialogHeader>

                    <div className="grid grid-cols-2 gap-4 py-4">
                        <div className="grid gap-2">
                            <Label htmlFor="to">{t('emailDialog.email')}</Label>
                            <Input
                                id="to"
                                type="email"
                                value={to}
                                onChange={(e) => setToValue(e.target.value)}
                                placeholder={t('emailDialog.recipientEmail')}
                                required
                            />
                        </div>

                        <div className="grid  gap-2">
                            <Label htmlFor="cc">{t('emailDialog.cc')}</Label>
                            <Input
                                id="cc"
                                type="email"
                                value={cc}
                                onChange={(e) => setCcValue(e.target.value)}
                                placeholder={t('emailDialog.ccPlaceholder')}
                            />
                        </div>

                        <div className="grid col-span-2 gap-2">
                            <Label htmlFor="subject">{t('emailDialog.subject')}</Label>
                            <Input
                                id="subject"
                                type="text"
                                value={subject}
                                onChange={(e) => setSubjectValue(e.target.value)}
                                placeholder={t('emailDialog.subjectPlaceholder')}
                                required
                            />
                        </div>

                        <div className="flex items-start gap-2 relative">
                            <DropdownMenu open={isFilterOpen} onOpenChange={setIsFilterOpen} dir={dir}>
                                <DropdownMenuTrigger asChild dir={dir}>
                                    <Button
                                        variant="outline"
                                        disabled={tripAlerts.length === 0}
                                        size="sm"
                                        className="flex items-center gap-1">
                                        <Icon name="alert" className="w-4 h-4" />
                                        {tripAlerts.length === 0 ? t('tripDetails.noAlerts') : t('tripDetails.alerts')}
                                    </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent className="max-h-100 overflow-y-auto p-2">
                                    {/* Button to select/unselect all Alert */}
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={toggleAll}
                                        className="whitespace-nowrap">
                                        {selectedFilters.length === tripAlerts.length
                                            ? t('filter.deSelectAll')
                                            : t('filter.selectAll')}
                                    </Button>

                                    <div className="border-t my-2" />

                                    {tripAlerts.map((alert) => (
                                        <Checkbox
                                            key={alert.id}
                                            label={`${alert.id} - ${localized(alert.alertType.name)}`}
                                            checked={selectedFilters.includes(alert.id)}
                                            onChange={() => toggleFilter(alert.id)}
                                            className={`${alert.acknowledgedAt ? 'opacity-75' : ''}`}
                                        />
                                    ))}
                                </DropdownMenuContent>
                            </DropdownMenu>
                        </div>

                        <div className="grid col-span-2  gap-2">
                            <Label htmlFor="description" className="mb-1">
                                {t('emailDialog.descriptionPlaceholder')}
                            </Label>
                            <Editor
                                // unstyled
                                // formats={[]}
                                // showHeader={false}
                                dir="ltr"
                                placeholder={t('emailDialog.descriptionPlaceholder')}
                                onTextChange={(e) => setHtmlContent(e.htmlValue ?? '')}
                                style={{ height: '500px', textAlign: dir === 'rtl' ? 'right' : 'left' }}
                                value={htmlContent}
                            />
                        </div>
                    </div>

                    <DialogFooter>
                        <DialogClose asChild>
                            <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
                                {t('common.cancel')}
                            </Button>
                        </DialogClose>

                        <Button
                            type="submit"
                            disabled={isSending}
                            className="flex text-white rounded-md px-4 py-2 bg-green-700 disabled:opacity-60">
                            {isSending ? t('common.sending') : t('common.send')}
                        </Button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    );
}
