/**
 * DraggableList Component
 *
 * A reusable drag-and-drop list component built with **@dnd-kit**.
 * It allows you to render a list of items that can be reordered via drag-and-drop.
 * The component supports horizontal and vertical layouts, localStorage persistence,
 * and a callback to handle order changes.
 *
 * ---
 * 📦 Libraries Used:
 * - @dnd-kit/core → Provides the drag-and-drop context and sensors.
 * - @dnd-kit/sortable → Adds sortable behavior to list items.
 * - @dnd-kit/utilities → Utility helpers (e.g., CSS transforms).
 * - React (useState, useEffect) → State and lifecycle management.
 *
 * ---
 * 🚀 Features:
 * - Reusable with any item type (cards, list items, widgets, etc.).
 * - Supports both **horizontal** and **vertical** layouts.
 * - Optional **localStorage** integration to persist order across sessions.
 * - Provides an `onChange` callback to listen for order changes.
 * - Tailwind-friendly with customizable `gap` and `className`.
 *
 * ---
 * 🔧 Props:
 * - `items: string[]` → List of item identifiers.
 * - `renderItem: (item: string) => ReactNode` → Render function for each item.
 * - `storageKey?: string` → Optional key to persist order in localStorage.
 * - `strategy?: 'horizontal' | 'vertical'` → Layout orientation (default: "horizontal").
 * - `className?: string` → Additional classes for container styling.
 * - `gap?: string` → Tailwind gap classes (default: "gap-3").
 * - `onChange?: (newOrder: string[]) => void` → Callback when order changes.
 *
 * ---
 * 💡 Example Usage:
 * ```tsx
 * import DraggableList from '@/components/common/ui/DraggableList';
 *
 * const cards = ['card1', 'card2', 'card3', 'card4'];
 *
 * export default function Dashboard() {
 *   return (
 *     <DraggableList
 *       items={cards}
 *       storageKey="dashboardCardOrder"
 *       strategy="horizontal"
 *       gap="gap-4"
 *       renderItem={(id) => (
 *         <div className="p-4 bg-white shadow rounded-lg">
 *           {id.toUpperCase()}
 *         </div>
 *       )}
 *       onChange={(newOrder) => console.log("New order:", newOrder)}
 *     />
 *   );
 * }
 * ```
 *
 * ---
 */

// src/components/common/ui/DraggableList.tsx
import type { ReactNode } from 'react';
import { useState, useEffect } from 'react';
import { DndContext, closestCenter, PointerSensor, useSensor, useSensors, type DragEndEvent } from '@dnd-kit/core';
import { SortableContext, horizontalListSortingStrategy, useSortable, arrayMove } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

interface SortableItemProps {
    id: string;
    children: ReactNode;
}

function SortableItem({ id, children }: SortableItemProps) {
    const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({ id });

    const style: React.CSSProperties = {
        transform: CSS.Transform.toString(transform),
        transition,
        zIndex: isDragging ? 50 : undefined,
    };

    return (
        <div ref={setNodeRef} style={style} {...attributes} {...listeners}>
            {children}
        </div>
    );
}

interface DraggableListProps<T extends string> {
    items: T[];
    renderItem: (item: T) => ReactNode;
    storageKey?: string; // optional: save order in localStorage
    strategy?: 'horizontal' | 'vertical';
    className?: string;
    gap?: string;
    onChange?: (newOrder: T[]) => void;
}

/**
 * Utility: try to restore order from storage and validate it:
 * - parsed must be an array of strings
 * - every element of parsed must exist in items (strict subset)
 * - it's allowed that parsed.length !== items.length (we will merge later)
 */
function readStoredOrder<T extends string>(storageKey: string | undefined, items: T[]): T[] | null {
    if (!storageKey) return null;
    try {
        const raw = localStorage.getItem(storageKey);
        if (!raw) return null;
        const parsed = JSON.parse(raw);
        if (!Array.isArray(parsed)) return null;
        // ensure all parsed entries are strings and exist in current items
        const parsedStrings = parsed.filter((p) => typeof p === 'string') as T[];
        // Check that parsedStrings is a subset of items
        const allExist = parsedStrings.every((p) => items.includes(p));
        return allExist ? parsedStrings : null;
    } catch {
        return null;
    }
}

/**
 * DraggableList - improved behavior:
 * - Initializes order from storage if valid
 * - On items change, merges stored/current order so saved order is preserved and new items appended
 */
export default function DraggableList<T extends string>({
    items,
    renderItem,
    storageKey,
    strategy = 'horizontal',
    className = '',
    gap = 'gap-3',
    onChange,
}: DraggableListProps<T>) {
    const initialOrder = (() => {
        const stored = readStoredOrder(storageKey, items);
        return (stored && stored.length > 0 ? stored : items) as T[];
    })();

    const [order, setOrder] = useState<T[]>(initialOrder);

    // When items prop changes:
    // - If there is a valid stored order (subset of items), use it and append any new items.
    // - Otherwise, try to merge previous 'order' with new items (keep existing order, append new)
    useEffect(() => {
        // try stored order first
        const stored = readStoredOrder(storageKey, items);
        if (stored && stored.length > 0) {
            // append any missing items (in case items added later)
            const merged = [...stored, ...items.filter((it) => !stored.includes(it))];
            setOrder(merged);
            return;
        }

        // No valid stored order - merge current order with incoming items:
        // keep existing order for known items, append newly introduced items
        setOrder((prev) => {
            // if prev contains exactly the same set as items (just different order) keep prev
            const prevSet = new Set(prev);
            const itemsSet = new Set(items);
            const sameSet = prev.length === items.length && [...items].every((it) => prevSet.has(it));
            if (sameSet) return prev;

            // otherwise, keep prev order for items that still exist and append new items
            const kept = prev.filter((p) => itemsSet.has(p));
            const appended = items.filter((i) => !prevSet.has(i));
            return [...kept, ...appended];
        });
    }, [items, storageKey]);

    const sensors = useSensors(useSensor(PointerSensor));

    const handleDragEnd = (event: DragEndEvent) => {
        const { active, over } = event;
        if (!over) return;
        const activeId = active.id as T;
        const overId = over.id as T;
        if (activeId !== overId) {
            const oldIndex = order.indexOf(activeId);
            const newIndex = order.indexOf(overId);
            if (oldIndex === -1 || newIndex === -1) return;
            const newOrder = arrayMove(order, oldIndex, newIndex);
            setOrder(newOrder);
            if (storageKey) {
                try {
                    localStorage.setItem(storageKey, JSON.stringify(newOrder));
                } catch {
                    // ignore storage errors
                }
            }
            onChange?.(newOrder);
        }
    };

    return (
        <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
            <SortableContext
                items={order}
                strategy={strategy === 'horizontal' ? horizontalListSortingStrategy : undefined}>
                <div
                    className={`flex ${strategy === 'horizontal' ? 'flex-row flex-wrap' : 'flex-col'} ${gap} ${className}`}>
                    {order.map((id) => (
                        <SortableItem key={id} id={id}>
                            <div className="cursor-grab">{renderItem(id)}</div>
                        </SortableItem>
                    ))}
                </div>
            </SortableContext>
        </DndContext>
    );
}
