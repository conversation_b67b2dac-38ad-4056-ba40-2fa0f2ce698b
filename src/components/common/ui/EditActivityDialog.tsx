import * as React from 'react';

import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>ooter,
    <PERSON><PERSON>Header,
    DialogTitle,
} from '@/components/common/ui/Dialog';
import { Button } from '@/components/common/ui/Button';
import { Input } from '@/components/common/ui/Input';
import { Label } from '@/components/common/ui/Label';
import { Icon } from '@/components/common/ui/Icon';
import type { EditTripActivityRequest } from '@/infrastructure/api/trip-activities/types';

type EditActivityDialogProps = {
    open: boolean;
    onOpenChange: (v: boolean) => void;
    initialData?: EditTripActivityRequest;
    onConfirm: (data: EditTripActivityRequest) => void;
    loading?: boolean;
    dir?: 'rtl' | 'ltr';
};

export default function EditActivityDialog({
    open,
    onOpenChange,
    initialData,
    onConfirm,
    loading,
    dir = 'rtl',
}: EditActivityDialogProps) {
    const [statusId] = React.useState<number | undefined>(initialData?.statusId);
    const [actionId] = React.useState<number | undefined>(initialData?.actionId);
    const [details, setDetails] = React.useState<string>(initialData?.details || '');
    const [note, setNote] = React.useState<string>(initialData?.note || '');

    const handleSubmit = () => {
        onConfirm({ statusId, actionId, details, note });
    };

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-[520px] w-full rounded-md p-4" dir={dir}>
                <DialogHeader className="border-b pb-2">
                    <DialogTitle className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                        <Icon name="edit" className="w-5 h-5 text-blue-500" />
                        <span>تعديل النشاط</span>
                    </DialogTitle>
                </DialogHeader>

                {/* Form */}
                <div className="grid gap-3 mt-3">
                    <div>
                        <Label htmlFor="details">التفاصيل</Label>
                        <textarea
                            id="details"
                            value={details}
                            onChange={(e) => setDetails(e.target.value)}
                            className="h-[80px] w-full border border-gray-300 rounded-md p-2 resize-none text-sm"
                        />
                    </div>

                    <div>
                        <Label htmlFor="note">ملاحظات</Label>
                        <Input id="note" type="text" value={note} onChange={(e) => setNote(e.target.value)} />
                    </div>
                </div>

                <DialogFooter className="flex justify-end gap-2 mt-4">
                    <DialogClose asChild>
                        <Button variant="outline">إلغاء</Button>
                    </DialogClose>
                    <Button type="button" onClick={handleSubmit} disabled={loading} className="bg-blue-600 text-white">
                        {loading ? 'جارٍ الحفظ...' : 'حفظ'}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}
