/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';

import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/common/ui/Dialog';
import { Button } from '@/components/common/ui/Button';
import { useActivityLogsStore } from '@/stores/activity-logs.store';
import type { GetActivityLogsQueryParams } from '@/infrastructure/api/activities-logs/types';
import { formatLocalizedDate } from '@/shared/utils/format-date.utils';

type ActivityLogsDialogProps = {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    title?: string;
    params?: Partial<GetActivityLogsQueryParams>;
    showDeleteReason?: boolean;
};

export function ActivityLogsDialog({
    open,
    onOpenChange,
    title,
    params,
    showDeleteReason = false,
}: ActivityLogsDialogProps) {
    const { t, i18n } = useTranslation();

    const logs = useActivityLogsStore((s) => s.logs);
    const isLoading = useActivityLogsStore((s) => s.isLoading);
    const loadActivityLogs = useActivityLogsStore((s) => s.loadActivityLogs);

    // دمج القيم الافتراضية مع الـ props مرة واحدة
    const effectiveParams = useMemo<Partial<GetActivityLogsQueryParams>>(
        () => ({
            ...(params ?? {}),
        }),
        [params],
    );

    useEffect(() => {
        if (open) {
            loadActivityLogs(effectiveParams);
        }
    }, [open, effectiveParams, loadActivityLogs]);

    // inside renderBeforeAfter
    const renderBeforeAfter = (log: { before: any; after: any }) => (
        <div className="overflow-auto">
            <table
                className="
        w-70px table-fixed border-collapse text-sm text-gray-700
      ">
                <thead className="bg-gray-100 text-gray-800">
                    <tr>
                        <th className="w-[15%] px-2 py-2 text-center font-semibold border border-gray-200">
                            {t('activityLogs.rowTitle')}
                        </th>
                        <th className="w-[45%] px-2 py-2 text-center font-semibold border border-gray-200">
                            {t('activityLogs.details')}
                        </th>
                        <th className="w-[20%] px-2 py-2 text-center font-semibold border border-gray-200">
                            {t('activityLogs.status')}
                        </th>
                        <th className="w-[20%] px-2 py-2 text-center font-semibold border border-gray-200">
                            {t('activityLogs.action')}
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr className="odd:bg-white even:bg-gray-50">
                        <td className="px-2 py-2 text-center font-medium border border-gray-200">
                            {t('activityLogs.before')}
                        </td>
                        <td className="px-2 py-2 border border-gray-200 text-left">{log.before?.details ?? ''}</td>
                        <td className="px-2 py-2 border border-gray-200 text-center">
                            {log.before?.status?.arabic ?? ''}
                        </td>
                        <td className="px-2 py-2 border border-gray-200 text-center">
                            {log.before?.action?.arabic ?? ''}
                        </td>
                    </tr>
                    <tr className="odd:bg-white even:bg-gray-50">
                        <td className="px-2 py-2 text-center font-medium border border-gray-200">
                            {t('activityLogs.after')}
                        </td>
                        <td className="px-2 py-2 border border-gray-200 text-left">{log.after?.details ?? ''}</td>
                        <td className="px-2 py-2 border border-gray-200 text-center">
                            {log.after?.status?.arabic ?? ''}
                        </td>
                        <td className="px-2 py-2 border border-gray-200 text-center">
                            {log.after?.action?.arabic ?? ''}
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    );

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-[90vw] w-full max-h-[90vh] rounded-lg p-4" dir={i18n.dir()}>
                <DialogHeader className="border-b pb-2 flex items-center justify-between">
                    <DialogTitle className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                        <span>{title ?? t('activityLogs.deletedActivities')}</span>
                    </DialogTitle>
                </DialogHeader>

                <div
                    className="_table_container flex-grow mt-4"
                    style={{ overflowY: 'auto', maxHeight: 'calc(100vh - 200px)' }}>
                    <DataTable
                        value={logs}
                        loading={isLoading}
                        scrollable
                        scrollHeight="100%"
                        size="small"
                        tableStyle={{ tableLayout: 'fixed', width: '100%' }} // ← keep widths stable
                        className="shadow-md border border-gray-200"
                        emptyMessage={isLoading ? t('loading') : t('common.noDataExist')}>
                        {/* 14% */}
                        <Column
                            field="actionType"
                            header={t('tripActivities.action')}
                            headerStyle={{ width: '14%', textAlign: 'center' }}
                            bodyClassName="text-center"
                        />

                        {/* 16% */}
                        <Column
                            field="user.name"
                            header={t('activityLogs.user')}
                            headerStyle={{ width: '16%', textAlign: 'center' }}
                            bodyClassName="text-center"
                        />

                        {/* 15% */}
                        <Column
                            field="timestamp"
                            header={t('common.timestamp')}
                            body={(row) => formatLocalizedDate(row.timestamp)}
                            headerStyle={{ width: '15%', textAlign: 'center' }}
                            bodyClassName="text-center"
                        />

                        {/* 40% — the wide column for details/beforeAfter */}
                        <Column
                            header={t('activityLogs.beforeAfter')}
                            body={renderBeforeAfter}
                            headerStyle={{ width: '40%', textAlign: 'center' }}
                            bodyStyle={{
                                whiteSpace: 'normal',
                                wordBreak: 'break-word',
                                overflowWrap: 'anywhere',
                            }}
                            bodyClassName="text-left"
                        />

                        {/* 15% */}
                        {showDeleteReason && (
                            <Column
                                field="deleteReason"
                                header={t('activityLogs.deleteReason')}
                                headerStyle={{ width: '15%', textAlign: 'center' }}
                                bodyStyle={{
                                    whiteSpace: 'normal',
                                    wordBreak: 'break-word',
                                    overflowWrap: 'anywhere',
                                }}
                                bodyClassName="text-center"
                            />
                        )}
                    </DataTable>
                </div>

                <DialogFooter className="flex justify-end mt-4">
                    <Button onClick={() => onOpenChange(false)} variant="outline">
                        {t('common.close')}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}
