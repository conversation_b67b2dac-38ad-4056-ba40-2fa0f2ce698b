import React, { useState } from 'react';
import { useParams } from 'react-router-dom';

import { DialogTrigger } from '@/components/common/ui/Dialog';

import ActivityLogDialog from './ActivityLogDialog';

export default function CreateActivityDialog({ children }: { children?: React.ReactNode }) {
    const { id } = useParams();
    const tripId = Number(id);
    const [open, setOpen] = useState(false);

    return (
        <>
            <DialogTrigger asChild onClick={() => setOpen(true)}>
                {children}
            </DialogTrigger>

            <ActivityLogDialog
                mode="create"
                open={open}
                onOpenChange={setOpen}
                tripId={tripId}
                onSuccess={() => setOpen(false)}
            />
        </>
    );
}
