import * as React from 'react';
import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';

import { cn } from '@/shared/utils/class-name.utils';

const infoRowVariants = cva('table-row text-xs', {
    variants: {
        size: {
            md: 'py-2',
            sm: 'py-1 text-sm',
        },
    },
    defaultVariants: {
        size: 'md',
    },
});

// 📌 label cell with RTL/LTR spacing
const labelVariants = cva('table-cell font-medium whitespace-nowrap', {
    variants: {
        align: {
            start: 'text-start ltr:pr-2 rtl:pl-2',
            end: 'text-end ltr:pl-2 rtl:pr-2',
        },
    },
    defaultVariants: { align: 'start' },
});

// 📌 value cell with default + link colors
const valueVariants = cva('table-cell truncate font-bold', {
    variants: {
        link: {
            true: 'text-[#179FCA] cursor-pointer hover:underline',
            false: 'text-[#292929]', // ✅ default value color
        },
    },
    defaultVariants: { link: false },
});

export type InfoRowProps = React.ComponentProps<'div'> &
    VariantProps<typeof infoRowVariants> & {
        label: React.ReactNode;
        value: React.ReactNode;
        isLink?: boolean;
        href?: string;
        onClick?: (e: React.MouseEvent) => void;
        asChild?: boolean;
        labelAlign?: 'start' | 'end';
    };

export const InfoRow = React.forwardRef<HTMLDivElement, InfoRowProps>(
    (
        {
            label,
            value,
            isLink = false,
            href,
            onClick,
            asChild = false,
            labelAlign = 'start',
            size,
            className,
            ...rest
        },
        ref,
    ) => {
        const Comp = asChild ? Slot : 'div';

        const valueContent = (
            <span
                role={isLink || href ? 'link' : undefined}
                tabIndex={isLink || href ? 0 : undefined}
                onClick={onClick}
                className={cn(valueVariants({ link: Boolean(isLink || href) }))}>
                {value}
            </span>
        );

        return (
            <Comp ref={ref as React.Ref<HTMLDivElement>} className={cn(infoRowVariants({ size }), className)} {...rest}>
                <span
                    className={cn(
                        labelVariants({ align: labelAlign }),
                        'text-[#7C7C7C]', // ✅ default label color
                    )}>
                    {label}
                </span>

                {href ? (
                    <a href={href} onClick={onClick} className={cn(valueVariants({ link: true }))}>
                        {value}
                    </a>
                ) : (
                    valueContent
                )}
            </Comp>
        );
    },
);

InfoRow.displayName = 'InfoRow';
export default InfoRow;
