import { useTranslation } from 'react-i18next';

import { Icon } from './Icon';

export default function ComingSoon() {
    const { t } = useTranslation();

    return (
        <div className="max-w-[500px] grid place-items-center m-auto h-[100%] min-h-[400px]">
            <div className="text-center space-y-6">
                {/* Icon */}
                <div className="flex justify-center">
                    <div className="w-24 h-24 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg">
                        <Icon name="notification" className="w-12 h-12 text-white" />
                    </div>
                </div>

                {/* Title */}
                <div className="space-y-2">
                    <h2 className="text-3xl font-bold text-gray-800 dark:text-gray-200">{t('common.comingSoon')}</h2>
                    <div className="w-16 h-1 bg-gradient-to-r from-blue-500 to-purple-600 mx-auto rounded-full"></div>
                </div>

                {/* Description */}
                <p className="text-lg text-gray-600 dark:text-gray-400 max-w-md mx-auto leading-relaxed">
                    {t('common.featureInDevelopment')}
                </p>

                {/* Decorative elements */}
                <div className="flex justify-center space-x-2 pt-4">
                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                    <div
                        className="w-2 h-2 bg-purple-500 rounded-full animate-pulse"
                        style={{ animationDelay: '0.2s' }}></div>
                    <div
                        className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"
                        style={{ animationDelay: '0.4s' }}></div>
                </div>
            </div>
        </div>
    );
}
