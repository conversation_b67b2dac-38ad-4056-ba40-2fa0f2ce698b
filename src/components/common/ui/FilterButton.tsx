import { useTranslation } from 'react-i18next';

import { Button } from './Button';
import { Icon } from './Icon';

interface FilterButtonProps {
    onClick: () => void;
    className?: string;
}

const FilterButton: React.FC<FilterButtonProps> = ({ onClick, className }) => {
    const { t } = useTranslation();

    return (
        <Button
            variant="secondary"
            className={`p-button-outlined bg-white border !border-green-600 rounded-sm px-4 py-2 ${className ?? ''}`}
            onClick={onClick}>
            <Icon name="filter" className="w-7 h-7 text-green-700" />
            <p className="text-green-700">{t('common.filter')}</p>
        </Button>
    );
};

export default FilterButton;
