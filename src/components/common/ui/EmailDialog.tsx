import { Editor } from 'primereact/editor';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import ReactDOMServer from 'react-dom/server';

import { logger } from '@/infrastructure/logging';
import { showError, showSuccess } from '@/components/common/ui/Toast';
import type { Scope } from '@/shared/enums';
import { Button } from '@/components/common/ui/Button';
import {
    Dialog,
    DialogClose,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/common/ui/Dialog';
import { Input } from '@/components/common/ui/Input';
import { Label } from '@/components/common/ui/Label';
import { useEmailStore } from '@/stores/email.store';

import { Icon } from './Icon';

interface EmailDialogProps {
    children?: React.ReactNode;
    scope: Scope; // Trip=1, TripAlert=2
    emailBody?: string | React.ReactElement | undefined;
}

export default function EmailDialog({ children, scope, emailBody }: EmailDialogProps) {
    const { t } = useTranslation();
    const [isOpen, setIsOpen] = useState(false);
    const [htmlContent, setHtmlContent] = useState<string>('');
    const [to, setToValue] = useState<string>('');
    const [cc, setCcValue] = useState<string>('');
    const [subject, setSubjectValue] = useState<string>('');
    const [isSending, setIsSending] = useState<boolean>(false);

    const { setTo, setCc, setSubject, setBody, setScope, sendEmail } = useEmailStore();

    useEffect(() => {
        if (!emailBody) return;

        if (typeof emailBody === 'string') {
            setHtmlContent(emailBody);
            setBody(emailBody);
        } else {
            const html = ReactDOMServer.renderToStaticMarkup(emailBody);
            setHtmlContent(html);
            setBody(html);
        }
    }, [emailBody, setBody]);

    /** Validate input fields before sending */
    const validateFields = (): boolean => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

        if (!to || !emailRegex.test(to)) {
            showError(t('common.error'), t('validation.emailInvalid'));
            return false;
        }

        if (cc && !emailRegex.test(cc)) {
            showError(t('common.error'), t('validation.emailInvalid'));
            return false;
        }

        if (!subject.trim()) {
            showError(t('common.error'), t('validation.subjectRequired'));
            return false;
        }

        if (!htmlContent.trim()) {
            showError(t('common.error'), t('validation.descriptionRequired'));
            return false;
        }

        return true;
    };

    /** Handle form submission */
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!validateFields()) return;
        try {
            setIsSending(true);
            setScope(scope);
            setTo([to]);
            if (cc) setCc([cc]);
            setSubject(subject);
            setBody(htmlContent);

            await sendEmail();

            showSuccess(t('common.success'), t('emailDialog.sendSuccess'));
            setIsOpen(false);
        } catch (error) {
            logger.error('[ EmailDialog.handleSubmit] Error occurred while sending email. Error: ', error as Error);
            showError(t('common.error'), t('emailDialog.sendFailed'));
        } finally {
            setIsSending(false);
        }
    };

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>{children}</DialogTrigger>
            <DialogDescription></DialogDescription>
            <DialogContent className="sm:max-w-[500px] md:min-w-[700px] lg:min-w-[1000px]">
                <form onSubmit={handleSubmit}>
                    <DialogHeader className="mb-3">
                        <DialogTitle>
                            <div className="flex items-center gap-3 text-green-700">
                                <Icon name="email" className="size-6" />
                                {t('emailDialog.title')}
                            </div>
                        </DialogTitle>
                    </DialogHeader>

                    <div className="grid gap-4 py-4">
                        <div className="grid gap-2">
                            <Label htmlFor="to">{t('emailDialog.email')}</Label>
                            <Input
                                id="to"
                                type="email"
                                value={to}
                                onChange={(e) => setToValue(e.target.value)}
                                placeholder={t('emailDialog.recipientEmail')}
                                required
                            />
                        </div>

                        <div className="grid gap-2">
                            <Label htmlFor="cc">{t('emailDialog.cc')}</Label>
                            <Input
                                id="cc"
                                type="email"
                                value={cc}
                                onChange={(e) => setCcValue(e.target.value)}
                                placeholder={t('emailDialog.ccPlaceholder')}
                            />
                        </div>

                        <div className="grid gap-2">
                            <Label htmlFor="subject">{t('emailDialog.subject')}</Label>
                            <Input
                                id="subject"
                                type="text"
                                value={subject}
                                onChange={(e) => setSubjectValue(e.target.value)}
                                placeholder={t('emailDialog.subjectPlaceholder')}
                                required
                            />
                        </div>

                        <div className="grid gap-2">
                            <Label htmlFor="description" className="mb-1">
                                {t('emailDialog.descriptionPlaceholder')}
                            </Label>
                            <Editor
                                dir="ltr"
                                placeholder={t('emailDialog.descriptionPlaceholder')}
                                onTextChange={(e) => setHtmlContent(e.htmlValue ?? '')}
                                style={{ height: '200px' }}
                                value={htmlContent}
                            />
                        </div>
                    </div>

                    <DialogFooter>
                        <DialogClose asChild>
                            <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
                                {t('common.cancel')}
                            </Button>
                        </DialogClose>

                        <Button
                            type="submit"
                            disabled={isSending}
                            className="flex text-white rounded-md px-4 py-2 bg-green-700 disabled:opacity-60">
                            {isSending ? t('common.sending') : t('common.send')}
                        </Button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    );
}
