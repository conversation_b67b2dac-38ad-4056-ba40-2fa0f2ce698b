import { Icon, type IconName } from './Icon';

export interface SummaryCardDetails {
    title: string;
    iconName: IconName;
    value: number | string;
    iconColor?: string;
    iconBgColor?: string;
}

export default function SummaryCard({ details, minWidth = 260 }: { details: SummaryCardDetails; minWidth?: number }) {
    return (
        <div className="flex items-center gap-2 p-2 px-3 border rounded-[8px] w-fit" style={{ minWidth }}>
            <span className={`flex items-center justify-center rounded-full w-15 h-15 bg-[${details.iconBgColor}]`}>
                <Icon
                    name={details.iconName}
                    className={`w-8 h-8 flex items-center justify-center text-[${details.iconColor}]`}
                />
            </span>
            <div>
                <p className="mb-1 text-[#7C7C7C] text-[16px]">{details.title} </p>
                <p className=" text-[20px] font-semibold whitespace-nowrap max-w-[225px]"> {details.value} </p>
            </div>
        </div>
    );
}
