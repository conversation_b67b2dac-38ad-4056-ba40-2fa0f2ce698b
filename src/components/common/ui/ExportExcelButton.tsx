// components/common/ExportExcelButton.tsx
import type { WorkBook, WorkSheet } from 'xlsx';
import { utils, write } from 'xlsx';
import { saveAs } from 'file-saver';

import { Button } from '@/components/common/ui/Button';
import ExcelIcon from '@/assets/imgs/svg/excel.svg';

export interface ExcelSheetTable {
    title?: string;
    columns: string[];
    rows: (string | number | boolean)[][];
}

export interface ExportExcelButtonProps {
    tables: ExcelSheetTable[];
    fileName: string;
    className?: string;
    sheetName?: string;
    horizontal?: boolean; // if true => place tables side-by-side, else stacked vertically
    rtl?: boolean;
    columnWidth?: number | null; // if provided, use fixed width for all columns; if null/undefined => auto width
    minColWidth?: number; // min width for auto (wch)
    maxColWidth?: number; // max width for auto (wch)
}

/**
 * ExportExcelButton
 *
 * Reusable exporter: supports multiple tables, merged titles, headers styling,
 * striped rows, borders, alignment, RTL reading order, fixed/auto column widths,
 * and print page setup.
 */
export const ExportExcelButton = ({
    tables,
    fileName,
    className = '',
    sheetName = 'Sheet1',
    horizontal = false,
    rtl = false,
    columnWidth = null,
    minColWidth = 10,
    maxColWidth = 60,
}: ExportExcelButtonProps) => {
    const exportExcel = () => {
        if (!tables?.length) return;

        // create empty worksheet
        const worksheet: WorkSheet = utils.aoa_to_sheet([]);

        // ensure !cols exists so we can set by index
        worksheet['!cols'] = worksheet['!cols'] || [];

        // position trackers
        let currentRow = 0;
        let currentCol = 0;

        // spacing (only on the axis depending on layout)
        const rowGap = horizontal ? 0 : 1; // vertical stacking -> small gap
        const colGap = horizontal ? 2 : 0; // horizontal layout -> small gap

        tables.forEach((table) => {
            const columns = table.columns;
            const rows = table.rows;
            const totalCols = columns.length;

            // start positions depend on layout
            const startRow = horizontal ? 0 : currentRow;
            const startCol = horizontal ? currentCol : 0;

            let writeRow = startRow;

            // ----- Title (merged) -----
            if (table.title) {
                // write title value
                utils.sheet_add_aoa(worksheet, [[table.title]], { origin: { r: writeRow, c: startCol } });

                // add merge across columns of this table
                worksheet['!merges'] = worksheet['!merges'] || [];
                worksheet['!merges'].push({
                    s: { r: writeRow, c: startCol },
                    e: { r: writeRow, c: startCol + totalCols - 1 },
                });

                // style title cell: bold, larger, centered, light background
                const titleCell = worksheet[utils.encode_cell({ r: writeRow, c: startCol })];
                if (titleCell) {
                    titleCell.s = {
                        font: { bold: true, sz: 14 },
                        alignment: { horizontal: 'center', vertical: 'center', readingOrder: rtl ? 2 : 1 },
                        fill: { fgColor: { rgb: 'FCE4D6' } }, // light tint
                        border: {
                            top: { style: 'thin', color: { rgb: '000000' } },
                            bottom: { style: 'thin', color: { rgb: '000000' } },
                            left: { style: 'thin', color: { rgb: '000000' } },
                            right: { style: 'thin', color: { rgb: '000000' } },
                        },
                    };
                }
                writeRow += 1;
            }

            // ----- Header row -----
            utils.sheet_add_aoa(worksheet, [columns], { origin: { r: writeRow, c: startCol } });

            // style header cells (alignment, bold, header fill, border)
            for (let c = 0; c < totalCols; c++) {
                const cellRef = utils.encode_cell({ r: writeRow, c: startCol + c });
                const cell = worksheet[cellRef];
                if (!cell) continue;
                cell.s = {
                    font: { bold: true },
                    fill: { fgColor: { rgb: 'D9E1F2' } }, // light blue header
                    alignment: {
                        horizontal: rtl ? 'right' : 'center',
                        vertical: 'center',
                        wrapText: true,
                        readingOrder: rtl ? 2 : 1,
                    },
                    border: {
                        top: { style: 'thin', color: { rgb: '000000' } },
                        bottom: { style: 'thin', color: { rgb: '000000' } },
                        left: { style: 'thin', color: { rgb: '000000' } },
                        right: { style: 'thin', color: { rgb: '000000' } },
                    },
                };
            }

            writeRow += 1;

            // ----- Data rows -----
            utils.sheet_add_aoa(worksheet, rows, { origin: { r: writeRow, c: startCol } });

            for (let r = 0; r < rows.length; r++) {
                const absoluteRow = writeRow + r;
                const isStriped = r % 2 === 1; // stripe every second row
                for (let c = 0; c < totalCols; c++) {
                    const cellRef = utils.encode_cell({ r: absoluteRow, c: startCol + c });
                    const cell = worksheet[cellRef];
                    if (!cell) continue;
                    cell.s = {
                        alignment: {
                            horizontal: 'center',
                            vertical: 'center',
                            wrapText: true,
                            readingOrder: rtl ? 2 : 1,
                        },
                        fill: { fgColor: { rgb: isStriped ? 'F2F2F2' : 'FFFFFF' } },
                        border: {
                            top: { style: 'thin', color: { rgb: '000000' } },
                            bottom: { style: 'thin', color: { rgb: '000000' } },
                            left: { style: 'thin', color: { rgb: '000000' } },
                            right: { style: 'thin', color: { rgb: '000000' } },
                        },
                    };
                }
            }

            // ----- Column widths (fixed or auto) -----
            if (columnWidth && typeof columnWidth === 'number') {
                // set fixed width for each column of this table at correct index
                for (let i = 0; i < totalCols; i++) {
                    const colIndex = startCol + i;
                    const cols = worksheet['!cols'] || [];
                    cols[colIndex] = { wch: columnWidth };
                    worksheet['!cols'] = cols;
                }
            } else {
                // auto-calc widths for these columns and set
                for (let i = 0; i < totalCols; i++) {
                    const colIndex = startCol + i;
                    const headerLen = columns[i] ? String(columns[i]).length : 0;
                    let maxLen = headerLen;
                    for (let r = 0; r < rows.length; r++) {
                        const val = rows[r][i];
                        const len = val !== undefined && val !== null ? String(val).length : 0;
                        if (len > maxLen) maxLen = len;
                    }
                    const wch = Math.min(Math.max(maxLen + 2, minColWidth), maxColWidth); // add small padding
                    const cols = worksheet['!cols'] || [];
                    cols[colIndex] = { wch };
                    worksheet['!cols'] = cols;
                }
            }

            // compute occupied rows count
            const occupiedRows = (table.title ? 1 : 0) + 1 + rows.length; // title + header + data

            // update positions for next table based on layout only
            if (horizontal) {
                currentCol += totalCols + colGap; // shift right
            } else {
                currentRow += occupiedRows + rowGap; // shift down
            }
        });

        // Page setup (print-friendly)
        worksheet['!pageSetup'] = {
            orientation: 'landscape',
            fitToWidth: 1,
            fitToHeight: 1,
            horizontalCentered: true,
            verticalCentered: true,
        };

        // Build workbook and save
        const workbook: WorkBook = { Sheets: { [sheetName]: worksheet }, SheetNames: [sheetName] };
        const excelBuffer = write(workbook, { bookType: 'xlsx', type: 'array' });
        const blob = new Blob([excelBuffer], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8',
        });
        saveAs(blob, `${fileName}.xlsx`);
    };

    return (
        <Button
            onClick={exportExcel}
            variant="secondary"
            disabled={!tables?.length}
            className={`p-button-outlined bg-white border !border-green-600 rounded-sm px-4 py-2 ${className}`}>
            <img src={ExcelIcon} alt="excel" className="w-7 h-7 text-green-700" />
        </Button>
    );
};
