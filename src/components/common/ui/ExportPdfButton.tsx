import React, { useState } from 'react';
import { pdf, Page, Document, StyleSheet, Font, Text, View } from '@react-pdf/renderer';
import { t } from 'i18next';

import { Button } from '@/components/common/ui/Button';
import PdfIcon from '@/assets/imgs/svg/pdf.svg';
import cairoFont from '@/assets/fonts/Cairo/Cairo-VariableFont_slnt,wght.ttf';
import type { TripDetail } from '@/infrastructure/api/trips/types';
import type { TripAlert } from '@/infrastructure/api/trip-alerts/types';
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogDescription,
    DialogClose,
} from '@/components/common/ui/Dialog';
import { appConfig } from '@/shared/config/app-settings.config';
import type { TripStop } from '@/infrastructure/api/trip-stops/types';
import type { TripActivity } from '@/infrastructure/api/trip-activities/types';

import { SaudiCustomsTuHeaderPDF } from '../pdf-templates/SaudiCustomsTuHeaderPDF';
import { AlertsSectionPDF } from '../pdf-templates/AlertsSectionPDF';
import { TripDetailsSectionPDF } from '../pdf-templates/TripDetailsSectionPDF';
import { StopsSectionPDF } from '../pdf-templates/StopsSectionPDF';
import { ActivitiesSectionPDF } from '../pdf-templates/ActivitiesSectionPDF';

import { Icon } from './Icon';

Font.register({
    family: 'Cairo',
    src: cairoFont,
});

const styles = StyleSheet.create({
    page: {
        padding: 20,
        flexDirection: 'column',
        backgroundColor: '#fff',
        fontFamily: 'Cairo',
    },
    footer: {
        marginTop: 10,
        fontSize: 8,
        textAlign: 'left',
        color: '#666',
        borderTop: '1px solid #ccc',
        paddingTop: 4,
    },
});

interface TripPdfDocumentProps {
    tripDetail: TripDetail;
    tripAlerts?: TripAlert[];
    tripStops?: TripStop[];
    tripActivities?: TripActivity[];
}

const TripPdfDocument = ({ tripDetail, tripAlerts, tripStops, tripActivities }: TripPdfDocumentProps) => {
    const currentDateTime = new Date().toLocaleString('en-GB', {
        dateStyle: 'short',
        timeStyle: 'short',
    });

    return (
        <Document>
            <Page size="A4" orientation="landscape" style={styles.page}>
                {/* Header Section */}
                <SaudiCustomsTuHeaderPDF />
                {/* Trip Details */}
                <TripDetailsSectionPDF tripDetail={tripDetail} />
            </Page>
            <Page size="A4" orientation="landscape" style={styles.page}>
                {/* Alerts Section */}
                {tripAlerts && tripAlerts?.length > 0 && <AlertsSectionPDF alerts={tripAlerts} />}
                {/* Stops Section */}
                {tripStops && tripStops?.length > 0 && <StopsSectionPDF stops={tripStops} />}
                {/* Activities Section */}
                {tripActivities && tripActivities?.length > 0 && <ActivitiesSectionPDF activities={tripActivities} />}
                {/* Footer with Date & Time */}
                <View style={styles.footer}>
                    <Text>Printed on: {currentDateTime}</Text>
                </View>
            </Page>
        </Document>
    );
};

interface ExportPdfButtonProps {
    tripDetail: TripDetail | null;
    tripAlerts?: TripAlert[];
    tripStops?: TripStop[];
    tripActivities?: TripActivity[];
    fileName: string;
    className?: string;
}

export const ExportPdfButton: React.FC<ExportPdfButtonProps> = ({
    tripDetail,
    tripAlerts,
    tripStops,
    tripActivities,
    fileName,
    className = '',
}) => {
    const [loading, setLoading] = useState(false);
    const [pdfUrl, setPdfUrl] = useState<string | null>(null);
    const [isOpen, setIsOpen] = useState(false);

    const previewEnabled = appConfig.get('pdfPreviewEnabled');

    const generatePdfBlob = async () => {
        if (!tripDetail) return null;
        const blob = await pdf(
            <TripPdfDocument
                tripDetail={tripDetail}
                tripAlerts={tripAlerts}
                tripStops={tripStops}
                tripActivities={tripActivities}
            />,
        ).toBlob();
        return URL.createObjectURL(blob);
    };

    const handleClick = async () => {
        if (!tripDetail) return;
        setLoading(true);

        const url = await generatePdfBlob();
        if (!url) return;

        if (previewEnabled) {
            setPdfUrl(url);
            setIsOpen(true);
        } else {
            const a = document.createElement('a');
            a.href = url;
            a.download = fileName || 'trip-details.pdf';
            a.click();
            URL.revokeObjectURL(url);
        }

        setLoading(false);
    };

    return (
        <>
            <Button
                onClick={handleClick}
                variant="secondary"
                disabled={loading}
                className={`p-button-outlined bg-white border !border-green-600 text-green-700 rounded-sm px-4 py-2 ${className}`}>
                <img src={PdfIcon} alt="pdf" className="w-7 h-7 text-green-700" />
                {loading ? t('common.preparing') : previewEnabled ? t('common.PDFPreview') : t('common.downloadPDF')}
            </Button>

            {previewEnabled && (
                <Dialog open={isOpen} onOpenChange={setIsOpen}>
                    <DialogContent className="filter-container w-full max-w-[80vw] max-h-[100vh] h-[98vh] p-0 rounded-2xl shadow-lg">
                        <DialogHeader className="px-6 py-3 pb-3 border-b-2">
                            <DialogTitle className="text-xl flex items-center gap-3">
                                <div className="flex items-center gap-1 text-green-700">
                                    <Icon name="pdf" className="w-6 h-6 text-red-700" />
                                    <span>{t('common.PDFPreview')}</span>
                                </div>
                            </DialogTitle>
                        </DialogHeader>
                        <DialogDescription />

                        {pdfUrl ? (
                            <iframe src={pdfUrl} className="w-full flex-1 border rounded-md" title="Trip PDF Preview" />
                        ) : (
                            <p className="text-center text-gray-500">Loading preview...</p>
                        )}

                        <div className="flex justify-end gap-2 m-2">
                            <DialogClose asChild>
                                <Button variant="outline"> {t('common.close')}</Button>
                            </DialogClose>
                            <Button
                                variant="secondary"
                                className="p-button-outlined bg-green-700 !border-green-700 rounded-sm px-4 py-2 hover:bg-green-800 text-white"
                                onClick={() => {
                                    if (pdfUrl) {
                                        const a = document.createElement('a');
                                        a.href = pdfUrl;
                                        a.download = fileName || 'trip-details.pdf';
                                        a.click();
                                    }
                                }}>
                                {t('common.download')}
                            </Button>
                        </div>
                    </DialogContent>
                </Dialog>
            )}
        </>
    );
};
