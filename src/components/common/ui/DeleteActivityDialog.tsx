// src/components/common/ui/DeleteActivityDialog.tsx
import { t } from 'i18next';

import {
    Dialog,
    DialogClose,
    DialogContent,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from '@/components/common/ui/Dialog';
import { Button } from '@/components/common/ui/Button';
import { Icon } from '@/components/common/ui/Icon';

type DeleteActivityDialogProps = {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    reason: string;
    onReasonChange: (v: string) => void;
    onConfirm: () => void;
    loading?: boolean;
    dir?: 'rtl' | 'ltr';
    title?: string; // default: "سبب حذف النشاط"
    placeholder?: string; // default: "قم بإضافة سبب حذف النشاط"
    confirmLabel?: string; // default: "حذف"
    cancelLabel?: string; // default: "إغلاق"
    dashed?: boolean; // keep visual as in screenshot
};

export default function DeleteActivityDialog({
    open,
    onOpenChange,
    reason,
    onReasonChange,
    onConfirm,
    loading = false,
    dir = 'rtl',
    title = t('tripActivities.deleteReason'),
    placeholder = t('tripActivities.deleteReasonPlaceholder'),
    confirmLabel = t('common.delete'),
    cancelLabel = t('common.cancel'),
    dashed = true,
}: DeleteActivityDialogProps) {
    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-[520px] w-full rounded-md p-4" dir={dir}>
                <DialogHeader className="border-b pb-2">
                    <DialogTitle className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                        <Icon name="delete" className="w-5 h-5 text-red-500" />
                        <span>{title}</span>
                    </DialogTitle>
                </DialogHeader>

                <div className="mt-3">
                    <textarea
                        value={reason}
                        onChange={(e) => onReasonChange(e.target.value)}
                        placeholder={placeholder}
                        className={`h-[110px] w-full border ${dashed ? 'border-dashed' : ''} border-gray-300 rounded-md p-2 resize-none text-sm`}
                    />
                </div>

                <DialogFooter className="flex justify-end gap-2 mt-4">
                    <DialogClose asChild>
                        <Button
                            type="button"
                            variant="outline"
                            className="bg-white border border-gray-400 text-gray-700 rounded-sm px-4 py-2"
                            disabled={loading}>
                            {cancelLabel}
                        </Button>
                    </DialogClose>

                    <Button
                        type="button"
                        onClick={onConfirm}
                        disabled={loading || !reason.trim()}
                        className="bg-red-600 hover:bg-red-700 text-white rounded-sm px-4 py-2">
                        {loading ? 'جارِ الحذف…' : confirmLabel}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}
