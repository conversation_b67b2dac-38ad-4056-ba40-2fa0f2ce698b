/**
 * Icon Component
 * --------------
 * A reusable SVG-based Icon component that integrates with `vite-plugin-svg-icons`.
 *
 * ## Features:
 * - Dynamically renders an SVG `<use>` reference from the sprite generated by `vite-plugin-svg-icons`.
 * - Supports custom `className` for styling (size, color, margin, etc.).
 * - Defaults to a consistent style if no custom class is provided.
 * - Uses `currentColor` for `fill`, making the icon automatically adapt to the text color (`color` CSS property).
 *
 * ## Usage:
 * ```tsx
 * <Icon name="shipment-tracking" />
 * <Icon name="alert-01" className="w-8 h-8 text-red-600" />
 * <Icon name="filter" className="w-6 h-6" />
 * ```
 */

export type IconName =
    | 'tripPings'
    | 'ackAlert'
    | 'movementReport'
    | 'activitiesReport'
    | 'truck-3'
    | 'email'
    | 'endTrip'
    | 'excel'
    | 'target'
    | 'notification'
    | 'notificationMuted'
    | 'noMap'
    | 'mapLocation'
    | 'charging'
    | 'enteringGeoFence'
    | 'leavingGeofence'
    | 'policeCheckPointPin'
    | 'truck'
    | 'airPortPin'
    | 'checkPointPin'
    | 'filter'
    | 'seaPortPin'
    | 'alert'
    | 'landPortPin'
    | 'shipmentTracking'
    | 'tripPin'
    | 'batteryCharging'
    | 'batteryEmpty'
    | 'batteryFull'
    | 'batteryLow'
    | 'batteryMedium'
    | 'batteryVeryLow'
    | 'charger'
    | 'chargerOn'
    | 'gpsDisconnected'
    | 'gpsSignal'
    | 'gpsSignalFull'
    | 'gpsSignalWeak'
    | 'signalFull'
    | 'signalMedium'
    | 'signalLowMedium'
    | 'signalLow'
    | 'signalNo'
    | 'customCheckpoint'
    | 'customPoliceStation'
    | 'ship'
    | 'plane'
    | 'truck-2'
    | 'lock'
    | 'speed'
    | 'print'
    | 'pdf'
    | 'print'
    | 'createActivity'
    // New Alert Icons
    | 'alertPin'
    | 'tripRoute'
    | 'truckStopped'
    | 'trackerTamper'
    | 'trackerDropped'
    | 'lockTamper'
    | 'lockOpen'
    | 'lockConnectionLost'
    | 'trackerBatteryLow'
    | 'lockBatteryLow'
    | 'gsmSignalLost'
    | 'gpsSignalLost'
    | 'entringGeofence'
    | 'leavingGeofence'
    | 'trackerConnectionLost'
    | 'tripDistanceExceeded'
    | 'tripTimeExceeded'
    | 'overSpeeding'
    | 'wrongDirection'
    | 'suspiciousArea'
    | 'activity'
    | 'fourHourExceeded'
    | 'viewLog'
    | 'edit'
    | 'delete'
    | 'play'
    | 'pause'
    | 'fastForward'
    | 'rewind'
    | 'locationPin'
    | 'activityMarker'
    | 'enteringGeofenceMarker'
    | 'fourHourStopMarker'
    | 'gpsSignalLostMarker'
    | 'gsmSignalLostMarker'
    | 'leavingGeofenceMarker'
    | 'lockBatteryLowMarker'
    | 'lockConnectionLostMarker'
    | 'lockOpenMarker'
    | 'lockTamperMarker'
    | 'overspeedingMarker'
    | 'stoppedTruckMarker'
    | 'suspiciousAreaMarker'
    | 'timeExceededMarker'
    | 'trackerBatteryLowMarker'
    | 'trackerConnectionLostMarker'
    | 'trackerDroppedMarker'
    | 'trackerTamperMarker'
    | 'wrongDirectionMarker'
    | 'genericAlertMarker'
    | 'clock'
    | 'notAckAlert'
    | 'ruler'
    | 'traffic'
    | 'dots'
    | 'plus'
    | 'close';

type IconProps = {
    /** The unique icon name, matching the SVG filename without extension. */
    name: IconName;

    /** Optional Tailwind or custom classes for size, color, etc. */
    className?: string;
};

export function Icon({
    name,
    className = 'w-5 h-5 text-green-700', // Default styling (size + color)
}: IconProps) {
    return (
        <svg
            className={className}
            style={{ fill: 'currentColor' }} // Ensures color adapts to CSS `color`
            aria-hidden="true" // Decorative only
        >
            <use href={`#icon-${name}`} />
        </svg>
    );
}
