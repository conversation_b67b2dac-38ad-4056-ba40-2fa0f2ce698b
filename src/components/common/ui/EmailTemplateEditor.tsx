// import React, { useRef, useEffect } from 'react';
// import type { EditorRef, EmailEditorProps } from 'react-email-editor';
// import EmailEditor from 'react-email-editor';

// interface EmailTemplateEditorProps {
//     value?: string; // JSON design أو HTML خام
//     onChange?: (html: string) => void;
//     readOnly?: boolean;
//     height?: string;
//     dir?: 'ltr' | 'rtl';
// }

// export const EmailTemplateEditor: React.FC<EmailTemplateEditorProps> = ({
//     value,
//     onChange,
//     readOnly = false,
//     height = '600px',
//     dir = 'ltr',
// }) => {
//     const editorRef = useRef<EditorRef | null>(null);

//     type ExportHtmlResult = { design: object; html: string };

//     useEffect(() => {
//         const editor = editorRef.current?.editor;
//         if (!editor || !value) return;

//         try {
//             // إذا كانت القيمة JSON تصميم صالح - نحمله
//             const design = JSON.parse(value);
//             editor.loadDesign(design);
//         } catch {
//             const fallbackDesign = {
//                 // Template-level fields (مطلوبة بحسب embed.d.ts)
//                 id: 'template-1',
//                 counters: {
//                     u_column: 1,
//                     u_row: 1,
//                     u_content_text: 1,
//                 },
//                 body: {
//                     id: 'body-1',
//                     // rows: مصفوفة BodyItem (كل BodyItem يحتاج cells, columns, values)
//                     rows: [
//                         {
//                             id: 'row-1',
//                             cells: [0], // array of cell indices
//                             values: {},
//                             columns: [
//                                 {
//                                     id: 'col-1',
//                                     values: {},
//                                     contents: [
//                                         {
//                                             id: 'content-1',
//                                             type: 'text',
//                                             values: {
//                                                 text: value,
//                                                 fontSize: '14px',
//                                                 color: '#000000',
//                                                 lineHeight: '1.5',
//                                             },
//                                         },
//                                     ],
//                                 },
//                             ],
//                         },
//                     ],
//                     headers: [],
//                     footers: [],
//                     values: {},
//                 },
//                 values: {},
//             };

//             // حمل التصميم fallback
//             editor.loadDesign(fallbackDesign);
//         }
//     }, [value]);

//     // الاستماع لحدث design:updated واحصل على HTML
//     useEffect(() => {
//         const editor = editorRef.current?.editor;
//         if (!editor) return;

//         const handleDesignUpdated = () => {
//             editor.exportHtml((data: ExportHtmlResult) => {
//                 onChange?.(data.html);
//             });
//         };

//         // أضف المستمع للحدث (API الخاص بـ unlayer/react-email-editor)
//         editor.addEventListener('design:updated', handleDesignUpdated);

//         // Cleanup: بحسب تعريف الحزمة، إزالة المستمع تتم عبر اسم الحدث (أو removeEventListener مع المعالج بحسب التوثيق).
//         // هنا نستخدم removeEventListener('design:updated') لإزالة كل مستمعي نفس الحدث (آمن في هذا السياق).
//         return () => {
//             try {
//                 // بعض إصدارات الواجهة تسمح بإزالة بالمعالج كـ param الثاني؛
//                 // لكن حسب الأنواع التي لديك في المشروع قد يتلقى الحزمة اسم الحدث فقط.
//                 // الاختيار الآمن: استدعاء removeEventListener مع اسم الحدث.
//                 editor.removeEventListener('design:updated');
//             } catch {
//                 // في حال كانت واجهة المكتبة تتطلب معالجًا، نجرّب إزالة بالمعالج
//                 // (المحاولة الثانية لا تستخدم any، لأننا نعرف handleDesignUpdated ضمن النطاق)
//                 // @ts-ignore-next-line -- only fallback; remove if not needed
//                 editor.removeEventListener && editor.removeEventListener('design:updated', handleDesignUpdated);
//             }
//         };
//     }, [onChange]);

//     const handleReady: EmailEditorProps['onReady'] = () => {
//         // جاهزية المحرّر
//     };

//     const displayMode: NonNullable<EmailEditorProps['options']>['displayMode'] = 'web'; // readOnly ? 'web' : 'email';

//     return (
//         <div dir={dir}>
//             <EmailEditor
//                 ref={editorRef}
//                 onReady={handleReady}
//                 style={{ height, borderRadius: '12px', overflow: 'hidden' }}
//                 options={{
//                     locale: dir === 'rtl' ? 'ar' : 'en',
//                     displayMode,
//                     appearance: {
//                         theme: 'dark',
//                         // panels: { tools: { dock: 'right' } },
//                     },
//                 }}
//             />
//         </div>
//     );
// };
