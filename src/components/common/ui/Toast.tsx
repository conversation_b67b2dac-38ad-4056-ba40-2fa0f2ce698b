import { toast } from 'react-hot-toast';
import clsx from 'clsx';

import i18n from '@/shared/config/i18n.config';

type Variant = 'success' | 'error';

interface ToastCardProps {
    id: string;
    title: string;
    description?: string;
    variant?: Variant;
}

export const ToastCard: React.FC<ToastCardProps> = ({ title, description, variant = 'success' }) => {
    const isRTL = i18n.dir() === 'rtl';
    const color = variant === 'success' ? 'green' : 'red';

    return (
        <article
            dir={isRTL ? 'rtl' : 'ltr'}
            className="w-[520px] max-w-[92vw] relative rounded-xl bg-white shadow-[0_8px_24px_rgba(0,0,0,0.08)] ring-1 ring-black/5 overflow-hidden transition-all">
            {/* Accent bar */}
            <div className={clsx('absolute top-0 h-full w-[6px]', isRTL ? 'right-0' : 'left-0', `bg-${color}-500`)} />

            <div className="flex items-center gap-3 px-4 py-3">
                {/* Icon */}
                <div
                    className={clsx(
                        'flex h-8 w-8 items-center justify-center rounded-full border',
                        `text-${color}-700 border-${color}-700 bg-${color}-50`,
                    )}>
                    {variant === 'success' ? (
                        <svg viewBox="0 0 24 24" className="w-6 h-6" fill="none" stroke="currentColor" strokeWidth="2">
                            <circle cx="12" cy="12" r="10" className="opacity-20" />
                            <path d="M7 12l3 3 7-7" />
                        </svg>
                    ) : (
                        <svg viewBox="0 0 24 24" className="w-6 h-6" fill="none" stroke="currentColor" strokeWidth="2">
                            <circle cx="12" cy="12" r="10" className="opacity-20" />
                            <path d="M15 9l-6 6M9 9l6 6" />
                        </svg>
                    )}
                </div>

                {/* Text */}
                <div className="flex-1 pe-2">
                    <div className="font-semibold text-gray-800 text-[15px] leading-6">{title}</div>
                    {description && <div className="text-[13px] text-gray-500 leading-5 mt-0.5">{description}</div>}
                </div>

                {/* Close */}
                {/* TODO: Add close button to toasts and ensure that it close toast only */}
                {/* <button
                    type="button"
                    onMouseDownCapture={(e) => {
                        // Block at the capture phase before Radix handlers
                        e.preventDefault();
                        e.stopPropagation();
                        e.nativeEvent?.stopImmediatePropagation?.();
                    }}
                    onClick={(e) => {
                        // Just in case, block again and dismiss only the toast
                        e.preventDefault();
                        e.stopPropagation();
                        e.nativeEvent?.stopImmediatePropagation?.();
                        toast.dismiss(id);
                    }}
                    className="rounded-md p-1 text-gray-400 hover:text-gray-600"
                    aria-label={t('common.close')}>
                    <svg viewBox="0 0 24 24" className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="M18 6L6 18M6 6l12 12" />
                    </svg>
                </button> */}
            </div>
        </article>
    );
};

// Helpers
export const showSuccess = (title: string, description?: string, duration = 2000) =>
    toast.custom((t) => <ToastCard id={t.id} title={title} description={description} variant="success" />, {
        duration,
    });

export const showError = (title: string, description?: string, duration = 2000) =>
    toast.custom((t) => <ToastCard id={t.id} title={title} description={description} variant="error" />, { duration });
