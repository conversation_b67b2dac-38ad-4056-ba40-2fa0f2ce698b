// // src/components/common/email-templates/trip/TripEmailTemplate.tsx
// import {
//     Html,
//     Head,
//     pixelBasedPreset,
//     Preview,
//     Tailwind,
//     Body,
//     Container,
//     Section,
//     Text,
// } from '@react-email/components';
// import { useTranslation } from 'react-i18next';

// import { useLocalized } from '@/shared/hooks/use-localized.hook';

// import type { TripEmail } from './types';
// import { EmailHeader } from './components/EmailHeader';
// // import { TripInfoSection } from './components/TripInfoSection';
// import { TripDetailsSection } from './components/TripDetailsSection';
// // import { AlertsSection } from './components/AlertsSection';
// // import { EmployeeSignature } from './components/EmployeeSignature';
// // import { EmailFooter } from './components/EmailFooter';

// export function TripEmailTemplate({ trip }: { trip: TripEmail }) {
//     const { t } = useTranslation();
//     const { dir } = useLocalized();
//     const isRTL = dir === 'rtl';

//     return (
//         <Html lang={isRTL ? 'ar' : 'en'} dir={dir}>
//             <Head>
//                 {/* Cairo via Google Fonts (fallback) — replace with CDN/Base64 if required */}
//                 <link
//                     href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap"
//                     rel="stylesheet"
//                 />
//                 <style>{`
//           body { font-family: 'Cairo', Arial, sans-serif; background:#F9FAFB; margin:0; padding:0; }
//           .saudi-primary { color: #1F3D4B; }
//           .saudi-accent { color: #00796B; }
//         `}</style>
//             </Head>

//             {/* <Preview>{t('emails.trip.title')}</Preview> */}

//             <Tailwind>
//                 <Body className="bg-[#F9FAFB] w-full py-6">
//                     <Container className="mx-auto bg-white rounded-md shadow-sm max-w-[680px] p-6">
//                         <EmailHeader />
//                         <TripDetailsSection trip={trip} />
//                         {/* <Section className={`${isRTL ? 'text-right' : 'text-left'} mt-4`}>
//                             <Text
//                                 className={`text-lg font-bold saudi-primary mb-4 ${isRTL ? 'text-right' : 'text-left'}`}>
//                                 {t('emails.trip.title')}
//                             </Text>

//                             <TripInfoSection trip={trip} />
//                             <TripDetailsSection trip={trip} />

//                             {trip.alerts && trip.alerts.length > 0 && <AlertsSection trip={trip} />}

//                             <EmailFooter />

//                             {trip.employee && <EmployeeSignature employee={trip.employee} />}
//                         </Section> */}
//                     </Container>
//                 </Body>
//             </Tailwind>
//         </Html>
//     );
// }

// // src/components/common/email-templates/trip/TripEmailTemplate.tsx
// import {
//     Html,
//     Head,
//     pixelBasedPreset,
//     Preview,
//     Tailwind,
//     Body,
//     Container,
//     Section,
//     Text,
// } from '@react-email/components';
// import { useTranslation } from 'react-i18next';

// import { useLocalized } from '@/shared/hooks/use-localized.hook';

// import type { TripEmail } from './types';
// import { EmailHeader } from './components/EmailHeader';
// // import { TripInfoSection } from './components/TripInfoSection';
// import { TripDetailsSection } from './components/TripDetailsSection';
// // import { AlertsSection } from './components/AlertsSection';
// // import { EmployeeSignature } from './components/EmployeeSignature';
// // import { EmailFooter } from './components/EmailFooter';

// export function TripEmailTemplate({ trip }: { trip: TripEmail }) {
//     const { t } = useTranslation();
//     const { dir } = useLocalized();
//     const isRTL = dir === 'rtl';

//     return (
//         <Container className="mx-auto bg-white rounded-md shadow-sm max-w-[680px] p-6">
//             <EmailHeader />
//             <TripDetailsSection trip={trip} />
//             {/* <TripInfoSection trip={trip} /> */}

//             {/* <Section className={`${isRTL ? 'text-right' : 'text-left'} mt-4`}>
//                             <Text
//                                 className={`text-lg font-bold saudi-primary mb-4 ${isRTL ? 'text-right' : 'text-left'}`}>
//                                 {t('emails.trip.title')}
//                             </Text>

//                             <TripDetailsSection trip={trip} />

//                             {trip.alerts && trip.alerts.length > 0 && <AlertsSection trip={trip} />}

//                             <EmailFooter />

//                             {trip.employee && <EmployeeSignature employee={trip.employee} />}
//                         </Section> */}
//         </Container>
//     );
// }
