import { useTranslation } from 'react-i18next';

import type { TripEmail } from '../types';

export const TripInfoTable = ({ trip }: { trip: TripEmail }) => {
    const { t } = useTranslation();

    const headers = [
        t('emails.trip.transitNumber'),
        t('emails.trip.movementNumber'),
        t('emails.trip.truckData'),
        t('emails.trip.truckDescription'),
        t('emails.trip.truckPlate'),
    ];

    const driverHeaders = [
        t('emails.trip.driverName'),
        t('emails.trip.driverNationality'),
        t('emails.trip.driverPhone'),
        t('emails.trip.ownerName'),
        '',
    ];

    return (
        <table className="w-full border border-gray-300 border-collapse text-[12px] mb-5">
            <thead>
                <tr className="bg-gray-100">
                    {headers.map((h, i) => (
                        <th key={i} className="border border-gray-300 p-2 font-bold">
                            {h}
                        </th>
                    ))}
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td className="border p-2">{trip.transitNumber}</td>
                    <td className="border p-2">{trip.transitSeqNumber}</td>
                    <td className="border p-2">{trip.vehicleType}</td>
                    <td className="border p-2">{trip.vehicleDescription}</td>
                    <td className="border p-2">{trip.vehiclePlateNumber}</td>
                </tr>
                <tr className="bg-gray-100">
                    {driverHeaders.map((h, i) => (
                        <td key={i} className="border border-gray-300 p-2 font-bold">
                            {h}
                        </td>
                    ))}
                </tr>
                <tr>
                    <td className="border p-2">{trip.driverName}</td>
                    <td className="border p-2">{trip.driverNationality}</td>
                    <td className="border p-2">{trip.driverPhoneNumber}</td>
                    <td className="border p-2">{trip.owner}</td>
                    <td className="border p-2">&nbsp;</td>
                </tr>
            </tbody>
        </table>
    );
};
