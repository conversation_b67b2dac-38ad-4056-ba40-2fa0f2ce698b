// src/components/common/email-templates/trip/components/EmailHeader.tsx
// import { Row, Section, Column, Img, Text } from '@react-email/components';

import { Section, Text } from '@react-email/components';
import { useTranslation } from 'react-i18next';

// import SaudiCustomsLogo from '@imgs/saudi-customs-logo.png';
// import Tu<PERSON>ogo from '@imgs/tu-logo.png';

export const EmailHeader = () => {
    const { t } = useTranslation();
    return (
        <Section style={{ display: 'flex', justifyContent: 'center' }}>
            {/* <Row>
                <>
                    <Column>
                        <Img src={SaudiCustomsLogo} alt="logo" width="160" />
                    </Column>
                    <Column>
                        <Img src={TuLogo} alt="logo" width="160" />
                    </Column>
                </>
            </Row> */}
            <Text style={{ textAlign: 'center', color: '#6B7280', marginTop: 8 }}>{t('emails.greeting')}</Text>
        </Section>
    );
};
