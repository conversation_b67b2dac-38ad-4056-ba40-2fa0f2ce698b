// src/components/common/email-templates/trip/components/EmployeeSignature.tsx
import { Text } from '@react-email/components';

import { useLocalized } from '@/shared/hooks/use-localized.hook';

import type { TripEmail } from '../types';

export const EmployeeSignature = ({ employee }: { employee: NonNullable<TripEmail['employee']> }) => {
    const { localized, dir } = useLocalized();

    return (
        <div style={{ textAlign: dir === 'rtl' ? 'right' : 'left', marginTop: 16 }}>
            <Text style={{ color: '#1F3D4B', fontWeight: 700, marginBottom: 6 }}>{localized(employee.Name)}</Text>
            <Text style={{ color: '#6B7280', fontSize: 12 }}>
                {localized(employee.Department)}
                <br />
                {employee.Phone1 && employee.Phone2
                    ? `${employee.Phone1} | ${employee.Phone2}`
                    : employee.Phone1 || employee.Phone2 || ''}
                {employee.Ex && (
                    <>
                        <br />
                        <span style={{ color: '#1F3D4B' }}>Ex:</span> {employee.Ex}
                    </>
                )}
                {employee.Fax && (
                    <>
                        <br />
                        <span style={{ color: '#1F3D4B' }}>Fax:</span> {employee.Fax}
                    </>
                )}
            </Text>
        </div>
    );
};
