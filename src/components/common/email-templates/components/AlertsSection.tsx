// src/components/common/email-templates/trip/components/AlertsSection.tsx
import React from 'react';
import { Row, Column, Text } from '@react-email/components';
import { useTranslation } from 'react-i18next';

import { useLocalized } from '@/shared/hooks/use-localized.hook';
import { formatLocalizedDate } from '@/shared/utils/format-date.utils';

import type { TripEmail } from '../types';

const cellStyle: React.CSSProperties = { padding: '8px', border: '1px solid #E5E7EB', fontSize: 12 };

export const AlertsSection = ({ trip }: { trip: TripEmail }) => {
    const { t } = useTranslation();
    const { localized, dir } = useLocalized();
    const textAlign = dir === 'rtl' ? 'right' : 'left';

    return (
        <div style={{ marginBottom: 12 }}>
            <Text style={{ fontSize: 16, fontWeight: 700, color: '#1F3D4B', marginBottom: 8, textAlign }}>
                {t('emails.trip.alerts')}
            </Text>

            {/* header */}
            <Row style={{ display: 'flex', border: '1px solid #E5E7EB', borderRadius: 4, overflow: 'hidden' }}>
                <Column style={{ width: '60%', background: '#F5F5F5' }}>
                    <Text style={{ ...cellStyle, fontWeight: 600, textAlign }}>{t('emails.trip.alertName')}</Text>
                </Column>
                <Column style={{ width: '40%', background: '#F5F5F5' }}>
                    <Text style={{ ...cellStyle, fontWeight: 600, textAlign }}>{t('emails.trip.alertTime')}</Text>
                </Column>
            </Row>

            {/* rows */}
            {trip.alerts && trip.alerts.length > 0 ? (
                trip.alerts.map((alert, i) => (
                    <Row key={i} style={{ display: 'flex', border: '1px solid #E5E7EB', marginTop: 0 }}>
                        <Column style={{ width: '60%' }}>
                            <Text style={{ ...cellStyle, textAlign }}>{localized(alert.name)}</Text>
                        </Column>
                        <Column style={{ width: '40%' }}>
                            <Text style={{ ...cellStyle, textAlign }}>
                                {alert.toStateTimeStamp
                                    ? dir === 'rtl'
                                        ? `${formatLocalizedDate(alert.fromStateTimeStamp)} ← ${formatLocalizedDate(alert.toStateTimeStamp)}`
                                        : `${formatLocalizedDate(alert.fromStateTimeStamp)} → ${formatLocalizedDate(alert.toStateTimeStamp)}`
                                    : formatLocalizedDate(alert.fromStateTimeStamp)}
                            </Text>
                        </Column>
                    </Row>
                ))
            ) : (
                <Row style={{ display: 'flex', marginTop: 8 }}>
                    <Column style={{ width: '60%' }}>
                        <Text style={{ ...cellStyle, textAlign }}>-</Text>
                    </Column>
                    <Column style={{ width: '40%' }}>
                        <Text style={{ ...cellStyle, textAlign }}>-</Text>
                    </Column>
                </Row>
            )}
        </div>
    );
};
