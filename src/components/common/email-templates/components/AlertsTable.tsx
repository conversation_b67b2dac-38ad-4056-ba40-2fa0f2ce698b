import { useTranslation } from 'react-i18next';

import { useLocalized } from '@/shared/hooks/use-localized.hook';
import { formatLocalizedDate } from '@/shared/utils/format-date.utils';

import type { TripEmail } from '../types';

export const AlertsTable = ({ trip }: { trip: TripEmail }) => {
    const { t } = useTranslation();
    const { localized, dir } = useLocalized();

    return (
        <div className="mb-5">
            <h3 className="text-right mb-3 text-lg font-bold text-[#1f3d4b]">{t('emails.trip.alerts')}</h3>
            <table className="w-full border border-gray-300 border-collapse text-[12px]">
                <thead>
                    <tr className="bg-gray-100">
                        <th className="border p-2 font-bold">{t('emails.trip.alertName')}</th>
                        <th className="border p-2 font-bold">{t('emails.trip.alertTime')}</th>
                    </tr>
                </thead>
                <tbody>
                    {trip.alerts?.map((alert, index) => (
                        <tr key={index}>
                            <td className="border p-2">{localized(alert.name)}</td>
                            <td className="border p-2">
                                {alert.toStateTimeStamp
                                    ? dir === 'rtl'
                                        ? `${formatLocalizedDate(alert.fromStateTimeStamp)} ← ${formatLocalizedDate(alert.toStateTimeStamp)}`
                                        : `${formatLocalizedDate(alert.fromStateTimeStamp)} → ${formatLocalizedDate(alert.toStateTimeStamp)}`
                                    : formatLocalizedDate(alert.fromStateTimeStamp)}
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
};
