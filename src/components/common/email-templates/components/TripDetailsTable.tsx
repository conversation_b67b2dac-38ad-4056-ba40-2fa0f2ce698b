import { useTranslation } from 'react-i18next';

import { useLocalized } from '@/shared/hooks/use-localized.hook';
import { formatKSADateTime } from '@/shared/utils/format-date.utils';

import type { TripEmail } from '../types';

export const TripDetailsTable = ({ trip }: { trip: TripEmail }) => {
    const { t } = useTranslation();
    const { localized } = useLocalized();

    return (
        <table className="w-full border border-gray-300 border-collapse text-[12px] mb-5">
            <thead>
                <tr className="bg-gray-100">
                    <th className="border p-2 font-bold">{t('emails.trip.shipmentDescription')}</th>
                    <th className="border p-2 font-bold">{t('emails.trip.transitType')}</th>
                    <th className="border p-2 font-bold">{t('emails.trip.trackingDevice')}</th>
                    <th className="border p-2 font-bold">{t('emails.trip.lockNumber')}</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td className="border p-2">{trip.shipmentDescription}</td>
                    <td className="border p-2">{trip.transitType}</td>
                    <td className="border p-2">{trip.trackerSerialNumber}</td>
                    <td className="border p-2">{trip.eLockSerialNumbers?.join(', ')}</td>
                </tr>
                <tr className="bg-gray-100">
                    <td className="border p-2 font-bold">{t('emails.trip.entryPort')}</td>
                    <td className="border p-2 font-bold">{t('emails.trip.exitPort')}</td>
                    <td className="border p-2 font-bold">{t('emails.trip.entryDate')}</td>
                    <td className="border p-2 font-bold">{t('emails.trip.entryTime')}</td>
                </tr>
                <tr>
                    <td className="border p-2">{trip.entryPortName ? localized(trip.entryPortName) : ''}</td>
                    <td className="border p-2">{trip.exitPortName ? localized(trip.exitPortName) : ''}</td>
                    <td className="border p-2">{formatKSADateTime(trip.startDate || '')}</td>
                    <td className="border p-2">{formatKSADateTime(trip.startDate || '')}</td>
                </tr>
            </tbody>
        </table>
    );
};
