// src/components/common/email-templates/trip/components/EmailFooter.tsx
import { Text, Img } from '@react-email/components';
import { useTranslation } from 'react-i18next';

import { useLocalized } from '@/shared/hooks/use-localized.hook';

export const EmailFooter = () => {
    const { t } = useTranslation();
    const { dir } = useLocalized();

    return (
        <div style={{ marginTop: 12, textAlign: dir === 'rtl' ? 'right' : 'left' }}>
            <ul
                style={{
                    margin: '0 0 12px 0',
                    paddingLeft: dir === 'rtl' ? 0 : 20,
                    paddingRight: dir === 'rtl' ? 20 : 0,
                }}>
                <li style={{ marginBottom: 6 }}>{t('emails.trip.colleagues')}</li>
                <li style={{ marginBottom: 6 }}>{t('emails.trip.greetingMessage')}</li>
            </ul>

            <Text style={{ marginBottom: 12 }}>{t('emails.trip.verificationMessage')}</Text>

            <div style={{ textAlign: 'right' }}>
                <Img src="https://example.com/your-logo.png" alt="logo" width="200" />
            </div>
        </div>
    );
};
