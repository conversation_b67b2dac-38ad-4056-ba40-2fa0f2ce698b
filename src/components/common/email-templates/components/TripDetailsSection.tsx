// // src/components/common/email-templates/trip/components/TripDetailsSection.tsx
// import React from 'react';
// import { Row, Column, Text } from '@react-email/components';
// import { useTranslation } from 'react-i18next';

// import { useLocalized } from '@/shared/hooks/use-localized.hook';
// import { formatKSADateTime } from '@/shared/utils/format-date.utils';

// import type { TripEmail } from '../types';

// const cellStyle: React.CSSProperties = { padding: '8px', border: '1px solid #E5E7EB', fontSize: 12 };

// export const TripDetailsSection = ({ trip }: { trip: TripEmail }) => {
//     const { t } = useTranslation();
//     const { localized, dir } = useLocalized();
//     const textAlign = dir === 'rtl' ? 'right' : 'left';
//     const safe = (v?: any) => (v === undefined || v === null || v === '' ? '-' : String(v));

//     return (
//         <>
//             <Row
//                 style={{
//                     display: 'flex',
//                     border: '1px solid #E5E7EB',
//                     borderRadius: 4,
//                     overflow: 'hidden',
//                     marginBottom: 8,
//                 }}>
//                 <Column style={{ width: '25%', background: '#F5F5F5' }}>
//                     <Text style={{ ...cellStyle, fontWeight: 600, textAlign }}>
//                         {t('emails.trip.shipmentDescription')}
//                     </Text>
//                 </Column>
//                 <Column style={{ width: '25%', background: '#F5F5F5' }}>
//                     <Text style={{ ...cellStyle, fontWeight: 600, textAlign }}>{t('emails.trip.transitType')}</Text>
//                 </Column>
//                 <Column style={{ width: '25%', background: '#F5F5F5' }}>
//                     <Text style={{ ...cellStyle, fontWeight: 600, textAlign }}>{t('emails.trip.trackingDevice')}</Text>
//                 </Column>
//                 <Column style={{ width: '25%', background: '#F5F5F5' }}>
//                     <Text style={{ ...cellStyle, fontWeight: 600, textAlign }}>{t('emails.trip.lockNumber')}</Text>
//                 </Column>
//             </Row>

//             <Row style={{ display: 'flex', marginBottom: 12 }}>
//                 <Column style={{ width: '25%' }}>
//                     <Text style={{ ...cellStyle, textAlign }}>{safe(trip.shipmentDescription)}</Text>
//                 </Column>
//                 <Column style={{ width: '25%' }}>
//                     <Text style={{ ...cellStyle, textAlign }}>{safe(trip.transitType)}</Text>
//                 </Column>
//                 <Column style={{ width: '25%' }}>
//                     <Text style={{ ...cellStyle, textAlign }}>{safe(trip.trackerSerialNumber)}</Text>
//                 </Column>
//                 <Column style={{ width: '25%' }}>
//                     <Text style={{ ...cellStyle, textAlign }}>{(trip.eLockSerialNumbers || []).join(', ') || '-'}</Text>
//                 </Column>
//             </Row>

//             {/* Ports & dates */}
//             <Row
//                 style={{
//                     display: 'flex',
//                     border: '1px solid #E5E7EB',
//                     borderRadius: 4,
//                     overflow: 'hidden',
//                     marginBottom: 12,
//                 }}>
//                 <Column style={{ width: '25%', background: '#F5F5F5' }}>
//                     <Text style={{ ...cellStyle, fontWeight: 600, textAlign }}>{t('emails.trip.entryPort')}</Text>
//                 </Column>
//                 <Column style={{ width: '25%', background: '#F5F5F5' }}>
//                     <Text style={{ ...cellStyle, fontWeight: 600, textAlign }}>{t('emails.trip.exitPort')}</Text>
//                 </Column>
//                 <Column style={{ width: '25%', background: '#F5F5F5' }}>
//                     <Text style={{ ...cellStyle, fontWeight: 600, textAlign }}>{t('emails.trip.entryDate')}</Text>
//                 </Column>
//                 <Column style={{ width: '25%', background: '#F5F5F5' }}>
//                     <Text style={{ ...cellStyle, fontWeight: 600, textAlign }}>{t('emails.trip.entryTime')}</Text>
//                 </Column>
//             </Row>

//             <Row style={{ display: 'flex', marginBottom: 12 }}>
//                 <Column style={{ width: '25%' }}>
//                     <Text style={{ ...cellStyle, textAlign }}>
//                         {trip.entryPortName ? localized(trip.entryPortName) : '-'}
//                     </Text>
//                 </Column>
//                 <Column style={{ width: '25%' }}>
//                     <Text style={{ ...cellStyle, textAlign }}>
//                         {trip.exitPortName ? localized(trip.exitPortName) : '-'}
//                     </Text>
//                 </Column>
//                 <Column style={{ width: '25%' }}>
//                     <Text style={{ ...cellStyle, textAlign }}>
//                         {trip.startDate ? formatKSADateTime(trip.startDate) : '-'}
//                     </Text>
//                 </Column>
//                 <Column style={{ width: '25%' }}>
//                     <Text style={{ ...cellStyle, textAlign }}>
//                         {trip.startDate ? formatKSADateTime(trip.startDate) : '-'}
//                     </Text>
//                 </Column>
//             </Row>
//         </>
//     );
// };
