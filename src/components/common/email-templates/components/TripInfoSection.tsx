// // src/components/common/email-templates/trip/components/TripInfoSection.tsx
// import React from 'react';
// import { Row, Column, Text } from '@react-email/components';
// import { useTranslation } from 'react-i18next';

// import { useLocalized } from '@/shared/hooks/use-localized.hook';

// import type { TripEmail } from '../types';

// const cellStyle: React.CSSProperties = { padding: '8px', border: '1px solid #E5E7EB', fontSize: 12 };

// export const TripInfoSection = ({ trip }: { trip: TripEmail }) => {
//     const { t } = useTranslation();
//     const { dir } = useLocalized();
//     const textAlign = dir === 'rtl' ? 'right' : 'left';

//     const safe = (v?: any) => (v === undefined || v === null || v === '' ? '-' : String(v));

//     return (
//         <>
//             {/* Header row */}
//             <Row
//                 style={{
//                     display: 'flex',
//                     border: '1px solid #E5E7EB',
//                     borderRadius: 4,
//                     overflow: 'hidden',
//                     marginBottom: 12,
//                 }}>
//                 <Column style={{ width: '20%', background: '#F5F5F5' }}>
//                     <Text style={{ ...cellStyle, fontWeight: 600, textAlign }}>{t('emails.trip.transitNumber')}</Text>
//                 </Column>
//                 <Column style={{ width: '20%', background: '#F5F5F5' }}>
//                     <Text style={{ ...cellStyle, fontWeight: 600, textAlign }}>{t('emails.trip.movementNumber')}</Text>
//                 </Column>
//                 <Column style={{ width: '20%', background: '#F5F5F5' }}>
//                     <Text style={{ ...cellStyle, fontWeight: 600, textAlign }}>{t('emails.trip.truckData')}</Text>
//                 </Column>
//                 <Column style={{ width: '20%', background: '#F5F5F5' }}>
//                     <Text style={{ ...cellStyle, fontWeight: 600, textAlign }}>
//                         {t('emails.trip.truckDescription')}
//                     </Text>
//                 </Column>
//                 <Column style={{ width: '20%', background: '#F5F5F5' }}>
//                     <Text style={{ ...cellStyle, fontWeight: 600, textAlign }}>{t('emails.trip.truckPlate')}</Text>
//                 </Column>
//             </Row>

//             {/* Data row */}
//             <Row
//                 style={{
//                     display: 'flex',
//                     border: '1px solid #E5E7EB',
//                     borderRadius: 4,
//                     overflow: 'hidden',
//                     marginBottom: 8,
//                 }}>
//                 <Column style={{ width: '20%' }}>
//                     <Text style={{ ...cellStyle, textAlign }}>{safe(trip.transitNumber)}</Text>
//                 </Column>
//                 <Column style={{ width: '20%' }}>
//                     <Text style={{ ...cellStyle, textAlign }}>{safe(trip.transitSeqNumber)}</Text>
//                 </Column>
//                 <Column style={{ width: '20%' }}>
//                     <Text style={{ ...cellStyle, textAlign }}>{safe(trip.vehicleType)}</Text>
//                 </Column>
//                 <Column style={{ width: '20%' }}>
//                     <Text style={{ ...cellStyle, textAlign }}>{safe(trip.vehicleDescription)}</Text>
//                 </Column>
//                 <Column style={{ width: '20%' }}>
//                     <Text style={{ ...cellStyle, textAlign }}>{safe(trip.vehiclePlateNumber)}</Text>
//                 </Column>
//             </Row>

//             {/* Driver header */}
//             <Row style={{ display: 'flex', marginBottom: 8 }}>
//                 <Column style={{ width: '20%', background: '#F5F5F5' }}>
//                     <Text style={{ ...cellStyle, fontWeight: 600, textAlign }}>{t('emails.trip.driverName')}</Text>
//                 </Column>
//                 <Column style={{ width: '20%', background: '#F5F5F5' }}>
//                     <Text style={{ ...cellStyle, fontWeight: 600, textAlign }}>
//                         {t('emails.trip.driverNationality')}
//                     </Text>
//                 </Column>
//                 <Column style={{ width: '20%', background: '#F5F5F5' }}>
//                     <Text style={{ ...cellStyle, fontWeight: 600, textAlign }}>{t('emails.trip.driverPhone')}</Text>
//                 </Column>
//                 <Column style={{ width: '20%', background: '#F5F5F5' }}>
//                     <Text style={{ ...cellStyle, fontWeight: 600, textAlign }}>{t('emails.trip.ownerName')}</Text>
//                 </Column>
//                 <Column style={{ width: '20%', background: '#F5F5F5' }}>
//                     <Text style={{ ...cellStyle, fontWeight: 600, textAlign }}>&nbsp;</Text>
//                 </Column>
//             </Row>

//             {/* Driver data */}
//             <Row style={{ display: 'flex', marginBottom: 12 }}>
//                 <Column style={{ width: '20%' }}>
//                     <Text style={{ ...cellStyle, textAlign }}>{safe(trip.driverName)}</Text>
//                 </Column>
//                 <Column style={{ width: '20%' }}>
//                     <Text style={{ ...cellStyle, textAlign }}>{safe(trip.driverNationality)}</Text>
//                 </Column>
//                 <Column style={{ width: '20%' }}>
//                     <Text style={{ ...cellStyle, textAlign }}>{safe(trip.driverPhoneNumber)}</Text>
//                 </Column>
//                 <Column style={{ width: '20%' }}>
//                     <Text style={{ ...cellStyle, textAlign }}>{safe(trip.owner)}</Text>
//                 </Column>
//                 <Column style={{ width: '20%' }}>
//                     <Text style={{ ...cellStyle, textAlign }}>&nbsp;</Text>
//                 </Column>
//             </Row>
//         </>
//     );
// };
