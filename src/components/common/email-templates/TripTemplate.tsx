import { useTranslation } from 'react-i18next';

import type { Translatable } from '@/shared/types/common';
import { useLocalized } from '@/shared/hooks/use-localized.hook';
import { formatKSADateTime } from '@/shared/utils/format-date.utils';

export type TripEmailTemplateProps = {
    trip: TripEmail;
};

export type TripEmail = {
    owner: string;
    driverPhoneNumber: string;
    driverNationality: string;
    driverName: string;
    vehiclePlateNumber: string;
    vehicleType: string;
    vehicleDescription: string;
    transitSeqNumber: string;
    transitNumber: string;
    shipmentDescription: string;
    transitType: string;
    trackerSerialNumber: string;
    eLockSerialNumbers: string[];
    trackingPriority: string;
    entryPortName: Translatable;
    exitPortName: Translatable;
    startDate: Date;
    alerts?:
        | { id: number; typeName: Translatable; fromStateTimeStamp: string; toStateTimeStamp: string | null }[]
        | undefined;
    employee?:
        | {
              Name: Translatable;
              Phone1: string | undefined;
              Phone2: string | undefined;
              Department: Translatable;
              Fax: string | undefined;
              Ex: string | undefined;
          }
        | undefined;
};

export function TripEmailTemplate({ trip }: TripEmailTemplateProps) {
    const { t } = useTranslation();
    const { localized, dir } = useLocalized();

    return (
        <table
            border={0}
            cellPadding="0"
            cellSpacing="0"
            style={{
                width: '100%',
                maxWidth: '600px',
                margin: '0 auto',
                fontFamily: 'Arial, sans-serif',
                fontSize: '14px',
                lineHeight: '1.4',
                color: '#333333',
                backgroundColor: '#ffffff',
                textAlign: dir === 'rtl' ? 'right' : 'left',
            }}>
            <tbody>
                <tr>
                    <td style={{ padding: '20px' }}>
                        {/* Greeting */}
                        <p style={{ textAlign: 'center', margin: '0 0 20px 0', fontSize: '16px' }}>
                            {t('emails.greeting')}
                        </p>

                        {/* Title */}
                        <h3
                            style={{
                                textAlign: dir === 'rtl' ? 'right' : 'left',
                                margin: '0 0 20px 0',
                                fontSize: '18px',
                                fontWeight: 'bold',
                                color: '#1f3d4b',
                            }}>
                            {t('emails.trip.title')}
                        </h3>

                        {/* First Table */}
                        <table
                            border={1}
                            cellPadding="8"
                            cellSpacing="0"
                            style={{
                                width: '100%',
                                borderCollapse: 'collapse',
                                borderColor: '#cccccc',
                                marginBottom: '20px',
                                fontSize: '12px',
                                textAlign: dir === 'rtl' ? 'right' : 'left',
                                alignItems: dir === 'rtl' ? 'right' : 'left',
                            }}>
                            <tbody>
                                <tr style={{ backgroundColor: '#f5f5f5' }}>
                                    <td style={{ border: '1px solid #cccccc', fontWeight: 'bold' }}>
                                        {t('emails.trip.transitNumber')}
                                    </td>
                                    <td style={{ border: '1px solid #cccccc', fontWeight: 'bold' }}>
                                        {t('emails.trip.transitSequenceNumber')}
                                    </td>
                                    <td style={{ border: '1px solid #cccccc', fontWeight: 'bold' }}>
                                        {t('emails.trip.truckData')}
                                    </td>
                                    <td style={{ border: '1px solid #cccccc', fontWeight: 'bold' }}>
                                        {t('emails.trip.truckDescription')}
                                    </td>
                                    <td style={{ border: '1px solid #cccccc', fontWeight: 'bold' }}>
                                        {t('emails.trip.truckPlate')}
                                    </td>
                                    <td style={{ border: '1px solid #cccccc', fontWeight: 'bold' }}>
                                        {t('emails.trip.driverName')}
                                    </td>
                                    <td style={{ border: '1px solid #cccccc', fontWeight: 'bold' }}>
                                        {t('emails.trip.driverNationality')}
                                    </td>
                                    <td style={{ border: '1px solid #cccccc', fontWeight: 'bold' }}>
                                        {t('emails.trip.driverPhone')}
                                    </td>
                                    <td style={{ border: '1px solid #cccccc', fontWeight: 'bold' }}>
                                        {t('emails.trip.ownerName')}
                                    </td>
                                </tr>
                                <tr>
                                    <td style={{ border: '1px solid #cccccc' }}>{trip.transitNumber}</td>
                                    <td style={{ border: '1px solid #cccccc' }}>{trip.transitSeqNumber}</td>
                                    <td style={{ border: '1px solid #cccccc' }}>{trip.vehicleType}</td>
                                    <td style={{ border: '1px solid #cccccc' }}>{trip.vehicleDescription}</td>
                                    <td style={{ border: '1px solid #cccccc' }}>{trip.vehiclePlateNumber}</td>
                                    <td style={{ border: '1px solid #cccccc' }}>{trip.driverName}</td>
                                    <td style={{ border: '1px solid #cccccc' }}>{trip.driverNationality}</td>
                                    <td style={{ border: '1px solid #cccccc' }}>{trip.driverPhoneNumber}</td>
                                    <td style={{ border: '1px solid #cccccc' }}>{trip.owner}</td>
                                </tr>
                                <tr style={{ backgroundColor: '#f5f5f5' }}>
                                    <td style={{ border: '1px solid #cccccc', fontWeight: 'bold' }}>
                                        {t('emails.trip.shipmentDescription')}
                                    </td>
                                    <td style={{ border: '1px solid #cccccc', fontWeight: 'bold' }}>
                                        {t('emails.trip.transitType')}
                                    </td>
                                    <td style={{ border: '1px solid #cccccc', fontWeight: 'bold' }}>
                                        {t('emails.trip.trackingDevice')}
                                    </td>
                                    <td style={{ border: '1px solid #cccccc', fontWeight: 'bold' }}>
                                        {t('emails.trip.lockNumber')}
                                    </td>
                                    <td style={{ border: '1px solid #cccccc', fontWeight: 'bold' }}>
                                        {t('emails.trip.trackingPriority')}
                                    </td>
                                    <td style={{ border: '1px solid #cccccc', fontWeight: 'bold' }}>
                                        {t('emails.trip.entryPort')}
                                    </td>
                                    <td style={{ border: '1px solid #cccccc', fontWeight: 'bold' }}>
                                        {t('emails.trip.exitPort')}
                                    </td>
                                    <td style={{ border: '1px solid #cccccc', fontWeight: 'bold' }}>
                                        {t('emails.trip.entryDate')}
                                    </td>
                                    <td style={{ border: '1px solid #cccccc', fontWeight: 'bold' }}>
                                        {t('emails.trip.entryTime')}
                                    </td>
                                </tr>
                                <tr>
                                    <td style={{ border: '1px solid #cccccc' }}>{trip.shipmentDescription}</td>
                                    <td style={{ border: '1px solid #cccccc' }}>{trip.transitType}</td>
                                    <td style={{ border: '1px solid #cccccc' }}>{trip.trackerSerialNumber}</td>
                                    <td style={{ border: '1px solid #cccccc' }}>
                                        {trip.eLockSerialNumbers.join(', ')}
                                    </td>
                                    <td style={{ border: '1px solid #cccccc' }}>{trip.trackingPriority}</td>
                                    <td style={{ border: '1px solid #cccccc' }}>{localized(trip.entryPortName)}</td>
                                    <td style={{ border: '1px solid #cccccc' }}>{localized(trip.exitPortName)}</td>
                                    <td style={{ border: '1px solid #cccccc' }}>{formatKSADateTime(trip.startDate)}</td>
                                    <td style={{ border: '1px solid #cccccc' }}>{formatKSADateTime(trip.startDate)}</td>
                                </tr>
                            </tbody>
                        </table>

                        {/* Alerts Table */}
                        {trip.alerts && trip.alerts.length > 0 && (
                            <>
                                {/* Alerts Section Title */}
                                <h3
                                    style={{
                                        textAlign: dir === 'rtl' ? 'right' : 'left',
                                        margin: '0 0 20px 0',
                                        fontSize: '18px',
                                        fontWeight: 'bold',
                                        color: '#1f3d4b',
                                    }}>
                                    {t('emails.trip.alerts')}
                                </h3>
                                <table
                                    border={1}
                                    cellPadding="8"
                                    cellSpacing="0"
                                    style={{
                                        width: '100%',
                                        borderCollapse: 'collapse',
                                        borderColor: '#cccccc',
                                        marginBottom: '20px',
                                        fontSize: '12px',
                                        textAlign: dir === 'rtl' ? 'right' : 'left',
                                        alignItems: dir === 'rtl' ? 'right' : 'left',
                                    }}>
                                    <tbody>
                                        <tr style={{ backgroundColor: '#f5f5f5' }}>
                                            <td
                                                style={{
                                                    border: '1px solid #cccccc',
                                                    fontWeight: 'bold',
                                                }}>
                                                {t('emails.trip.alertID')}
                                            </td>
                                            <td
                                                style={{
                                                    border: '1px solid #cccccc',
                                                    fontWeight: 'bold',
                                                }}>
                                                {t('emails.trip.alertType')}
                                            </td>
                                            <td
                                                style={{
                                                    border: '1px solid #cccccc',
                                                    fontWeight: 'bold',
                                                }}>
                                                {t('emails.trip.alertTime')}
                                            </td>
                                        </tr>
                                        {trip.alerts && trip.alerts.length > 0 ? (
                                            trip.alerts.map((alert, index) => (
                                                <tr key={index}>
                                                    <td style={{ border: '1px solid #cccccc' }}>{alert.id}</td>
                                                    <td style={{ border: '1px solid #cccccc' }}>
                                                        {localized(alert.typeName)}
                                                    </td>
                                                    <td style={{ border: '1px solid #cccccc' }}>
                                                        {alert.toStateTimeStamp && alert.toStateTimeStamp
                                                            ? dir === 'rtl'
                                                                ? `${formatKSADateTime(alert.fromStateTimeStamp)} ← ${formatKSADateTime(
                                                                      alert.toStateTimeStamp,
                                                                  )}`
                                                                : `${formatKSADateTime(alert.fromStateTimeStamp)} → ${formatKSADateTime(
                                                                      alert.toStateTimeStamp,
                                                                  )}`
                                                            : formatKSADateTime(alert.fromStateTimeStamp)}
                                                    </td>
                                                </tr>
                                            ))
                                        ) : (
                                            <tr>
                                                <td style={{ border: '1px solid #cccccc' }}>-</td>
                                                <td style={{ border: '1px solid #cccccc' }}>-</td>
                                            </tr>
                                        )}
                                    </tbody>
                                </table>
                            </>
                        )}

                        {/* Messages */}
                        {/* <ul
                            style={{
                                margin: '0 0 20px 0',
                                paddingLeft: '20px',
                                textAlign: dir === 'rtl' ? 'right' : 'left',
                                listStyleType: 'none',
                            }}>
                            <li style={{ textAlign: dir === 'rtl' ? 'right' : 'left', marginBottom: '5px' }}>
                                {t('emails.trip.colleagues')}
                            </li>
                            <li style={{ textAlign: dir === 'rtl' ? 'right' : 'left', marginBottom: '5px' }}>
                                {t('emails.trip.greetingMessage')}
                            </li>
                        </ul> */}

                        {/* <p style={{ textAlign: dir === 'rtl' ? 'right' : 'left', margin: '0 0 20px 0' }}>
                            {t('emails.trip.verificationMessage')}
                        </p> */}

                        {/* Title */}
                        <h3
                            style={{
                                textAlign: dir === 'rtl' ? 'right' : 'left',
                                margin: '0 0 20px 0',
                                fontWeight: 'bold',
                                color: '#1f3d4b',
                            }}>
                            {t('emails.trip.colleagues')}
                        </h3>
                        <h4
                            style={{
                                textAlign: dir === 'rtl' ? 'right' : 'left',
                                margin: '0 0 20px 0',
                            }}>
                            {t('emails.trip.greetingMessage')}
                        </h4>
                        <h4
                            style={{
                                textAlign: dir === 'rtl' ? 'right' : 'left',
                                margin: '0 0 20px 0',
                            }}>
                            {t('emails.trip.verificationMessage')}
                        </h4>

                        {/* Image */}
                        {/* <div
                            style={{
                                textAlign: dir === 'rtl' ? 'right' : 'left',
                                width: '25px',
                                height: '25px',
                                marginBottom: '20px',
                            }}>
                            <img
                                src="/src/assets/imgs/logo.png"
                                alt="Company Logo"
                                style={{
                                    maxWidth: '25px',
                                    height: 'auto',
                                    display: 'block',
                                    marginLeft: 'auto',
                                    border: 'none',
                                    outline: 'none',
                                }}
                            />
                        </div> */}

                        {/* Image */}
                        <div
                            style={{
                                textAlign: dir === 'rtl' ? 'right' : 'left',
                                width: '25px',
                                height: '25px',
                                marginBottom: '20px',
                                overflow: 'hidden', // للتأكد من عدم تجاوز الصورة للحاوية
                            }}>
                            <img
                                src="/src/assets/imgs/logo.png"
                                alt="Company Logo"
                                style={{
                                    width: '25px', // حجم ثابت
                                    height: '25px', // حجم ثابت
                                    objectFit: 'contain', // يحافظ على نسبة الأبعاد داخل الحجم المحدد
                                    display: 'block',
                                    marginLeft: 'auto',
                                    marginRight: 'auto',
                                    border: 'none',
                                    outline: 'none',
                                }}
                            />
                        </div>

                        {/* Employee Signature */}
                        {trip.employee && (
                            <div style={{ textAlign: 'right', marginBottom: '20px' }}>
                                <p style={{ margin: '0 0 10px 0' }}>
                                    <strong style={{ color: '#1f3d4b', fontSize: '16px' }}>
                                        {localized(trip.employee.Name)}
                                    </strong>
                                </p>
                                <p style={{ margin: '0', color: '#666666', fontSize: '12px' }}>
                                    {localized(trip.employee.Department)}
                                    <br />
                                    {trip.employee.Phone1 && trip.employee.Phone2
                                        ? `${trip.employee.Phone1} | ${trip.employee.Phone2}`
                                        : trip.employee.Phone1 || trip.employee.Phone2 || ''}
                                    {trip.employee.Ex && (
                                        <>
                                            <br />
                                            <span style={{ color: '#1f3d4b' }}>Ex:</span> {trip.employee.Ex}
                                        </>
                                    )}
                                    {trip.employee.Fax && (
                                        <>
                                            <br />
                                            <span style={{ color: '#1f3d4b' }}>Fax:</span> {trip.employee.Fax}
                                        </>
                                    )}
                                </p>
                            </div>
                        )}
                    </td>
                </tr>
            </tbody>
        </table>
    );
}
