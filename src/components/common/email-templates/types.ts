// src/components/common/email-templates/trip/types.ts
import type { Translatable } from '@/shared/types/common';

export type TripEmail = {
    owner?: string;
    driverPhoneNumber?: string | null;
    driverNationality?: string | null;
    driverName?: string | null;
    vehiclePlateNumber?: string | null;
    vehicleType?: string | null;
    vehicleDescription?: string | null;
    transitSeqNumber?: string | null;
    transitNumber?: string | null;
    shipmentDescription?: string | null;
    transitType?: string | null;
    trackerSerialNumber?: string | null;
    eLockSerialNumbers?: string[];
    entryPortName?: Translatable | null;
    exitPortName?: Translatable | null;
    startDate?: string | Date | null;
    alerts?: { name: Translatable; fromStateTimeStamp: string; toStateTimeStamp?: string | null }[] | undefined;
    employee?:
        | {
              Name: Translatable;
              Phone1?: string;
              Phone2?: string;
              Department: Translatable;
              Fax?: string;
              Ex?: string;
          }
        | undefined;
};
