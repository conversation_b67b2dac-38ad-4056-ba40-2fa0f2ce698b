import './TripFilterDialog.css';
import { Accordion, AccordionTab } from 'primereact/accordion';
import { useEffect, useMemo, useState, useRef } from 'react';
import { IoFilter } from 'react-icons/io5';
import { FaCalendarDays } from 'react-icons/fa6';
import { useTranslation } from 'react-i18next';
import { ChevronDown, ChevronUp, FilterIcon, RefreshCwIcon, Route } from 'lucide-react';

import { DatePicker } from '@/components/common/ui/DatePicker';
import { Button } from '@/components/common/ui/Button';
import { Input } from '@/components/common/ui/Input';
import { Checkbox } from '@/components/common/ui/Checkbox';
import { Radio } from '@/components/common/ui/Radio';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/common/ui/Dialog';
import { useAlertTypeLookups, useAlertTypeStore } from '@/stores/alert-type.store';
import { useLocalized } from '@/shared/hooks/use-localized.hook';
import { createPortsStore } from '@/stores/ports.store';
import { Icon } from '@/components/common/ui/Icon';
import { TripLocation, TripCategory, OrderDirection, TripFilterOrderBy, AlertAcknowledgement } from '@/shared/enums';
import { useTripFiltersStore } from '@/stores/trip-filters.store';

import { LoaderButton } from '../ui/LoaderButton';

interface LocalProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    showPortIn?: boolean;
    showPortOut?: boolean;
    showWarnings?: boolean;
    showTruck?: boolean;
    showDate?: boolean;
    showSorting?: boolean;
    showTripStatus?: boolean;
    onApply: () => void;
    portalContainer?: HTMLElement;
}

export default function TripFilterDialog({
    open,
    onOpenChange,
    showPortIn = true,
    showPortOut = true,
    showWarnings = true,
    showTruck = true,
    showDate = true,
    showSorting = true,
    showTripStatus = true,
    onApply,
    portalContainer,
}: Readonly<LocalProps>) {
    const { t } = useTranslation();
    const { t: tWarnings } = useTranslation('warnings');
    const useTripFilterDialogPortsStore = useMemo(() => createPortsStore('tripFilterDialog'), []);
    const loadPorts = useTripFilterDialogPortsStore((state) => state.loadPorts);
    const portsIn = useTripFilterDialogPortsStore((state) => state.ports);
    const portsOut = useTripFilterDialogPortsStore((state) => state.ports);
    const alertTypes = useAlertTypeLookups();
    const loadAlertTypes = useAlertTypeStore((state) => state.loadAlertTypes);
    const TRIP_LOCATION_OPTIONS = [
        { label: 'insideEntryPort', value: TripLocation.ENTRY },
        { label: 'onRoute', value: TripLocation.ON_ROUTE },
        { label: 'inExitPort', value: TripLocation.EXIT },
    ] as const;

    // Create refs to avoid function dependency issues
    const loadAlertTypesRef = useRef(loadAlertTypes);
    const loadPortsRef = useRef(loadPorts);

    // Update refs when functions change
    loadAlertTypesRef.current = loadAlertTypes;
    loadPortsRef.current = loadPorts;

    // Load alert types when component mounts
    useEffect(() => {
        loadAlertTypesRef.current({ PageNumber: 1 });
        loadPortsRef.current({ pageNumber: 1, pageSize: 1000 });
    }, []);

    // Store selectors (fine-grained)
    // const filters = useTripFiltersStore((state) => state.filters);
    const draft = useTripFiltersStore((state) => state.draftFilters);
    const appliedFiltersCount = useTripFiltersStore((state) => state.appliedFiltersCount);
    const updateListSection = useTripFiltersStore((state) => state.updateListSection);
    const setFilter = useTripFiltersStore((state) => state.setFilter);
    const resetFilters = useTripFiltersStore((state) => state.resetFilters);
    const applyFilters = useTripFiltersStore((state) => state.applyFilters);
    const localized = useLocalized();

    // UI-only state
    const [activeIndex, setActiveIndex] = useState<number | number[]>([0, 1, 2, 3, 4, 5]);
    const [isApplying, setIsApplying] = useState(false);
    const handleApply = async () => {
        setIsApplying(true);
        applyFilters((_) => onApply());
        setIsApplying(false);
        onOpenChange(false);
    };

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent
                portalContainer={portalContainer}
                className="filter-container w-full max-w-[95vw] h-auto max-h-[80vh] p-0 rounded-2xl shadow-lg">
                <DialogHeader className="px-6 py-3 pb-3 border-b-2">
                    <DialogTitle className="text-xl  flex items-center gap-3">
                        <div className="flex items-center gap-1">
                            <Icon name="filter" />
                            <span>{t('common.advancedFilter')}</span>
                        </div>
                    </DialogTitle>
                </DialogHeader>
                <DialogDescription />
                <div className="overflow-y-auto px-3">
                    <Accordion
                        className="flex flex-wrap gap-3 justify-center"
                        multiple
                        activeIndex={activeIndex}
                        onTabChange={(e) => setActiveIndex(e.index)}>
                        {/* Port In */}
                        {showPortIn && (
                            <AccordionTab
                                headerClassName="accordion-header"
                                className="md:w-[91vw] lg:w-[93vw]"
                                contentClassName="accordion-body"
                                header={
                                    <div className="flex items-center justify-between gap-1">
                                        <div className="flex items-center gap-1">
                                            <Icon name="enteringGeoFence" />
                                            <span>{t('common.portIn')}</span>
                                        </div>
                                        <div>
                                            {Array.isArray(activeIndex) && activeIndex.includes(0) ? (
                                                <ChevronUp className="w-4 h-4" />
                                            ) : (
                                                <ChevronDown className="w-4 h-4  text-gray-400" />
                                            )}
                                        </div>
                                    </div>
                                }>
                                <div className="grid md:grid-cols-3 lg:grid-cols-5 gap-x-3 gap-y-1">
                                    <Checkbox
                                        isSelectAll={true}
                                        label={t('filter.selectAll')}
                                        checked={draft.inPorts?.length === portsIn.length && portsIn.length > 0}
                                        onChange={(checked: boolean) => {
                                            setFilter('inPorts', checked ? portsIn.map((p) => p.id) : []);
                                        }}
                                    />
                                </div>
                                <div className="grid md:grid-cols-3 lg:grid-cols-5 gap-x-3 gap-y-1">
                                    {portsIn.map((item) => (
                                        <Checkbox
                                            key={item.id}
                                            label={localized(item.name)}
                                            checked={draft.inPorts?.includes(item.id) ?? false}
                                            onChange={(checked: boolean) =>
                                                updateListSection('inPorts', item.id, checked)
                                            }
                                        />
                                    ))}
                                </div>
                            </AccordionTab>
                        )}

                        {/* Port Out */}
                        {showPortOut && (
                            <AccordionTab
                                headerClassName="accordion-header"
                                className="md:w-[91vw] lg:w-[93vw]"
                                contentClassName="accordion-body"
                                header={
                                    <div className="flex items-center justify-between gap-1">
                                        <div className="flex items-center gap-1">
                                            <Icon name="leavingGeofence" />
                                            <span>{t('common.portOut')}</span>
                                        </div>
                                        <div>
                                            {Array.isArray(activeIndex) && activeIndex.includes(1) ? (
                                                <ChevronUp className="w-4 h-4" />
                                            ) : (
                                                <ChevronDown className="w-4 h-4  text-gray-400" />
                                            )}
                                        </div>
                                    </div>
                                }>
                                <div className="grid md:grid-cols-3 lg:grid-cols-5 gap-x-3 gap-y-1">
                                    <Checkbox
                                        isSelectAll={true}
                                        label={t('filter.selectAll')}
                                        checked={draft.outPorts?.length === portsOut.length && portsOut.length > 0}
                                        onChange={(checked: boolean) => {
                                            setFilter('outPorts', checked ? portsOut.map((p) => p.id) : []);
                                        }}
                                    />
                                </div>
                                <div className="grid md:grid-cols-4 lg:grid-cols-5 gap-x-3 gap-y-1">
                                    {portsOut.map((item) => (
                                        <Checkbox
                                            key={item.id}
                                            label={localized(item.name)}
                                            checked={draft.outPorts?.includes(item.id) ?? false}
                                            onChange={(checked: boolean) =>
                                                updateListSection('outPorts', item.id, checked)
                                            }
                                        />
                                    ))}
                                </div>
                            </AccordionTab>
                        )}

                        {/* Warnings */}
                        {showWarnings && (
                            <AccordionTab
                                headerClassName="accordion-header"
                                className="md:w-[91vw] lg:w-[93vw]"
                                contentClassName="accordion-body"
                                header={
                                    <div className="flex items-center justify-between gap-1">
                                        <div className="flex items-center gap-1">
                                            <Icon name="alert" />
                                            <span>{t('filter.warnings')}</span>
                                        </div>
                                        <div>
                                            {Array.isArray(activeIndex) && activeIndex.includes(2) ? (
                                                <ChevronUp className="w-4 h-4" />
                                            ) : (
                                                <ChevronDown className="w-4 h-4  text-gray-400" />
                                            )}
                                        </div>
                                    </div>
                                }>
                                <div className="flex items-center justify-start gap-3 border-b p-3">
                                    <Radio
                                        label={tWarnings('activeWarnings')}
                                        name="warnings-radio-group"
                                        onChange={() => setFilter('activeAlertsOnly', true)}
                                        checked={draft.activeAlertsOnly === true}
                                    />
                                    <Radio
                                        label={tWarnings('allWarnings')}
                                        name="warnings-radio-group"
                                        onChange={() => setFilter('activeAlertsOnly', false)}
                                        checked={draft.activeAlertsOnly === false}
                                    />
                                </div>
                                {/* Warning acknowledgement filter */}
                                <div className="flex items-center justify-start gap-3 border-b p-3">
                                    <Radio
                                        label={tWarnings('acknowledgeWarnings')}
                                        name="acknowledgement-radio-group"
                                        onChange={() =>
                                            setFilter('alertAcknowledgement', AlertAcknowledgement.ACKNOWLEDGED)
                                        }
                                        checked={draft.alertAcknowledgement === AlertAcknowledgement.ACKNOWLEDGED}
                                    />
                                    <Radio
                                        label={tWarnings('notAcknowledgedWarnings')}
                                        name="acknowledgement-radio-group"
                                        onChange={() =>
                                            setFilter('alertAcknowledgement', AlertAcknowledgement.NOT_ACKNOWLEDGED)
                                        }
                                        checked={draft.alertAcknowledgement === AlertAcknowledgement.NOT_ACKNOWLEDGED}
                                    />
                                    <Radio
                                        label={tWarnings('allWarnings')}
                                        name="acknowledgement-radio-group"
                                        onChange={() => setFilter('alertAcknowledgement', AlertAcknowledgement.ALL)}
                                        checked={draft.alertAcknowledgement === AlertAcknowledgement.ALL}
                                    />
                                </div>

                                <div className="grid md:grid-cols-3 lg:grid-cols-5 gap-x-3 gap-y-1">
                                    <Checkbox
                                        isSelectAll={true}
                                        label={t('filter.selectAll')}
                                        checked={
                                            draft.alertTypes?.length === alertTypes.length && alertTypes.length > 0
                                        }
                                        onChange={(checked: boolean) => {
                                            setFilter('alertTypes', checked ? alertTypes.map((p) => p.id) : []);
                                        }}
                                    />
                                </div>
                                <div className="grid md:grid-cols-4 lg:grid-cols-4 gap-x-3 gap-y-1">
                                    {alertTypes.map((item) => (
                                        <Checkbox
                                            key={item.id}
                                            label={localized(item.name)}
                                            checked={draft.alertTypes?.includes(item.id) ?? false}
                                            onChange={(checked: boolean) =>
                                                updateListSection('alertTypes', item.id, checked)
                                            }
                                        />
                                    ))}
                                </div>
                            </AccordionTab>
                        )}

                        {/* Truck */}
                        {showTruck && (
                            <AccordionTab
                                headerClassName="accordion-header"
                                className="md:w-[91vw] lg:w-[93vw]"
                                contentClassName="accordion-body"
                                header={
                                    <div className="flex items-center justify-between gap-1">
                                        <div className="flex items-center gap-1">
                                            <Icon name="truck" />
                                            <span>{t('filter.truckInfo')}</span>
                                        </div>
                                        <div>
                                            {Array.isArray(activeIndex) && activeIndex.includes(3) ? (
                                                <ChevronUp className="w-4 h-4" />
                                            ) : (
                                                <ChevronDown className="w-4 h-4  text-gray-400" />
                                            )}
                                        </div>
                                    </div>
                                }>
                                <div className="grid md:grid-cols-3 lg:grid-cols-3 gap-x-5 gap-y-3 border-b p-3">
                                    <div className="text-[15px]">
                                        <label className="mb-1 block">{t('filter.transitNumber')}</label>
                                        <Input
                                            type="number"
                                            min={0}
                                            value={draft.transitNumber ?? ''}
                                            placeholder={t('placeholder.transitNumber')}
                                            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                setFilter(
                                                    'transitNumber',
                                                    e.target.value === '' ? null : Number(e.target.value),
                                                )
                                            }
                                        />
                                    </div>
                                    <div className="text-[15px]">
                                        <label className="mb-1 block">{t('filter.transitSequenceNumber')}</label>
                                        <Input
                                            type="text"
                                            placeholder={t('placeholder.transitSequenceNumber')}
                                            value={draft.transitSeqNumber ?? ''}
                                            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                setFilter('transitSeqNumber', e.target.value)
                                            }
                                        />
                                    </div>
                                    <div className="text-[15px]">
                                        <label className="mb-1 block">{t('filter.tripCode')}</label>
                                        <Input
                                            type="text"
                                            placeholder={t('placeholder.tripCode')}
                                            value={draft.tipCode ?? ''}
                                            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                setFilter('tipCode', e.target.value)
                                            }
                                        />
                                    </div>
                                    <div className="text-[15px]">
                                        <label className="mb-1 block">{t('filter.trackerNumber')}</label>
                                        <Input
                                            type="text"
                                            placeholder={t('placeholder.trackerNumber')}
                                            value={draft.trackerNumber ?? ''}
                                            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                setFilter('trackerNumber', e.target.value)
                                            }
                                        />
                                    </div>
                                    <div className="text-[15px]">
                                        <label className="mb-1 block">{t('filter.plateNumber')}</label>
                                        <Input
                                            type="text"
                                            placeholder={t('placeholder.plateNumber')}
                                            value={draft.plateNumber ?? ''}
                                            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                setFilter('plateNumber', e.target.value)
                                            }
                                        />
                                    </div>
                                    <div className="text-[15px]">
                                        <label className="mb-1 block">{t('filter.driverName')}</label>
                                        <Input
                                            type="text"
                                            placeholder={t('placeholder.driverName')}
                                            value={draft.driverName ?? ''}
                                            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                setFilter('driverName', e.target.value)
                                            }
                                        />
                                    </div>
                                </div>

                                <div className="grid gap-x-5 gap-y-3 border-b p-3">
                                    {/* Trip Category (radio group) */}
                                    <div className="flex items-center gap-1">
                                        <Route className="w-4 h-4 text-green-700" />
                                        <span className="font-bold">{t('filter.tripCategory')}:</span>
                                    </div>
                                    <div className=" text-[15px] flex items-center justify-start gap-4 px-3 ">
                                        <Radio
                                            label={t('filter.allTrips')}
                                            name="trip-category-radio-group"
                                            onChange={() => setFilter('tripCategory', TripCategory.ALL_TRIPS)}
                                            checked={draft.tripCategory == TripCategory.ALL_TRIPS}
                                        />
                                        <Radio
                                            label={t('filter.myRoutes')}
                                            name="trip-category-radio-group"
                                            onChange={() => setFilter('tripCategory', TripCategory.MY_PORTS)}
                                            checked={draft.tripCategory == TripCategory.MY_PORTS}
                                        />
                                        <Radio
                                            label={t('filter.suspiciousTrips')}
                                            name="trip-category-radio-group"
                                            onChange={() => setFilter('tripCategory', TripCategory.SUSPICIOUS_TRIPS)}
                                            checked={draft.tripCategory == TripCategory.SUSPICIOUS_TRIPS}
                                        />
                                        <Radio
                                            label={t('filter.focusedTrips')}
                                            name="trip-category-radio-group"
                                            onChange={() => setFilter('tripCategory', TripCategory.FOCUSED_TRIPS)}
                                            checked={draft.tripCategory == TripCategory.FOCUSED_TRIPS}
                                        />
                                        <Radio
                                            label={t('filter.stoppedTrips')}
                                            name="trip-category-radio-group"
                                            onChange={() => setFilter('tripCategory', TripCategory.STOPPED_TRIPS)}
                                            checked={draft.tripCategory == TripCategory.STOPPED_TRIPS}
                                        />
                                    </div>
                                </div>

                                {showTripStatus && (
                                    <>
                                        <div className="grid gap-x-5 gap-y-3 border-b p-3">
                                            {/* Active Trips Only (mapped to store.ActiveTripsOnly) */}
                                            <div className="flex items-center gap-1">
                                                <Route className="w-4 h-4 text-green-700" />
                                                <span className="font-bold">{t('filter.tripStatus')}:</span>
                                            </div>

                                            <div className="flex items-center gap-4 px-3">
                                                <Radio
                                                    label={t('filter.active')}
                                                    name="trip-status-radio-group"
                                                    onChange={() => setFilter('activeTripsOnly', true)}
                                                    checked={draft.activeTripsOnly === true}
                                                />
                                                <Radio
                                                    label={t('filter.ended')}
                                                    name="trip-status-radio-group"
                                                    onChange={() => setFilter('activeTripsOnly', false)}
                                                    checked={draft.activeTripsOnly === false}
                                                />
                                            </div>
                                        </div>
                                    </>
                                )}

                                {/* Trip Location (checkbox group) */}
                                <div className="grid gap-x-5 gap-y-3 border-b p-3">
                                    <div className="flex items-center justify-between gap-1">
                                        <div className="flex items-center  gap-1">
                                            <Icon name="shipmentTracking" />
                                            <span className="font-bold">{t('filter.tripLocation')}:</span>
                                        </div>

                                        {/* Right side: button */}
                                        <button
                                            className="text-green-700 text-sm font-bold py-1 hover:text-green-800 transition-colors "
                                            onClick={() => {
                                                const allSelected = TRIP_LOCATION_OPTIONS.every(({ value }) =>
                                                    draft.tripLocations.includes(value),
                                                );
                                                setFilter(
                                                    'tripLocations',
                                                    allSelected ? [] : TRIP_LOCATION_OPTIONS.map(({ value }) => value),
                                                );
                                            }}>
                                            {TRIP_LOCATION_OPTIONS.every(({ value }) =>
                                                draft.tripLocations.includes(value),
                                            )
                                                ? t('filter.deSelectAll')
                                                : t('filter.selectAll')}
                                        </button>
                                    </div>
                                    <div className="flex items-center gap-4 px-3">
                                        <Checkbox
                                            label={t('filter.onRoute')}
                                            checked={draft.tripLocations?.includes(TripLocation.ON_ROUTE) ?? false}
                                            onChange={(checked: boolean) =>
                                                updateListSection('tripLocations', TripLocation.ON_ROUTE, checked)
                                            }
                                        />
                                        <Checkbox
                                            label={t('filter.inExitBorder')}
                                            checked={draft.tripLocations?.includes(TripLocation.EXIT) ?? false}
                                            onChange={(checked: boolean) =>
                                                updateListSection('tripLocations', TripLocation.EXIT, checked)
                                            }
                                        />
                                        <Checkbox
                                            label={t('filter.inEntryBorder')}
                                            checked={draft.tripLocations?.includes(TripLocation.ENTRY) ?? false}
                                            onChange={(checked: boolean) =>
                                                updateListSection('tripLocations', TripLocation.ENTRY, checked)
                                            }
                                        />
                                    </div>
                                </div>
                            </AccordionTab>
                        )}

                        {/* Date */}
                        {showDate && (
                            <AccordionTab
                                headerClassName="accordion-header"
                                className="md:w-[44.25vw] lg:w-[46vw]"
                                contentClassName="accordion-body"
                                header={
                                    <div className="flex items-center justify-between gap-1">
                                        <div className="flex items-center gap-1">
                                            <span className="flex items-center">
                                                <FaCalendarDays className="text-[15px] text-[#1b8354]" />
                                                {t('filter.date')}
                                            </span>
                                        </div>
                                        <div>
                                            {Array.isArray(activeIndex) && activeIndex.includes(4) ? (
                                                <ChevronUp className="w-4 h-4" />
                                            ) : (
                                                <ChevronDown className="w-4 h-4  text-gray-400" />
                                            )}
                                        </div>
                                    </div>
                                }>
                                <div className="text-[15px]">
                                    <label className="mb-1 block" htmlFor="transactionDate">
                                        {t('filter.transactionDate')}
                                    </label>
                                    <DatePicker
                                        value={draft.transDate ?? null}
                                        onChange={(v: Date | null) => setFilter('transDate', v)}
                                        placeholder="dd/mm/yyyy"
                                        className="w-full"
                                    />
                                </div>

                                <div className="grid grid-cols-2 gap-x-5 gap-y-1 mt-9 mb-3">
                                    <div className="text-[15px]">
                                        <label className="mb-1 block" htmlFor="startDate">
                                            {t('filter.startDate')}
                                        </label>
                                        <DatePicker
                                            value={draft.tripStartDate ?? null}
                                            onChange={(v: Date | null) => setFilter('tripStartDate', v)}
                                            placeholder="dd/mm/yyyy"
                                        />
                                    </div>

                                    <div className="text-[15px]">
                                        <label className="mb-1 block" htmlFor="endDate">
                                            {t('filter.endDate')}
                                        </label>
                                        <DatePicker
                                            value={draft.tripEndDate ?? null}
                                            onChange={(v: Date | null) => setFilter('tripEndDate', v)}
                                            placeholder="dd/mm/yyyy"
                                        />
                                    </div>
                                </div>
                            </AccordionTab>
                        )}

                        {/* Sorting */}
                        {showSorting && (
                            <AccordionTab
                                headerClassName="accordion-header"
                                className="md:w-[44.25vw] lg:w-[46vw]"
                                contentClassName="accordion-body"
                                header={
                                    <div className="flex items-center justify-between gap-1">
                                        <div className="flex items-center gap-1">
                                            <span className="flex items-center ">
                                                <IoFilter className="text-[18px] text-[#1b8354]" />
                                                {t('filter.orderBy')}
                                            </span>
                                        </div>
                                        <div>
                                            {Array.isArray(activeIndex) && activeIndex.includes(4) ? (
                                                <ChevronUp className="w-4 h-4" />
                                            ) : (
                                                <ChevronDown className="w-4 h-4  text-gray-400" />
                                            )}
                                        </div>
                                    </div>
                                }>
                                <div className="grid grid-cols-2 gap-x-5 gap-y-1">
                                    <Radio
                                        label={t('filter.descending')}
                                        name="sorting-method"
                                        onChange={() => setFilter('orderDir', OrderDirection.DESCENDING)}
                                        checked={draft.orderDir == OrderDirection.DESCENDING}
                                    />
                                    <Radio
                                        label={t('filter.ascending')}
                                        name="sorting-method"
                                        onChange={() => setFilter('orderDir', OrderDirection.ASCENDING)}
                                        checked={draft.orderDir == OrderDirection.ASCENDING}
                                    />
                                </div>

                                <hr className="my-2" />

                                <div className="grid grid-cols-2 gap-x-5 gap-y-1">
                                    <Radio
                                        label={t('filter.tripCode')}
                                        name="sorting-data"
                                        defaultChecked={
                                            draft.orderBy === TripFilterOrderBy.TRIP_CODE || draft.orderBy == null
                                        }
                                        onChange={() => setFilter('orderBy', TripFilterOrderBy.TRIP_CODE)}
                                        checked={draft.orderBy == TripFilterOrderBy.TRIP_CODE}
                                    />
                                    <Radio
                                        label={t('filter.transitNumber')}
                                        name="sorting-data"
                                        onChange={() => setFilter('orderBy', TripFilterOrderBy.TRANSIT_NUMBER)}
                                        checked={draft.orderBy == TripFilterOrderBy.TRANSIT_NUMBER}
                                    />
                                    <Radio
                                        label={t('filter.entryPort')}
                                        name="sorting-data"
                                        onChange={() => setFilter('orderBy', TripFilterOrderBy.ENTRY_PORT)}
                                        checked={draft.orderBy == TripFilterOrderBy.ENTRY_PORT}
                                    />
                                    <Radio
                                        label={t('filter.exitPort')}
                                        name="sorting-data"
                                        onChange={() => setFilter('orderBy', TripFilterOrderBy.EXIT_PORT)}
                                        checked={draft.orderBy == TripFilterOrderBy.EXIT_PORT}
                                    />
                                    <Radio
                                        label={t('filter.transitDate')}
                                        name="sorting-data"
                                        onChange={() => setFilter('orderBy', TripFilterOrderBy.TRANSIT_DATE)}
                                        checked={draft.orderBy == TripFilterOrderBy.TRANSIT_DATE}
                                    />
                                    <Radio
                                        label={t('filter.entryDate')}
                                        name="sorting-data"
                                        onChange={() => setFilter('orderBy', TripFilterOrderBy.START_DATE)}
                                        checked={draft.orderBy == TripFilterOrderBy.START_DATE}
                                    />
                                    <Radio
                                        label={t('filter.exitDate')}
                                        name="sorting-data"
                                        onChange={() => setFilter('orderBy', TripFilterOrderBy.END_DATE)}
                                        checked={draft.orderBy == TripFilterOrderBy.END_DATE}
                                    />
                                    <Radio
                                        label={t('filter.createdDate')}
                                        name="sorting-data"
                                        onChange={() => setFilter('orderBy', TripFilterOrderBy.CREATION_DATE)}
                                        checked={draft.orderBy == TripFilterOrderBy.CREATION_DATE}
                                    />
                                </div>
                            </AccordionTab>
                        )}
                    </Accordion>
                </div>
                <div className="ms-auto pb-4 px-10 flex items-center gap-3">
                    <Button variant="outline" className="mx-3" onClick={() => resetFilters()}>
                        <RefreshCwIcon /> {t('filter.reset')}
                    </Button>
                    <LoaderButton
                        loading={isApplying}
                        defaultText={`${t('filter.applyFilter')} (${appliedFiltersCount})`}
                        isLoadingText={`${t('filter.applyingFilter')} (${appliedFiltersCount})`}
                        icon={<FilterIcon />}
                        variant="success"
                        onClick={handleApply}
                    />
                </div>
            </DialogContent>
        </Dialog>
    );
}
