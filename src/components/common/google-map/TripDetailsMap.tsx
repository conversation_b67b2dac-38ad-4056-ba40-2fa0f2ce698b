import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useSearchParams } from 'react-router-dom';

import { useTripAlertsStore, type AlertWithViewOptions } from '@/stores/trip-alerts.store';
import { useTripDetailStore } from '@/stores/trip-detail.store';
import { getMapPointIcon } from '@/shared/utils/map.utils';
import { useTripPingsStore } from '@/stores/trip-ping.store';
import { useTripActivitiesStore } from '@/stores/trip-activities.store';
import { useTripReplayStore } from '@/stores/trip-route-replay.store';
import type { Location } from '@/shared/types/common';

import LocationMarkerAnimated, { type LocationWithAlerts } from './LocationMarkerAnimated';
import { BaseMap, type MapPoint, type MapPort, type MapTrip, type MapAlert } from './BaseMap';
import { TripMarker } from './TripMarker';
import { MapRoute } from './MapRoute';
import { AlertMarkerWithInfoWindow } from './marker-with-info-window/AlertMarkerWithInfoWindow';
import { ActivityMarkerWithInfoWindow } from './marker-with-info-window/ActivityMarkerWithInfoWindow';
import { PingRoute } from './PingRoute';
import { LocationMarkerWithInfoWindow } from './marker-with-info-window/LocationMarkerWithInfoWindow';

interface TripDetailsMapProps {
    /**
     * Initial center point of the map (lat/lng).
     */
    center: { lat: number; lng: number };

    /**
     * The main trip object (id, name, center, path, etc.).
     */
    trip: MapTrip;

    /**
     * Optional checkpoint points to render as markers on the map.
     * Shared across maps (e.g., trip routes).
     */
    points?: MapPoint[];

    /**
     * Optional ports to render as markers on the map.
     * Shared across maps (e.g., loading/unloading points).
     */
    ports?: MapPort[];

    /**
     * Optional custom menu or control element.
     * Rendered in the top-right control area of the map.
     */
    menu?: React.ReactNode;
}

export function TripDetailsMap({ trip, points = [], menu, center }: TripDetailsMapProps) {
    const { t } = useTranslation();
    const navigate = useNavigate();

    // Separate initial center from dynamic center state
    const [initialCenter] = useState(center); // Never changes after mount

    const tripPings = useTripPingsStore((s) => s.selectedTripPings);
    const allTripPings = useTripPingsStore((s) => s.tripPings);

    // Create ping path for route (convert to lat/lng format)
    const pingPath = useMemo(
        () =>
            tripPings.map((ping) => ({
                lat: ping.location.latitude,
                lng: ping.location.longitude,
            })),
        [tripPings],
    );

    const playbackSpeed = useTripReplayStore((state) => state.speed);
    const isPlaying = useTripReplayStore((state) => state.isPlaying);
    const setIsPlaying = useTripReplayStore((state) => state.changeRoutePlayingStatus);
    const direction = useTripReplayStore((state) => state.direction);
    const setDirection = useTripReplayStore((state) => state.changeRoutePlayingDirection);

    const selectedAlerts = useTripAlertsStore((s) => s.selectedTripAlerts);
    const selectedActivities = useTripActivitiesStore((s) => s.selectedActivities);

    const handleDoneAnimation = () => {
        setIsPlaying(false);
        setDirection('forward');
    };

    const isLoadingActivities = useTripActivitiesStore((s) => s.isLoading);
    const selectTripActivityById = useTripActivitiesStore((s) => s.selectTripActivityById);
    const isLoadingAlerts = useTripAlertsStore((s) => s.isLoading);
    const selectTripAlertById = useTripAlertsStore((s) => s.selectTripAlertById);
    const clearSelectedTripActivities = useTripActivitiesStore((s) => s.clearSelectedTripActivities);
    const clearSelectedTripAlerts = useTripAlertsStore((s) => s.clearSelectedTripAlerts);
    const tripAlertsMapperByLocation = useTripAlertsStore((s) => s.tripAlertsMapperByLocation);

    // --- Server returns the path in reverse order (first item = last trip point), so I use reverse() to fix it.
    const path: LocationWithAlerts[] = useMemo(() => {
        return allTripPings
            .map((item) => {
                const [lat, lng] = [item.location.latitude, item.location.longitude];
                const location: Location = { lat, lng };
                const alerts: AlertWithViewOptions[] = tripAlertsMapperByLocation.get(`${lat},${lng}`) || []; //--- Note that no space before or after the ',' => (lat,lng)

                return { location, alerts };
            })
            .reverse();
    }, [allTripPings, tripAlertsMapperByLocation]);

    const { tripDetail, EntryPort, ExitPort, loadTripDetail, loadEntryPort, loadExitPort } = useTripDetailStore();

    useEffect(() => {
        if (trip.id) loadTripDetail({ id: trip.id });
    }, [trip.id, loadTripDetail]);

    useEffect(() => {
        if (tripDetail?.entryPort?.id) {
            loadEntryPort(tripDetail.entryPort.id);
        }
        if (tripDetail?.exitPort?.id) {
            loadExitPort(tripDetail.exitPort.id);
        }
    }, [tripDetail, loadEntryPort, loadExitPort]);

    const entryExitPorts: MapPort[] = [EntryPort, ExitPort]
        .filter((port): port is NonNullable<typeof port> => port !== null)
        .map((port) => ({
            ...port,
            icon: getMapPointIcon(port.type, 'port'),
        }));

    const [queryParams] = useSearchParams();
    const alertId = queryParams.get('alertId');
    const activityId = queryParams.get('activityId');
    const isOpen: boolean = queryParams.get('isOpen') == 'true' ? true : false;
    const resetUrl = () => {
        if (alertId || activityId || isOpen) {
            navigate(`/trips/${trip.id}/details`, { replace: true });
        }
    };

    // Select alerts/activities without auto-centering camera
    useEffect(() => {
        if (!alertId) return;
        selectTripAlertById(Number(alertId), { isOpen });
    }, [alertId, selectTripAlertById, isLoadingAlerts, isOpen]);

    useEffect(() => {
        if (!activityId) return;
        selectTripActivityById(Number(activityId));
    }, [activityId, selectTripActivityById, isLoadingActivities, isOpen]);

    // dispose & cleanup -- @0x03r00
    useEffect(() => {
        return () => {
            clearSelectedTripActivities();
            clearSelectedTripAlerts();
        };
    }, [clearSelectedTripActivities, clearSelectedTripAlerts]);

    return (
        <BaseMap points={points} ports={entryExitPorts} menu={menu} center={initialCenter} zoom={10}>
            {/* Render the trip as a marker */}
            <TripMarker key={trip.id} trip={trip} onClick={() => {}} title={t('common.currentTripLocation')} />

            {/* Draw animated route between entry ping pointes */}
            {allTripPings.length && (
                <LocationMarkerAnimated
                    path={path}
                    speed={playbackSpeed}
                    isPlaying={isPlaying}
                    direction={direction}
                    handleDoneAnimation={handleDoneAnimation}
                />
            )}

            {/* Draw route between entry and exit ports */}
            {EntryPort && ExitPort && (
                <MapRoute
                    startPoint={{ lat: EntryPort.lat, lng: EntryPort.long }}
                    endPoint={{ lat: ExitPort.lat, lng: ExitPort.long }}
                    preserveViewport={true}
                />
            )}

            {tripPings && tripPings.length > 0 && (
                <>
                    {/* Draw route between pings with directional arrows */}
                    <PingRoute
                        pings={pingPath}
                        strokeColor="#3b82f6"
                        strokeWeight={3}
                        strokeOpacity={0.6}
                        zIndex={999}
                        showArrows={true}
                    />

                    {/* Render individual ping markers */}
                    {tripPings.map((ping) => (
                        <LocationMarkerWithInfoWindow key={ping.id} location={ping} />
                    ))}
                </>
            )}

            {selectedActivities &&
                Array.from(selectedActivities).map((activity) => (
                    <ActivityMarkerWithInfoWindow key={activity.id} isOpen={isOpen} activity={activity} />
                ))}

            {selectedAlerts &&
                Array.from(selectedAlerts).map((alert) => (
                    // Array.from([testAlert]).map((alert) => (
                    <AlertMarkerWithInfoWindow
                        onRemoveMarkerClicked={resetUrl}
                        key={`point-${alert.id}`}
                        isOpen={alert.viewOptions?.isOpen}
                        mode={alert.viewOptions?.mode}
                        alert={alert as unknown as MapAlert}
                    />
                ))}
        </BaseMap>
    );
}
