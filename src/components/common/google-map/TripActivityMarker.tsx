import { AdvancedMarker } from '@vis.gl/react-google-maps';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';

import type { TripActivity } from '@/infrastructure/api/trip-activities/types';
import activityIcon from '@/assets/imgs/svg/activityMarker.svg';

export type TripActivityMarkerProps = {
    activity: TripActivity;
    onClick?: () => void;
    setMarkerRef?: (marker: google.maps.marker.AdvancedMarkerElement | null, key: string) => void;
    zIndex?: number;
    markerKey?: string | number | null;
};

export function TripActivityMarker({
    activity,
    onClick = undefined,
    setMarkerRef,
    markerKey = null,
    zIndex = 1,
}: TripActivityMarkerProps) {
    const { t } = useTranslation();
    const { lat, lng } = { lat: activity.location.latitude, lng: activity.location.longitude };

    const key = markerKey || `${lat}-${lng}`;

    const ref = useCallback(
        (marker: google.maps.marker.AdvancedMarkerElement | null) => {
            try {
                // forward marker reference if caller provided setMarkerRef
                setMarkerRef?.(marker, key.toString());
                // setMarkerRef?.(marker as unknown as ClustererMarker | null, markerKey);
            } catch {
                // ignore forwarding failures; setMarkerRef is optional
            }
        },
        [setMarkerRef, key],
    );

    return (
        <AdvancedMarker
            ref={ref}
            key={key}
            position={{ lat, lng }}
            onClick={onClick}
            zIndex={zIndex}
            title={t('activityLogs.tripActivity')}>
            <img src={activityIcon} alt={t('activityLogs.tripActivity')} />
        </AdvancedMarker>
    );
}
