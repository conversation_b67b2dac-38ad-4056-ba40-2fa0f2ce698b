// MonitorMap.tsx
/**
 * MonitorMap
 *
 * A composed map component that builds on `BaseMap` to display an overview
 * of trips for monitoring purposes.
 *
 * Responsibilities:
 *  - Render shared map layers from BaseMap (points, ports, controls).
 *  - Render trip locations either as clustered markers or as individual markers
 *    depending on the display mode from `useDisplaySettingsStore`.
 *  - Open a single Trip InfoWindow using the global UI store.
 *  - Auto-open the trip info window when a trip becomes highlighted in the
 *    trip location store.
 *  - Attach the MapCameraFollower to follow trips when needed.
 *
 *
 * Example:
 * ```tsx
 * <MonitorMap
 *   points={checkpoints}
 *   ports={ports}
 *   tripLocations={trips}
 *   menu={<MyMapMenu />}
 * />
 * ```
 */

import React, { useEffect } from 'react';

import { useUIStore } from '@/stores/ui.store';
import { useDisplaySettingsStore } from '@/stores/display-settings.store';
import { useTripLocationStore } from '@/stores/trip-location.store';

import { BaseMap, type ActiveEntity, type MapPoint, type MapPort, type MapTrip } from './BaseMap';
import { ClusteredTripMarkers } from './ClusteredTripMarkers';
import { TripMarker } from './TripMarker';
import { MapCameraFollower } from './MapCameraFollower';
import { TripDetailsInfoWindow } from './info-window/TripDetailsInfoWindow';

export interface MonitorMapProps {
    /**
     * Checkpoint points to render as markers (shared across maps).
     */
    points?: MapPoint[];

    /**
     * Ports to render as markers (shared across maps).
     */
    ports?: MapPort[];

    /**
     * Trip locations to render in the overview map. Each item should be a MapTrip.
     */
    tripLocations?: MapTrip[];

    /**
     * Optional custom menu or control node rendered in the top-right map control.
     */
    menu?: React.ReactNode;
}

/**
 * MonitorMap
 *
 * Composed map used for monitoring many trips. It:
 *  - Delegates shared rendering to BaseMap (points, ports, controls).
 *  - Renders trips either as clustered markers (`ClusteredTripMarkers`) or as
 *    individual `TripMarker`s depending on `tripDisplayMode`.
 *  - Uses the app-wide UI store to open/close the single active InfoWindow.
 *  - Shows a `TripDetailsInfoWindow` for the currently active trip entity.
 */
export function MonitorMap({ tripLocations = [], points = [], ports = [], menu }: MonitorMapProps) {
    // UI store (single source of truth for InfoWindow state)
    const activeInfoWindow = useUIStore((s) => s.activeInfoWindow);
    const setActiveInfoWindow = useUIStore((s) => s.setActiveInfoWindow);

    // Display mode for trips: 'cluster' | 'individual'
    const tripDisplayMode = useDisplaySettingsStore((s) => s.settings.tripDisplayMode);

    /**
     * Open trip info window in the UI store.
     * Avoid redundant writes when the same trip is already active.
     */
    const openTripInfoWindow = (trip: MapTrip): void => {
        if (activeInfoWindow && activeInfoWindow.kind === 'trip' && activeInfoWindow.id === trip.id) return;
        setActiveInfoWindow({ kind: 'trip', id: trip.id });
    };

    /**
     * Resolve active entity from the global activeInfoWindow to local `ActiveEntity`
     * so we can render the corresponding InfoWindow component.
     */
    const activeEntity: ActiveEntity | null = (() => {
        if (!activeInfoWindow) return null;
        const { kind, id } = activeInfoWindow;
        if (kind === 'trip') {
            const found = tripLocations.find((t) => String(t.id) === String(id));
            return found ? { type: 'trip', data: found } : null;
        }
        return null;
    })();

    // When a trip is highlighted in the trip location store, open its InfoWindow automatically.
    const highlightedTripId = useTripLocationStore((s) => s.highlightedTripId);
    useEffect(() => {
        if (!highlightedTripId) return;
        const trip = tripLocations.find((t) => String(t.id) === String(highlightedTripId));
        if (trip) {
            setActiveInfoWindow({ kind: 'trip', id: trip.id });
        }
    }, [highlightedTripId, tripLocations, setActiveInfoWindow]);

    return (
        <BaseMap points={points} ports={ports} menu={menu}>
            {/* Trip rendering layer (clustered or individual) */}
            {tripDisplayMode === 'cluster' ? (
                <ClusteredTripMarkers trips={tripLocations} onTripClick={(trip) => openTripInfoWindow(trip)} />
            ) : (
                tripLocations.map((trip) => (
                    <TripMarker key={trip.id} trip={trip} onClick={() => openTripInfoWindow(trip)} />
                ))
            )}

            {/* If a trip is active in the UI store, show the TripDetailsInfoWindow.
                Keep a single InfoWindow open at a time — BaseMap / UI store enforces this. */}
            {activeEntity?.type === 'trip' && (
                <TripDetailsInfoWindow
                    trip={activeEntity.data}
                    onClose={() => {
                        // avoid redundant write
                        if (activeInfoWindow) setActiveInfoWindow(null);
                    }}
                />
            )}

            {/* Camera follower helps the map follow selected trips */}
            <MapCameraFollower trips={tripLocations} />
        </BaseMap>
    );
}
