// ClusteredTripMarkers.tsx
import { useCallback, useEffect, useRef, useState } from 'react';
import { useMap } from '@vis.gl/react-google-maps';
import type { Marker as ClusterMarker } from '@googlemaps/markerclusterer';
import { MarkerClusterer } from '@googlemaps/markerclusterer';

import { logger } from '@/infrastructure/logging';

import { TripMarker } from './TripMarker';
import type { MapTrip } from './BaseMap';

/**
 * Props for ClusteredTripMarkers component.
 */
export type ClusteredTripMarkersProps = {
    trips: MapTrip[];
    onTripClick?: (trip: MapTrip) => void; // ✅ new prop
};

/**
 * Supports ClusterMarker, standard Marker, or AdvancedMarkerElement.
 */
type AdvancedOrLegacyMarker = ClusterMarker | google.maps.Marker | google.maps.marker.AdvancedMarkerElement;

/**
 * Safely removes a marker from the map, handling different marker types.
 */
function removeMarkerSafely(marker: AdvancedOrLegacyMarker | null): void {
    if (!marker) return;

    try {
        if (typeof (marker as google.maps.Marker).setMap === 'function') {
            (marker as google.maps.Marker).setMap(null);
            return;
        }
        if ('map' in marker) {
            // @ts-ignore
            marker.map = null;
        }
    } catch (err: unknown) {
        const errMsg = err instanceof Error ? err.message : String(err);
        logger.warn('[ClusteredTripMarkers] removeMarkerSafely failed', { message: errMsg });
    }
}

/**
 * ClusteredTripMarkers
 * Renders TripMarker components, manages a MarkerClusterer,
 * and ensures safe cleanup on marker changes or unmount.
 */
export const ClusteredTripMarkers = ({ trips, onTripClick }: ClusteredTripMarkersProps) => {
    const map = useMap();
    const clustererRef = useRef<MarkerClusterer | null>(null);

    const [markers, setMarkers] = useState<Record<string, AdvancedOrLegacyMarker | null>>({});
    const markersRef = useRef(markers);

    // Keep markersRef up-to-date for cleanup
    useEffect(() => {
        markersRef.current = markers;
    }, [markers]);

    useEffect(() => {
        if (!map) return;

        clustererRef.current = new MarkerClusterer({ map });

        return () => {
            const cluster = clustererRef.current;
            if (cluster) {
                try {
                    cluster.clearMarkers?.();
                    cluster.setMap?.(null);
                } catch (err: unknown) {
                    const errMsg = err instanceof Error ? err.message : String(err);
                    logger.warn('[ClusteredTripMarkers] clusterer teardown failed', { message: errMsg });
                } finally {
                    clustererRef.current = null;
                }
            }
        };
    }, [map]);

    // Sync markers into clusterer
    useEffect(() => {
        const cluster = clustererRef.current;
        if (!cluster) return;

        try {
            cluster.clearMarkers();
            const list = Object.values(markers).filter((m): m is ClusterMarker => !!m);
            if (list.length > 0) cluster.addMarkers(list);
        } catch (err: unknown) {
            const errorObj = err instanceof Error ? err : new Error(String(err));
            logger.error('[ClusteredTripMarkers] syncing markers to clusterer failed', errorObj);
        }
    }, [markers]);

    // Callback for TripMarker to report its marker instance
    const setMarkerRef = useCallback((marker: AdvancedOrLegacyMarker | null, key: string) => {
        setMarkers((prev) => {
            const existed = key in prev;
            if (marker && existed) return prev;
            if (!marker && !existed) return prev;

            if (marker) return { ...prev, [key]: marker };

            const { [key]: removed, ...rest } = prev;
            if (removed) removeMarkerSafely(removed);
            return rest;
        });
    }, []);

    // Cleanup all markers on unmount
    useEffect(() => {
        return () => {
            try {
                clustererRef.current?.clearMarkers();
                Object.values(markersRef.current).forEach(removeMarkerSafely);
            } catch (err: unknown) {
                const errMsg = err instanceof Error ? err.message : String(err);
                logger.warn('[ClusteredTripMarkers] unmount cleanup failed', { message: errMsg });
            }
        };
    }, []);

    const handleMarkerClick = useCallback(
        (trip: MapTrip) => {
            if (onTripClick) {
                onTripClick(trip); // ✅ notify parent
            }
        },
        [onTripClick],
    );

    return (
        <>
            {trips.map((trip) => (
                <TripMarker key={trip.id} trip={trip} onClick={handleMarkerClick} setMarkerRef={setMarkerRef} />
            ))}
        </>
    );
};
