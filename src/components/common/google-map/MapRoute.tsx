import { useEffect, useRef, useCallback, memo } from 'react';
import { useMap } from '@vis.gl/react-google-maps';

interface MapRouteProps {
    startPoint: { lat: number; lng: number };
    endPoint: { lat: number; lng: number };
    travelMode?: google.maps.TravelMode;
    strokeColor?: string;
    strokeWeight?: number;
    strokeOpacity?: number;
    zIndex?: number;
    preserveViewport?: boolean;
    pathIcon?: string | google.maps.SymbolPath | null;
    pathIconColor?: string | null;
    pathIconOpacity?: number;

    onRouteLoaded?: (route: google.maps.DirectionsResult) => void;
    onRouteError?: (error: google.maps.DirectionsStatus) => void;
}

// Cache for storing calculated routes to prevent duplicate API calls - @0x03r00
const routeCache = new Map<string, google.maps.DirectionsResult>();
const MAX_CACHE_SIZE = 50;

function cleanupCache(): void {
    if (routeCache.size > MAX_CACHE_SIZE) {
        const entries = Array.from(routeCache.keys());
        const toRemove = entries.slice(0, Math.floor(entries.length / 2));
        toRemove.forEach((key) => routeCache.delete(key));
    }
}

function generateRouteCacheKey(
    startPoint: { lat: number; lng: number },
    endPoint: { lat: number; lng: number },
    travelMode: google.maps.TravelMode,
): string {
    const round = (num: number) => num.toFixed(6);
    return `${round(startPoint.lat)},${round(startPoint.lng)}-${round(endPoint.lat)},${round(endPoint.lng)}-${travelMode}`;
}

export const MapRoute = memo(function MapRoute({
    startPoint,
    endPoint,
    travelMode = google.maps.TravelMode.DRIVING,
    strokeColor = '#4a9eff',
    strokeWeight = 4,
    strokeOpacity = 0.8,
    zIndex = 1001,
    preserveViewport = false,
    onRouteLoaded,
    onRouteError,
    pathIcon,
    pathIconColor = null,
    pathIconOpacity = 1,
}: MapRouteProps) {
    const map = useMap();
    const directionsServiceRef = useRef<google.maps.DirectionsService | null>(null);
    const directionsRendererRef = useRef<google.maps.DirectionsRenderer | null>(null);

    const handleRouteLoaded = useCallback(
        (result: google.maps.DirectionsResult) => {
            onRouteLoaded?.(result);
        },
        [onRouteLoaded],
    );

    const handleRouteError = useCallback(
        (status: google.maps.DirectionsStatus) => {
            onRouteError?.(status);
        },
        [onRouteError],
    );

    useEffect(() => {
        if (!map) return;

        if (!directionsServiceRef.current) {
            directionsServiceRef.current = new google.maps.DirectionsService();
        }
        const icons: google.maps.IconSequence[] = pathIcon
            ? [
                  {
                      icon: {
                          path: pathIcon,
                          scale: 4,
                          strokeColor: pathIconColor ?? strokeColor,
                          fillColor: pathIconColor ?? strokeColor,
                          fillOpacity: pathIconOpacity,
                      },
                      offset: '50%',
                      repeat: '100px',
                  },
              ]
            : [];

        if (!directionsRendererRef.current) {
            directionsRendererRef.current = new google.maps.DirectionsRenderer({
                map,
                suppressMarkers: true,
                preserveViewport,
                polylineOptions: { strokeColor, strokeWeight, strokeOpacity, zIndex, icons },
            });
        } else {
            directionsRendererRef.current.setOptions({
                preserveViewport,
                polylineOptions: { strokeColor, strokeWeight, strokeOpacity, zIndex, icons },
            });
        }

        // Generate cache key - @0x03r00
        const routeKey = generateRouteCacheKey(startPoint, endPoint, travelMode);

        // Try to load from cache
        const cachedResult = routeCache.get(routeKey);
        if (cachedResult) {
            directionsRendererRef.current.setDirections(cachedResult);
            handleRouteLoaded(cachedResult);
            return;
        }

        // Calculate new route - @0x03r00
        const request: google.maps.DirectionsRequest = {
            origin: startPoint,
            destination: endPoint,
            travelMode,
        };

        directionsServiceRef.current.route(request, (result, status) => {
            if (status === google.maps.DirectionsStatus.OK && result) {
                routeCache.set(routeKey, result);
                cleanupCache();

                directionsRendererRef.current?.setDirections(result);
                handleRouteLoaded(result);
            } else {
                handleRouteError(status);
            }
        });

        // Cleanup renderer on unmount - @0x03r00
        return () => {
            if (directionsRendererRef.current) {
                directionsRendererRef.current.setMap(null);
                directionsRendererRef.current = null;
            }
        };
    }, [
        map,
        startPoint,
        endPoint,
        travelMode,
        strokeColor,
        strokeWeight,
        strokeOpacity,
        zIndex,
        preserveViewport,
        handleRouteLoaded,
        handleRouteError,
        pathIcon,
        pathIconColor,
        pathIconOpacity,
    ]);

    return null;
});
