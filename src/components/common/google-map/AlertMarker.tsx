import { useCallback } from 'react';
import { AdvancedMarker } from '@vis.gl/react-google-maps';
import type { Marker as ClustererMarker } from '@googlemaps/markerclusterer';

import { useTripAlertsStore } from '@/stores/trip-alerts.store';
import { getAlertMarkerImageByAlertTypeId } from '@/shared/utils/alerts.utils';

import type { MapAlert } from './BaseMap';

export type AlertMarkerProps = {
    alert: MapAlert;
    position: { lat: number; lng: number };
    onClick: (trip: MapAlert) => void;
    setMarkerRef?: (marker: ClustererMarker | null, key: string) => void;
};

export const AlertMarker = ({ alert, onClick, position: position, setMarkerRef }: AlertMarkerProps) => {
    // highlightedTripAlertId is string|null in store
    const highlightedTripAlertId = useTripAlertsStore((s) => s.highlightedTripAlertId);

    const handleClick = useCallback(() => onClick(alert), [onClick, alert]);

    // ref callback: AdvancedMarkerElement won't match Clusterer Marker type,
    // so cast to any when forwarding to setMarkerRef (markerclusterer expects its own Marker type).
    const ref = useCallback(
        (marker: google.maps.marker.AdvancedMarkerElement | null) => {
            try {
                // forward marker reference if caller provided setMarkerRef
                setMarkerRef?.(marker as unknown as ClustererMarker | null, alert.id.toString());
            } catch {
                // ignore forwarding failures; setMarkerRef is optional
            }
        },
        [setMarkerRef, alert.id],
    );

    const isHighlighted = highlightedTripAlertId === alert.id.toString();
    const alertMarker = getAlertMarkerImageByAlertTypeId(alert.alertType.id);

    return (
        <AdvancedMarker
            position={{ lat: position.lat, lng: position.lng }}
            ref={ref as unknown as React.Ref<google.maps.marker.AdvancedMarkerElement>} // cast to any to satisfy the wrapper typing
            onClick={handleClick}
            // zIndex is supported by AdvancedMarkerElement options; pass it through
            zIndex={isHighlighted ? 999 : 1}>
            {/* children are regular DOM — style with Tailwind */}
            <div className="relative flex items-center justify-center pointer-events-none">
                {/* pulse/glow layer (only when highlighted) */}
                {isHighlighted && (
                    <span
                        aria-hidden
                        className="absolute inline-block rounded-full bg-sky-500/40 w-14 h-14 animate-pulse"
                    />
                )}

                {/* truck icon/image: animate & scale when highlighted */}
                <img
                    src={alertMarker}
                    alt={`Trip Alert ${alert.id}`}
                    className={[
                        'relative pointer-events-auto', // allow clicks on the image itself
                        'transition-transform duration-300 ease-out',
                        isHighlighted ? 'w-12 h-12 transform scale-110 animate-bounce' : 'w-4 h-4',
                    ].join(' ')}
                    // inline style fallback size for stricter layout (optional)
                    style={{ willChange: 'transform', width: '50px', height: '50px' }}
                />
            </div>
        </AdvancedMarker>
    );
};
