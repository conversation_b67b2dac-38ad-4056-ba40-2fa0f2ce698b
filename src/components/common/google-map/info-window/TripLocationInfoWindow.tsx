import { useTranslation } from 'react-i18next';

import { useLocalized } from '@/shared/hooks/use-localized.hook';
import { formatLocalizedDate, minutesToHoursFormatted } from '@/shared/utils/format-date.utils';
import { formateBearing, metersToKilometersFormatted } from '@/shared/utils/distance.utils';
import { ChargerStatus } from '@/shared/enums/charger-status.enum';
import InfoRow from '@/components/common/ui/InfoRow';
import { Button } from '@/components/common/ui/Button';
import type { TripPing } from '@/stores/trip-ping.store';

import { BaseInfoWindow } from './BaseInfoWindow';
type TripLocationInfoWindowProps = {
    tripPing: TripPing;
    position?: { lat: number; lng: number } | null;

    onClose: () => void;
};

export function TripLocationInfoWindow({ tripPing, position = null, onClose }: TripLocationInfoWindowProps) {
    const { localized, dir } = useLocalized();
    const { t } = useTranslation();

    position = position ?? { lat: tripPing.location.latitude, lng: tripPing.location.longitude };

    const headerColor = '#008236'; // brown

    const subtitle = formatLocalizedDate(tripPing.trackerDateTime);

    return (
        <BaseInfoWindow
            position={position}
            iconName="locationPin"
            title={t('pings.title')}
            subtitle={subtitle}
            onClose={onClose}
            headerColor={headerColor}
            textColor="white">
            {/* General Information Section */}
            <div className="space-y-2 border-b pb-4" dir={dir}>
                {/* Section Header */}
                <h3 className="text-sm font-semibold text-gray-800 pb-1">{t('tripDetails.tripMap.generalInfo')}</h3>

                <div className="space-y-1.5">
                    <InfoRow
                        label={t('common.address') + ' : '}
                        value={tripPing.address ? localized(tripPing.address) : 'N/A'}
                    />
                    <InfoRow
                        label={t('common.location') + ' : '}
                        value={`(${tripPing.location.latitude}, ${tripPing.location.longitude})`}
                    />
                </div>
            </div>
            <div className="space-y-2  w-97 border-b py-4" dir={dir}>
                {/* Section Header */}
                <h3 className="text-sm font-semibold text-gray-800 pb-1">{t('common.details')}</h3>
                <InfoRow label={t('common.bearing') + ' : '} value={formateBearing(tripPing.bearing)} />
                <InfoRow label={t('common.speed') + ' : '} value={tripPing.speed + t('common.kmh')} />

                <InfoRow
                    label={t('tripDetails.statusConditions.batteryLevel') + ' : '}
                    value={tripPing.batteryLevelPercentage + '%'}
                />
                <InfoRow
                    label={t('tripDetails.statusConditions.chargerStatus') + ' : '}
                    value={
                        tripPing.chargerStatus === ChargerStatus.CONNECTED
                            ? t('common.connected')
                            : t('common.disconnected')
                    }
                />
                <InfoRow
                    label={t('tripDetails.statusConditions.gpsSignalStrength') + ' : '}
                    value={tripPing.gpsSignalStrength + '%'}
                />
                <InfoRow
                    label={t('tripDetails.statusConditions.gsmSignalStrength') + ' : '}
                    value={tripPing.gsmSignalStrength + '%'}
                />
                <InfoRow
                    label={t('tripDetails.statusConditions.suspiciousZone') + ' : '}
                    value={tripPing.isWithinSuspiciousZone ? t('common.yes') : t('common.no')}
                />
                <InfoRow
                    label={t('tripDetails.statusConditions.routeGeofence') + ' : '}
                    value={tripPing.isWithinRouteGeofence ? t('common.yes') : t('common.no')}
                />
                <InfoRow
                    label={t('tripDetails.statusConditions.timeElapsedSinceTripStart') + ' : '}
                    value={minutesToHoursFormatted(tripPing.timeElapsedSinceTripStartInMinutes)}
                />
                <InfoRow
                    label={t('common.remainingDistance') + ' : '}
                    value={metersToKilometersFormatted(tripPing.remainingDistanceInMeters)}
                    // value={tripPing.remainingDistanceInMeters + t('common.kmh')}
                />
                <InfoRow
                    label={t('common.completeDistance') + ' : '}
                    value={metersToKilometersFormatted(tripPing.completeDistanceInMeters)}
                />
            </div>

            {/* Footer (sticky) */}
            <div className=" pt-2 mt-2 bg-white sticky bottom-0 pb-2">
                <div className="flex justify-between items-center">
                    <Button variant={'outline'} onClick={onClose} className="flex text-[#7C7C7C] border-[#7C7C7C]">
                        {t('common.close')}
                    </Button>
                </div>
            </div>
        </BaseInfoWindow>
    );
}

export default TripLocationInfoWindow;
