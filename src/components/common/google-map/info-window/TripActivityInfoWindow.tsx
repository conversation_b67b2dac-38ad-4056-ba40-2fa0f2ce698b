import { useTranslation } from 'react-i18next';

import { useLocalized } from '@/shared/hooks/use-localized.hook';
import { formatLocalizedDate } from '@/shared/utils/format-date.utils';
import type { TripActivity } from '@/infrastructure/api/trip-activities/types';
import InfoRow from '@/components/common/ui/InfoRow';
import { Button } from '@/components/common/ui/Button';

import { BaseInfoWindow } from './BaseInfoWindow';

type TripActivityInfoWindowProps = {
    tripActivity: TripActivity;

    onClose: () => void;
};

export function TripActivityInfoWindow({ tripActivity, onClose }: TripActivityInfoWindowProps) {
    const { localized, dir } = useLocalized();
    const { t } = useTranslation();

    const position = { lat: tripActivity.location.latitude ?? 0, lng: tripActivity.location.longitude ?? 0 };

    const headerColor = '#179fca'; // Blue

    const subtitle = formatLocalizedDate(tripActivity.createdAt);

    return (
        <BaseInfoWindow
            position={position}
            iconName="activity"
            title={t('activityLogs.tripActivity')}
            subtitle={subtitle}
            onClose={onClose}
            headerColor={headerColor}
            textColor="white">
            {/* General Information Section */}
            <div className="space-y-2 border-b pb-4" dir={dir}>
                {/* Section Header */}
                <h3 className="text-sm font-semibold text-gray-800 pb-1">{t('tripDetails.tripMap.generalInfo')}</h3>

                <div className="space-y-1.5">
                    <InfoRow
                        label={t('common.address') + ' : '}
                        value={tripActivity.address ? localized(tripActivity.address) : 'N/A'}
                    />
                    <InfoRow
                        label={t('common.location') + ' : '}
                        value={`${tripActivity.location.latitude}, ${tripActivity.location.longitude}`}
                    />
                </div>
            </div>
            <div className="space-y-2  w-97 border-b py-4" dir={dir}>
                {/* Section Header */}
                <h3 className="text-sm font-semibold text-gray-800 pb-1">{t('common.details')}</h3>
                <InfoRow label={t('tripActivities.action') + ' : '} value={localized(tripActivity.action.name)} />
                <InfoRow label={t('tripActivities.state') + ' : '} value={localized(tripActivity.status?.name)} />
                <InfoRow
                    label={t('tripActivities.details') + ' : '}
                    value={tripActivity.details}
                    className="[&_.table-cell:last-child]:!truncate-none [&_.table-cell:last-child]:!whitespace-normal [&_.table-cell:last-child]:!break-words"
                />
                <InfoRow
                    label={t('tripActivities.reportNumber') + ' : '}
                    value={tripActivity.note}
                    className="[&_.table-cell:last-child]:!truncate-none [&_.table-cell:last-child]:!whitespace-normal [&_.table-cell:last-child]:!break-words"
                />
            </div>

            {/* Footer (sticky) */}
            <div className=" pt-2 mt-2 bg-white sticky bottom-0 pb-2">
                <div className="flex justify-between items-center">
                    <Button variant={'outline'} onClick={onClose} className="flex text-[#7C7C7C] border-[#7C7C7C]">
                        {t('common.close')}
                    </Button>
                </div>
            </div>
        </BaseInfoWindow>
    );
}

export default TripActivityInfoWindow;
