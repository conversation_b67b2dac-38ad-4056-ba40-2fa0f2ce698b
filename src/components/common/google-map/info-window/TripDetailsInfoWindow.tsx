import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Info } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

import { useTripDetailStore } from '@/stores/trip-detail.store';
import { constructTripStates } from '@/shared/utils/trips.utils';
import { useLocalized } from '@/shared/hooks/use-localized.hook';
import { formatLocalizedDate } from '@/shared/utils/format-date.utils';
import { TripStatesSection } from '@/components/features/trips/trip-details/TripStatesSection';
import { TripAlertsSection } from '@/components/features/trips/trip-details/TripAlertsSection';
import { TripFromToSection } from '@/components/features/trips/trip-details/TripFromToSection';

import { Button } from '../../ui/Button';
import InfoRow from '../../ui/InfoRow';
import type { MapTrip } from '../BaseMap';

import { BaseInfoWindow } from './BaseInfoWindow';

type TripDetailsInfoWindowProps = {
    trip: MapTrip | null;
    onClose: () => void;
};

export function TripDetailsInfoWindow({ trip, onClose }: TripDetailsInfoWindowProps) {
    const { t } = useTranslation();
    const { tripDetail, isLoading, loadTripDetail } = useTripDetailStore();
    const TripStates = tripDetail ? constructTripStates(tripDetail.currentState) : [];
    const localized = useLocalized();
    const navigate = useNavigate();
    useEffect(() => {
        if (trip) loadTripDetail({ id: trip.id });
    }, [trip, loadTripDetail]);

    if (!trip) return null;

    const position = { lat: trip.location.latitude, lng: trip.location.longitude };
    const title = `${tripDetail?.transitNumber ?? 'N/A'}/\u200E#${trip.id}`;
    const subtitle = formatLocalizedDate(tripDetail?.startDate);

    // ================== (2) ALERTS SECTION ==================
    const alertsSection = !isLoading && tripDetail && tripDetail.activeAlerts && tripDetail.activeAlerts.length > 0 && (
        <TripAlertsSection alerts={tripDetail.activeAlerts} showCarousel={true} />
    );

    // ================== (3) FROM–TO SECTION ==================
    const fromToSection = tripDetail && (
        <TripFromToSection
            fromLabel={tripDetail.entryPort?.name ? localized(tripDetail.entryPort.name) : 'N/A'}
            toLabel={tripDetail.exitPort?.name ? localized(tripDetail.exitPort.name) : 'N/A'}
            startDate={tripDetail.startDate}
            endDate={tripDetail.endDate}
        />
    );

    // ================== (4) DETAILS ==================
    const detailsSection = !isLoading && tripDetail && (
        <div className="space-y-3 text-xs border-t pt-3">
            {/* Section: Shipment */}
            <div>
                <h3 className="font-semibold text-gray-800 mb-2">{t('tripDetails.aboutShipment')}</h3>
                <div className="space-y-1">
                    <InfoRow
                        label={t('tripDetails.trackingSerialNumber')}
                        value={tripDetail.tracker.serialNumber ?? 'N/A'}
                        isLink={true}
                        onClick={() => {
                            const trackerNumber = tripDetail.tracker.serialNumber;
                            if (trackerNumber) {
                                const params = new URLSearchParams();
                                params.set('trackerNumber', trackerNumber);
                                navigate(`/trips?${params.toString()}`);
                            }
                        }}
                    />
                    <InfoRow
                        label={t('tripDetails.elockSerialNumber')}
                        value={tripDetail.eLocks?.map((eLock) => eLock.serialNumber).join(', ') ?? 'N/A'}
                    />
                    <InfoRow
                        label={t('tripDetails.shipmentDescription')}
                        value={tripDetail.shipmentDescription ?? 'N/A'}
                    />
                </div>
            </div>

            {/* Section: Vehicle */}
            <div className="border-t pt-3">
                <h3 className="font-semibold text-gray-800 mb-2">{t('tripDetails.aboutVehicle')}</h3>
                <div className="flex gap-2 text-sm justify-between">
                    <InfoRow
                        label={t('tripDetails.aboutVehicle')}
                        value={[tripDetail.vehicle?.plateNo ?? 'N/A', tripDetail.vehicle?.id ?? 'N/A'].join(' , ')}
                        isLink={true}
                    />
                </div>
            </div>

            {/* Section: Driver */}
            <div className="border-t pt-3">
                <h3 className="font-semibold text-gray-800 mb-2">{t('tripDetails.aboutDriver')}</h3>
                <div className="space-y-1">
                    <InfoRow
                        label={t('tripDetails.driverName')}
                        value={tripDetail.driver?.name ?? 'N/A'}
                        isLink={true}
                        onClick={() => {
                            const driverName = tripDetail.driver?.name;
                            if (driverName) {
                                const params = new URLSearchParams();
                                params.set('driverName', driverName);
                                navigate(`/trips?${params.toString()}`);
                            }
                        }}
                    />
                    <InfoRow
                        label={t('tripDetails.driverNationality')}
                        value={tripDetail.driver?.passportCountry ?? ''}
                        isLink={true}
                        onClick={() => {
                            const driverNationality = tripDetail.driver?.passportCountry;
                            if (driverNationality) {
                                const params = new URLSearchParams();
                                params.set('driverNationality', driverNationality);
                                navigate(`/trips?${params.toString()}`);
                            }
                        }}
                    />
                </div>
            </div>
        </div>
    );

    // ================== (5) STATES ==================
    const statesSection = !isLoading && tripDetail && <TripStatesSection states={TripStates} />;

    // ================== (6) ALERTS ==================
    const alertsTagSection = !isLoading && tripDetail && <TripAlertsSection alerts={tripDetail.activeAlerts} />;

    return (
        <BaseInfoWindow
            position={position}
            iconName="truck-2"
            title={title}
            subtitle={subtitle}
            onClose={onClose}
            headerColor="bg-green-700">
            <div className="flex flex-col w-112 max-h-[25rem] min-h-[24rem]">
                {/* Scrollable content */}
                <div className="flex-1 overflow-y-auto space-y-3 pr-1">
                    {alertsSection}
                    {fromToSection}
                    {detailsSection}
                    {statesSection}
                    {alertsTagSection}
                </div>

                {/* Footer (sticky) */}
                <div className="flex-none bg-white border-t mt-2 pt-2">
                    <div className="flex justify-end items-center">
                        <Button
                            className="flex items-center gap-2 px-3 py-1 border border-green-700 text-green-700 bg-white rounded text-sm shadow-sm"
                            onClick={() => navigate(`/trips/${trip.id}/details`)}>
                            <Info size={12} className="text-green-700" />
                            {t('tripDetails.tripDetails')}
                        </Button>
                    </div>
                </div>
            </div>
        </BaseInfoWindow>
    );
}
