import { useTranslation } from 'react-i18next';

import { Icon } from '@/components/common/ui/Icon';
import { useLocalized } from '@/shared/hooks/use-localized.hook';
import type { TripAlert } from '@/infrastructure/api/trip-alerts/types';
import { formatDurationFromString, formatLocalizedDate } from '@/shared/utils/format-date.utils';
import { constructTripStates } from '@/shared/utils/trips.utils';
import Tooltip from '@/components/common/ui/Tooltip';
import { TooltipTrigger } from '@/components/common/ui/LibTooltip';
import { Tag } from '@/components/common/ui/Tag';
import { useTripDetailStore } from '@/stores/trip-detail.store';
import { useTripAlertsStore } from '@/stores/trip-alerts.store';
import { Scope } from '@/shared/enums';
import { AlertTypeMapByName, alertIconMapping } from '@/shared/utils/alerts.utils';
import { logger } from '@/infrastructure/logging';

import InfoRow from '../../ui/InfoRow';
import { Button } from '../../ui/Button';
import { showError, showSuccess } from '../../ui/Toast';
import TripEmailDialog from '../../ui/TripEmailDialog';

import { BaseInfoWindow } from './BaseInfoWindow';

/**
 * Props interface for the TripAlertInfoWindow component
 */
type TripAlertInfoWindowProps = {
    /** Trip Detail data to be displayed in the info window  */
    /** Trip Alert data to be displayed in the info window  */
    tripAlert: TripAlert;
    /** Callback function triggered when the info window is closed */
    onClose: () => void;
    onRemoveMarkerClicked?: () => void;
};

/**
 * TripAlertInfoWindow Component
 *
 * A specialized info window component for displaying trip alert information on a map.
 * Supports multiple alert types with different icons.
 * Features RTL/LTR localization and displays trip alert data in a readable format.
 *
 * @param props - Component properties
 * @returns JSX element representing the checkpoint info window
 */
export function TripAlertInfoWindow({ tripAlert, onClose, onRemoveMarkerClicked }: TripAlertInfoWindowProps) {
    // Localization hooks for multi-language support
    const { localized, dir } = useLocalized();
    const { t } = useTranslation();

    // Extract TripAlertTypeID from tripAlert data
    const tripAlertTypeID = tripAlert.alertType.id ?? 'default';

    // Extract geographic coordinates from tripAlert data
    const position = { lat: tripAlert.fromState.lat ?? 0, lng: tripAlert.fromState.long ?? 0 };

    // Determine icon based on tripAlertTypeID
    const iconName = alertIconMapping[tripAlertTypeID] ?? 'alert';

    const headerColor = '#DC2626'; // Red

    // Formate Subtitle from tripAlert data
    const subtitle =
        tripAlert.toState && tripAlert.toState.trackerDateTime
            ? dir === 'rtl'
                ? `${formatLocalizedDate(tripAlert.fromState.trackerDateTime)} ← ${formatLocalizedDate(
                      tripAlert.toState.trackerDateTime,
                  )}`
                : `${formatLocalizedDate(tripAlert.fromState.trackerDateTime)} → ${formatLocalizedDate(
                      tripAlert.toState.trackerDateTime,
                  )}`
            : formatLocalizedDate(tripAlert.fromState.trackerDateTime);

    // Construct trip states from tripAlert data
    const fromStates = tripAlert.fromState ? constructTripStates(tripAlert.fromState) : [];
    const toStates = tripAlert.toState ? constructTripStates(tripAlert.toState) : [];
    const trip = useTripDetailStore((state) => state.tripDetail); // TODO:: this is not the best way to get the trip, we should pass the trip as a prop

    const { markAsFocused } = useTripDetailStore();
    const deselectTripAlertById = useTripAlertsStore((s) => s.deselectTripAlertById);

    const handleRemoveMarker = () => {
        deselectTripAlertById(tripAlert.id);
        onClose();
        onRemoveMarkerClicked?.();
    };

    return (
        <BaseInfoWindow
            position={position}
            iconName={iconName}
            title={localized(tripAlert.alertType.name)}
            subtitle={subtitle}
            onClose={onClose}
            headerColor={headerColor}
            textColor="white">
            {/* General Information Section */}
            <div className="space-y-2 border-b pb-4" dir={dir}>
                {/* Section Header */}
                <h3 className="text-sm font-semibold text-gray-800 pb-1">{t('tripDetails.tripMap.generalInfo')}</h3>

                <div className="space-y-1.5">
                    <InfoRow
                        label={t('common.from') + ' : '}
                        value={trip && trip.entryPort && localized(trip.entryPort.name)}
                    />
                    <InfoRow
                        label={t('common.to') + ' : '}
                        value={trip && trip.exitPort && localized(trip.exitPort.name)}
                    />

                    {tripAlertTypeID == AlertTypeMapByName['truck_stopped'] ? (
                        <InfoRow
                            label={t('alert.stopDuration') + ' : '}
                            value={formatDurationFromString(
                                tripAlert.fromState?.trackerDateTime,
                                tripAlert.toState?.trackerDateTime,
                            )}
                        />
                    ) : null}
                </div>
            </div>
            <div className="space-y-2  w-97 border-b py-4" dir={dir}>
                {/* Section Header */}
                <h3 className="text-sm font-semibold text-gray-800 pb-1">
                    {t('tripDetails.tripMap.truckStateAtStartingPoint')}
                </h3>

                <div className="flex flex-wrap gap-2">
                    {fromStates.map(({ id, value, bgColor, textColor, icon, tooltipKey }) => (
                        <Tooltip key={id} tooltipMessage={t(tooltipKey)}>
                            <TooltipTrigger asChild>
                                <Tag
                                    bgColor={bgColor}
                                    textColor={textColor}
                                    icon={icon}
                                    className="inline-flex max-w-max cursor-pointer">
                                    {value}
                                </Tag>
                            </TooltipTrigger>
                        </Tooltip>
                    ))}
                </div>
            </div>
            <div className="space-y-2  w-97 border-b py-4" dir={dir}>
                {/* Section Header */}
                <h3 className="text-sm font-semibold text-gray-800 pb-1">
                    {t('tripDetails.tripMap.truckStateAtEndingPoint')}
                </h3>

                <div className="flex flex-wrap gap-2">
                    {toStates.length ? (
                        toStates.map(({ id, value, bgColor, textColor, icon, tooltipKey }) => (
                            <Tooltip key={id} tooltipMessage={t(tooltipKey)}>
                                <TooltipTrigger asChild>
                                    <Tag
                                        bgColor={bgColor}
                                        textColor={textColor}
                                        icon={icon}
                                        className="inline-flex max-w-max cursor-pointer">
                                        {value}
                                    </Tag>
                                </TooltipTrigger>
                            </Tooltip>
                        ))
                    ) : (
                        <p className="text-gray-500 text-sm">{t('common.noDataExist')}</p>
                    )}
                </div>
            </div>

            {/* Footer (sticky) */}
            <div className=" pt-2 mt-2 bg-white sticky bottom-0 pb-2">
                <div className="flex justify-between items-center">
                    <div className="flex items-center gap-1">
                        <Tooltip tooltipMessage={t('emailDialog.title')}>
                            <TripEmailDialog scope={Scope.Alert}>
                                <Button variant={'outline'}>
                                    <Icon name="email" className="size-6" />
                                </Button>
                            </TripEmailDialog>
                            {/* <EmailDialog
                                scope={Scope.Alert}
                                emailBody={trip ? <TripEmailTemplate trip={mapTripToTripEmail(trip)} /> : ''}>
                                <Button variant={'outline'}>
                                    <Icon name="email" className="size-6" />
                                </Button>
                            </EmailDialog> */}
                        </Tooltip>
                        {trip && (
                            <Button
                                variant={'outline'}
                                onClick={() => {
                                    try {
                                        markAsFocused(trip.id, !trip?.isFocused);
                                        showSuccess(t('common.success'), t('tripDetails.tripStatusChangeSuccess'));
                                    } catch {
                                        logger.error('[ TripAlertInfoWindow] Error occurred while suspending trip');
                                        showError(t('common.error'), t('tripDetails.tripStatusChangeFailed'));
                                    }
                                }}>
                                {trip?.isFocused ? t('tripDetails.unTracking') : t('tripDetails.tracking')}
                            </Button>
                        )}
                    </div>

                    <div className="flex items-center gap-2">
                        <Button
                            variant={'outline'}
                            onClick={handleRemoveMarker}
                            className="flex text-red-600 border-red-600">
                            {t('common.removeMarker')}
                        </Button>

                        <Button variant={'outline'} onClick={onClose} className="flex text-[#7C7C7C] border-[#7C7C7C]">
                            {t('common.close')}
                        </Button>
                    </div>
                </div>
            </div>
        </BaseInfoWindow>
    );
}

export default TripAlertInfoWindow;
