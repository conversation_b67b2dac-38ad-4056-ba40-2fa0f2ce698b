import { MapRoute } from './MapRoute';

interface AlertRouteProps {
    startPoint: { lat: number; lng: number };
    endPoint: { lat: number; lng: number };
    zIndex?: number;
}

export function AlertRoute({ startPoint, endPoint, zIndex }: AlertRouteProps) {
    return (
        <MapRoute
            startPoint={startPoint}
            endPoint={endPoint}
            zIndex={zIndex}
            pathIcon={google.maps.SymbolPath.FORWARD_OPEN_ARROW}
            strokeWeight={4}
            strokeOpacity={0.4}
            pathIconColor="#ff0000"
        />
    );
}
