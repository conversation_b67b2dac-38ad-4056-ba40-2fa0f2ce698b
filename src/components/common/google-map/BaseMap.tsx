/**
 * BaseMap.tsx
 *
 * Provides a reusable Google Maps wrapper component with support for:
 * - Rendering map points (checkpoints) and ports as markers.
 * - Displaying interactive InfoWindows for points and ports.
 * - Integrating a ruler tool for measuring distances.
 * - Supporting additional custom UI controls via `menu` and `children`.
 *
 * This component serves as the foundation for building specific maps,
 * such as MonitorMap (overview with clustered trips) or TripDetailsMap
 * (detailed trip paths with start/end markers).
 */

import { useRef, useState } from 'react';
import { APIProvider, ControlPosition, Map, MapControl, Marker } from '@vis.gl/react-google-maps';
import React from 'react';

import { useUIStore } from '@/stores/ui.store';
import { appConfig } from '@/shared/config/app-settings.config';
import { useLocalized } from '@/shared/hooks/use-localized.hook';
import type { MapPointDataItem } from '@/infrastructure/api/map-points/types';
import type { PortItem } from '@/infrastructure/api/ports/types';
import type { TripLocationItem } from '@/infrastructure/api/trips/types';
import type { TripAlert } from '@/infrastructure/api/trip-alerts/types';

import { RulerTool } from './RulerTool';
import CheckPointInfoWindow from './info-window/CheckPointInfoWindow';
import { PortInfoWindow } from './info-window/PortInfoWindow';
import { TrafficToggleButton } from './TrafficToggleButton';

/**
 * Extended map point types with icon property for display
 */
export type MapPoint = MapPointDataItem & { icon: string };
export type MapPort = PortItem & { icon: string };
export type MapTrip = TripLocationItem & { icon: string };
export type MapAlert = TripAlert & { icon: string };

/**
 * ActiveEntity
 *
 * Represents the entity currently selected/active on the map
 * for displaying an InfoWindow.
 */
export type ActiveEntity =
    | { type: 'trip'; data: MapTrip }
    | { type: 'point'; data: MapPoint }
    | { type: 'port'; data: MapPort }
    | { type: 'alert'; data: MapAlert }
    | null;

/**
 * Props for the BaseMap component.
 *
 * @property center - Optional initial map center coordinates.
 * @property zoom - Optional initial zoom level.
 * @property height - CSS height of the map container (default 100%).
 * @property width - CSS width of the map container (default 100%).
 * @property points - Array of points (checkpoints) to render as markers.
 * @property ports - Array of ports to render as markers.
 * @property menu - Optional React node to render as a control in the top-right corner.
 * @property children - Optional React node(s) rendered inside the map
 *                      (useful for overlays, trip markers, or extra controls).
 */
export interface BaseMapProps {
    center?: { lat: number; lng: number };
    zoom?: number;
    height?: string;
    width?: string;
    points?: MapPoint[];
    ports?: MapPort[];
    menu?: React.ReactNode;
    children?: React.ReactNode;
}

/**
 * BaseMap
 *
 * A generic map component built on Google Maps via @vis.gl/react-google-maps.
 * It provides:
 * - Points and Ports markers with InfoWindows.
 * - Unified state for managing InfoWindows via UI store.
 * - Ruler tool for distance measurement.
 * - Support for external custom controls (`menu`, `children`).
 *
 * This component does not render trip markers/paths by itself,
 * but can be extended or composed with additional layers.
 */
export function BaseMap({
    height = '100%',
    width = '100%',
    center = appConfig.get('googleDefaultMapCenter'),
    zoom = appConfig.get('googleDefaultMapZoom'),
    points = [],
    ports = [],
    menu,
    children,
}: BaseMapProps) {
    const [googleLoaded, setGoogleLoaded] = useState(false);
    const localized = useLocalized();

    // UI store: manages active info windows & tabs
    const activeInfoWindow = useUIStore((s) => s.activeInfoWindow);
    const setActiveInfoWindow = useUIStore((s) => s.setActiveInfoWindow);
    const activeTab = useUIStore((s) => s.activeTab);
    const setActiveTab = useUIStore((s) => s.setActiveTab);

    // Handlers for opening InfoWindows
    const openPointInfoWindow = (point: MapPoint) => {
        if (activeInfoWindow && activeInfoWindow.kind === 'point' && activeInfoWindow.id === point.id) return;
        setActiveInfoWindow({ kind: 'point', id: point.id });
    };
    const openPortInfoWindow = (port: MapPort) => {
        if (activeInfoWindow && activeInfoWindow.kind === 'port' && activeInfoWindow.id === port.id) return;
        setActiveInfoWindow({ kind: 'port', id: port.id });
    };

    // Resolve the active entity from store state
    const activeEntity: ActiveEntity | null = (() => {
        if (!activeInfoWindow) return null;
        const { kind, id } = activeInfoWindow;
        if (kind === 'point') {
            const found = points.find((p) => String(p.id) === String(id));
            return found ? { type: 'point', data: found } : null;
        }
        if (kind === 'port') {
            const found = ports.find((p) => String(p.id) === String(id));
            return found ? { type: 'port', data: found } : null;
        }
        return null;
    })();

    // Ref for attaching dialogs/menus to map controls
    const dialogRootRef = useRef<HTMLDivElement | null>(null);

    return (
        <div style={{ height, width }}>
            <APIProvider apiKey={appConfig.get('googleMapsApiKey')} libraries={['geometry', 'marker']} language="ar">
                <Map
                    mapId={appConfig.get('googleMapId')}
                    defaultCenter={center}
                    defaultZoom={zoom}
                    minZoom={3}
                    style={{ width: '100%', height: '100%' }}
                    onTilesLoaded={() => setGoogleLoaded(true)}
                    onClick={() => {
                        // Close active popups when clicking the map background
                        if (activeInfoWindow !== null) setActiveInfoWindow(null);
                        if (activeTab !== null && activeTab !== 'Alerts') setActiveTab(null);
                    }}>
                    {googleLoaded && (
                        <>
                            {/* Render checkpoints */}
                            {points.map((point) => (
                                <Marker
                                    key={`point-${point.id}`}
                                    position={{ lat: point.lat, lng: point.long }}
                                    title={localized(point.name)}
                                    icon={{ url: point.icon, scaledSize: new google.maps.Size(32, 32) }}
                                    onClick={() => openPointInfoWindow(point)}
                                />
                            ))}

                            {/* Render ports */}
                            {ports.map((port) => (
                                <Marker
                                    key={`port-${port.id}`}
                                    position={{ lat: port.lat, lng: port.long }}
                                    title={localized(port.name)}
                                    icon={{ url: port.icon, scaledSize: new google.maps.Size(32, 32) }}
                                    onClick={() => openPortInfoWindow(port)}
                                />
                            ))}

                            {/* InfoWindows for active entity */}
                            {activeEntity?.type === 'point' && (
                                <CheckPointInfoWindow
                                    point={activeEntity.data}
                                    onClose={() => activeInfoWindow && setActiveInfoWindow(null)}
                                />
                            )}
                            {activeEntity?.type === 'port' && (
                                <PortInfoWindow
                                    port={activeEntity.data}
                                    onClose={() => activeInfoWindow && setActiveInfoWindow(null)}
                                />
                            )}
                        </>
                    )}

                    {/* Map controls */}
                    <MapControl position={ControlPosition.TOP_LEFT}>
                        <RulerTool className="top-3" />
                    </MapControl>
                    <TrafficToggleButton className="mt-3" position={ControlPosition.TOP_LEFT} />
                    <MapControl position={ControlPosition.TOP_RIGHT}>
                        <div ref={dialogRootRef} className="map-control-root">
                            {menu && React.isValidElement(menu)
                                ? React.cloneElement(menu as React.ReactElement, {
                                      mapDialogRootRef: dialogRootRef,
                                  })
                                : menu}
                        </div>
                    </MapControl>
                    <MapControl position={ControlPosition.CENTER}>{children}</MapControl>
                </Map>
            </APIProvider>
        </div>
    );
}
