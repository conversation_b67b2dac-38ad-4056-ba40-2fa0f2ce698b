import { AdvancedMarker } from '@vis.gl/react-google-maps';
import { useCallback } from 'react';

import type { Location } from '@/shared/types/common';

import { Icon } from '../ui/Icon';

export type LocationMarkerProps = {
    location: Location;
    onClick?: () => void;
    setMarkerRef?: (marker: google.maps.marker.AdvancedMarkerElement | null, key: string) => void;
    zIndex?: number;
    key?: string | number | null;
};

export function LocationMarker({
    location,
    onClick = undefined,
    setMarkerRef,
    key: markerKey = null,
    zIndex = 1,
}: LocationMarkerProps) {
    const key = markerKey || `${location.lat}-${location.lng}`;

    const ref = useCallback(
        (marker: google.maps.marker.AdvancedMarkerElement | null) => {
            try {
                // forward marker reference if caller provided setMarkerRef
                setMarkerRef?.(marker, key.toString());
                // setMarkerRef?.(marker as unknown as ClustererMarker | null, markerKey);
            } catch {
                // ignore forwarding failures; setMarkerRef is optional
            }
        },
        [setMarkerRef, key],
    );

    return (
        <AdvancedMarker
            ref={ref}
            key={key}
            position={{ lat: location.lat, lng: location.lng }}
            onClick={onClick}
            zIndex={zIndex}>
            <Icon name="locationPin" />
        </AdvancedMarker>
    );
}
