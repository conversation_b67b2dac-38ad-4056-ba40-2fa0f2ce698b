import { useCallback } from 'react';
import { AdvancedMarker } from '@vis.gl/react-google-maps';
import type { Marker as ClustererMarker } from '@googlemaps/markerclusterer';

import { useTripLocationStore } from '@/stores/trip-location.store';

import type { MapTrip } from './BaseMap';

export type TripMarkerProps = {
    trip: MapTrip;
    onClick: (trip: MapTrip) => void;
    setMarkerRef?: (marker: ClustererMarker | null, key: string) => void;
    title?: string;
};

export const TripMarker = ({ trip, onClick, setMarkerRef, title }: TripMarkerProps) => {
    // highlightedTripId is string|null in store
    const highlightedTripId = useTripLocationStore((s) => s.highlightedTripId);

    const handleClick = useCallback(() => onClick(trip), [onClick, trip]);

    // ref callback: AdvancedMarkerElement won't match Clusterer Marker type,
    // so cast to any when forwarding to setMarkerRef (markerclusterer expects its own Marker type).
    const ref = useCallback(
        (marker: google.maps.marker.AdvancedMarkerElement | null) => {
            try {
                // forward marker reference if caller provided setMarkerRef
                setMarkerRef?.(marker as unknown as ClustererMarker | null, trip.id.toString());
            } catch {
                // ignore forwarding failures; setMarkerRef is optional
            }
        },
        [setMarkerRef, trip.id],
    );

    const isHighlighted = highlightedTripId === trip.id.toString();

    return (
        <AdvancedMarker
            title={title}
            position={{ lat: trip.location.latitude, lng: trip.location.longitude }}
            ref={ref as unknown as React.Ref<google.maps.marker.AdvancedMarkerElement>} // cast to any to satisfy the wrapper typing
            onClick={handleClick}
            // zIndex is supported by AdvancedMarkerElement options; pass it through
            zIndex={isHighlighted ? 999 : 1}>
            {/* children are regular DOM — style with Tailwind */}
            <div className="relative flex items-center justify-center pointer-events-none">
                {/* pulse/glow layer (only when highlighted) */}
                {isHighlighted && (
                    <span
                        aria-hidden
                        className="absolute inline-block rounded-full bg-sky-500/40 w-14 h-14 animate-pulse"
                    />
                )}

                {/* truck icon/image: animate & scale when highlighted */}
                <img
                    src={trip.icon}
                    alt={`Trip ${trip.id}`}
                    className={[
                        'relative pointer-events-auto', // allow clicks on the image itself
                        'transition-transform duration-300 ease-out',
                        isHighlighted ? 'w-12 h-12 transform scale-110 animate-bounce' : 'w-8 h-8',
                    ].join(' ')}
                    // inline style fallback size for stricter layout (optional)
                    style={{ willChange: 'transform', width: '50px', height: '50px' }}
                />
            </div>
        </AdvancedMarker>
    );
};
