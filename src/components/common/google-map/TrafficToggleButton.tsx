import { useEffect, useRef, useState } from 'react';
import { ControlPosition, MapControl, useMap } from '@vis.gl/react-google-maps';
import { useTranslation } from 'react-i18next';

import { Icon } from '@/components/common/ui/Icon';
import { cn } from '@/shared/utils/class-name.utils';

interface TrafficToggleButtonProps {
    /**
     * Initial state of the traffic layer visibility.
     * @default true
     */
    initialShowTraffic?: boolean;

    /**
     * Custom CSS classes for the button container.
     */
    className?: string;

    /**
     * Custom CSS classes for the button.
     */
    buttonClassName?: string;

    /**
     * Position of the control on the map.
     * @default ControlPosition.TOP_CENTER
     */
    position?: ControlPosition;
}

export function TrafficToggleButton({
    initialShowTraffic = false,
    className,
    buttonClassName,
    position = ControlPosition.TOP_CENTER,
}: TrafficToggleButtonProps) {
    const { t } = useTranslation();
    const [showTraffic, setShowTraffic] = useState(initialShowTraffic);
    const map = useMap(); // access google.maps.Map
    const trafficLayerRef = useRef<google.maps.TrafficLayer | null>(null);

    useEffect(() => {
        if (!map) return;

        // Initialize TrafficLayer only once
        trafficLayerRef.current ??= new google.maps.TrafficLayer();

        // Apply or remove the layer
        if (showTraffic) {
            trafficLayerRef.current.setMap(map);
        } else {
            trafficLayerRef.current.setMap(null);
        }

        // Cleanup on unmount
        return () => {
            trafficLayerRef.current?.setMap(null);
        };
    }, [map, showTraffic]);

    const toggleTraffic = () => {
        setShowTraffic((prev) => !prev);
    };

    return (
        <MapControl position={position}>
            <div className={cn('bg-white/95 backdrop-blur-sm shadow border border-gray-200 rounded-xs', className)}>
                <button
                    className={cn(
                        'flex items-center gap-2 h-9 px-4 text-sm font-medium transition-colors',
                        showTraffic ? 'bg-[#d4d4d4] text-gray-800 font-semibold' : 'text-gray-700 hover:bg-[#ebebeb]',
                        buttonClassName,
                    )}
                    onClick={toggleTraffic}
                    title={showTraffic ? t('map.hideTrafficInformation') : t('map.showTrafficInformation')}>
                    <Icon name="traffic" className="w-4 h-4" />
                    {showTraffic ? t('map.hideTraffic') : t('map.showTraffic')}
                </button>
            </div>
        </MapControl>
    );
}
