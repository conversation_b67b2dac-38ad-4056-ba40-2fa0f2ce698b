import { useEffect, useRef } from 'react';
import { useMap } from '@vis.gl/react-google-maps';

interface PingRouteProps {
    pings: Array<{ lat: number; lng: number }>;
    strokeColor?: string;
    strokeWeight?: number;
    strokeOpacity?: number;
    zIndex?: number;
    showArrows?: boolean;
}

export function PingRoute({
    pings,
    strokeColor = '#4a9eff',
    strokeWeight = 3,
    strokeOpacity = 0.6,
    zIndex = 1000,
    showArrows = true,
}: PingRouteProps) {
    const map = useMap();
    const polylineRef = useRef<google.maps.Polyline | null>(null);

    useEffect(() => {
        if (!map || !pings || pings.length < 2) {
            // Clean up if insufficient pings
            if (polylineRef.current) {
                polylineRef.current.setMap(null);
                polylineRef.current = null;
            }
            return;
        }

        const path = pings.map((p) => ({ lat: p.lat, lng: p.lng }));

        const icons: google.maps.IconSequence[] = showArrows
            ? [
                  {
                      icon: {
                          path: google.maps.SymbolPath.FORWARD_CLOSED_ARROW,
                          scale: 3,
                          strokeColor,
                          fillColor: strokeColor,
                          fillOpacity: 1,
                      },
                      offset: '100%',
                      repeat: '80px',
                  },
              ]
            : [];

        if (!polylineRef.current) {
            polylineRef.current = new google.maps.Polyline({
                path,
                map,
                strokeColor,
                strokeWeight,
                strokeOpacity,
                zIndex,
                icons,
                geodesic: true,
            });
        } else {
            // Update existing polyline
            polylineRef.current.setOptions({
                path,
                strokeColor,
                strokeWeight,
                strokeOpacity,
                zIndex,
                icons,
            });
        }

        return () => {
            if (polylineRef.current) {
                polylineRef.current.setMap(null);
                polylineRef.current = null;
            }
        };
    }, [map, pings, strokeColor, strokeWeight, strokeOpacity, zIndex, showArrows]);

    return null;
}
