/* eslint-disable react-hooks/exhaustive-deps */
import { Marker } from '@vis.gl/react-google-maps';
import { useEffect, useRef, useState } from 'react';

import TripTruckIcon from '@/assets/imgs/svg/location-pin.svg';
import type { Location } from '@/shared/types/common';
import type { AlertWithViewOptions } from '@/stores/trip-alerts.store';
import type { TripRouteReplayDirectionType, TripRouteReplayTypeSpeed } from '@/stores/trip-route-replay.store';

import type { MapAlert } from './BaseMap';
import { AlertMarkerWithInfoWindow } from './marker-with-info-window/AlertMarkerWithInfoWindow';

//--- Each path point contains its location and any alerts at that position
export interface LocationWithAlerts {
    alerts: AlertWithViewOptions[];
    location: Location;
}

//--- Props for the animated marker component
interface LocationMarkerAnimatedProps {
    path: LocationWithAlerts[];
    speed?: TripRouteReplayTypeSpeed;
    markerIconSize?: number;
    direction: TripRouteReplayDirectionType;
    isPlaying: boolean;
    showAlerts?: boolean; //--- toggle showing alerts or not
    handleDoneAnimation: () => void;
}

export function LocationMarkerAnimated({
    path,
    isPlaying,
    speed = 1,
    direction,
    markerIconSize = 40,
    showAlerts = true,
    handleDoneAnimation,
}: LocationMarkerAnimatedProps) {
    const ACTIVE_ALERT_DURATION = 3000; //--- how long to show alert marker
    const [markerPos, setMarkerPos] = useState<Location | null>(null);
    const [activeAlert, setActiveAlert] = useState<AlertWithViewOptions | null>(null);

    //--- Refs to keep track of animation state between renders
    const animationRef = useRef<number | null>(null);
    const alertTimeoutRef = useRef<number | null>(null);
    const indexRef = useRef<number>(0);
    const progressRef = useRef<number>(0);
    const directionRef = useRef<TripRouteReplayDirectionType>(direction);
    const markerPosRef = useRef<Location | null>(null);
    const pointsLength = path.length;

    //--- Map speed value to animation duration
    const speedMapper: Record<TripRouteReplayTypeSpeed, number> = {
        1: 600,
        1.5: 300,
        2: 20,
    };

    //--- Cancel current animation frame
    const cancelAnimationTracker = () => {
        if (animationRef.current) {
            cancelAnimationFrame(animationRef.current);
            animationRef.current = null;
        }
    };

    //--- Clear alert timeout if active
    const clearAlertTimeout = () => {
        if (alertTimeoutRef.current) {
            window.clearTimeout(alertTimeoutRef.current);
            alertTimeoutRef.current = null;
        }
    };

    //--- Keep ref in sync with latest marker position
    useEffect(() => {
        markerPosRef.current = markerPos;
    }, [markerPos]);

    //--- Haversine formula to calculate distance between two coordinates (in km)
    const getDistanceKm = (p1: Location, p2: Location) => {
        const R = 6371; //--- Earth's radius in km
        const dLat = (p2.lat - p1.lat) * (Math.PI / 180);
        const dLng = (p2.lng - p1.lng) * (Math.PI / 180);
        const a =
            Math.sin(dLat / 2) ** 2 +
            Math.cos((p1.lat * Math.PI) / 180) * Math.cos((p2.lat * Math.PI) / 180) * Math.sin(dLng / 2) ** 2;
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return R * c;
    };

    //--- Find the closest point in the path to the current marker position
    const findNearestSegmentIndex = (): number => {
        const pos = markerPosRef.current;
        if (!pos || path.length < 2) return 0;
        let minDist = Infinity;
        let best = 0;
        for (let i = 0; i < path.length; i++) {
            const d = getDistanceKm(pos, path[i].location);
            if (d < minDist) {
                minDist = d;
                best = i;
            }
        }
        return Math.max(0, Math.min(best, pointsLength - 2));
    };

    //--- Snap the marker to the nearest valid segment when restarting animation
    const snapToNearest = () => {
        const seg = findNearestSegmentIndex();
        indexRef.current = seg;
        progressRef.current = 0;
    };

    //--- Main animation loop
    const animate = () => {
        cancelAnimationTracker();
        clearAlertTimeout();

        if (!isPlaying || pointsLength < 2) return;

        directionRef.current = direction;

        if (indexRef.current < 0) indexRef.current = 0;
        if (indexRef.current >= pointsLength - 1) indexRef.current = pointsLength - 2;

        //--- Animate between two points (segment)
        const stepSegment = () => {
            if (!isPlaying) return;

            const startIdx = indexRef.current;
            const dir = directionRef.current;
            const endIdx = dir === 'forward' ? startIdx + 1 : startIdx - 1;

            //--- End of path handling
            if (endIdx < 0 || endIdx >= pointsLength) {
                handleDoneAnimation();
                cancelAnimationTracker();
                setMarkerPos(null);
                return;
            }

            const duration = speedMapper[speed];
            const startTime = performance.now() - progressRef.current * duration;

            //--- Frame update callback
            const step = (now: number) => {
                if (!isPlaying) return;
                const elapsed = now - startTime;
                const progress = Math.min(elapsed / duration, 1);
                const curDir = directionRef.current;

                const curEndIdx = curDir === 'forward' ? indexRef.current + 1 : indexRef.current - 1;
                const curEnd = path[curEndIdx]?.location;
                const curStart = path[indexRef.current]?.location;
                if (!curStart || !curEnd) return;

                //--- Interpolate between start and end positions
                const lat = curStart.lat + (curEnd.lat - curStart.lat) * progress;
                const lng = curStart.lng + (curEnd.lng - curStart.lng) * progress;
                setMarkerPos({ lat, lng });
                progressRef.current = progress;

                //--- When reaching next point
                if (progress >= 1) {
                    const arrivedIndex = curDir === 'forward' ? indexRef.current + 1 : indexRef.current - 1;
                    const arrivedPoint = path[arrivedIndex];
                    progressRef.current = 0;
                    indexRef.current = arrivedIndex;

                    //--- Pause for alert display if any alerts exist at that location
                    if (showAlerts && arrivedPoint && arrivedPoint.alerts && arrivedPoint.alerts.length > 0) {
                        cancelAnimationTracker();
                        setActiveAlert(arrivedPoint.alerts[0]);
                        alertTimeoutRef.current = window.setTimeout(() => {
                            setActiveAlert(null);
                            if (isPlaying) {
                                //--- Handle end of path check after alert
                                if (
                                    (directionRef.current === 'forward' && indexRef.current >= pointsLength - 1) ||
                                    (directionRef.current === 'rewind' && indexRef.current <= 0)
                                ) {
                                    handleDoneAnimation();
                                    setMarkerPos(null);
                                    return;
                                }
                                //--- Continue animation after alert closes
                                requestAnimationFrame(() => {
                                    progressRef.current = 0;
                                    animate();
                                });
                            }
                        }, ACTIVE_ALERT_DURATION);
                        return;
                    }

                    //--- Continue to next segment if no alerts
                    const nextEnd = directionRef.current === 'forward' ? indexRef.current + 1 : indexRef.current - 1;
                    if (nextEnd < 0 || nextEnd >= pointsLength) {
                        handleDoneAnimation();
                        cancelAnimationTracker();
                        setMarkerPos(null);
                        return;
                    }
                    animationRef.current = requestAnimationFrame(() => stepSegment());
                } else {
                    //--- Continue animation within same segment
                    animationRef.current = requestAnimationFrame(step);
                }
            };

            animationRef.current = requestAnimationFrame(step);
        };

        stepSegment();
    };

    //--- Handle direction changes
    useEffect(() => {
        directionRef.current = direction;
        cancelAnimationTracker();
        clearAlertTimeout();
        snapToNearest();
        if (isPlaying) requestAnimationFrame(() => animate());
    }, [direction]);

    //--- Handle speed changes
    useEffect(() => {
        if (!isPlaying) return;
        cancelAnimationTracker();
        requestAnimationFrame(() => animate());
    }, [speed]);

    //--- Handle play/pause changes
    useEffect(() => {
        if (isPlaying) {
            clearAlertTimeout();
            snapToNearest();
            requestAnimationFrame(() => animate());
        } else {
            cancelAnimationTracker();
            clearAlertTimeout();
            setActiveAlert(null);
        }
    }, [isPlaying]);

    //--- Cleanup on unmount
    useEffect(() => {
        return () => {
            cancelAnimationTracker();
            clearAlertTimeout();
        };
    }, []);

    return (
        <>
            {/* Main moving marker (the truck icon) */}
            {markerPos && (
                <Marker
                    position={markerPos}
                    icon={{
                        url: TripTruckIcon,
                        scaledSize: new window.google.maps.Size(markerIconSize, markerIconSize),
                    }}
                />
            )}

            {/* Optional alert marker display */}
            {showAlerts && activeAlert && (
                <AlertMarkerWithInfoWindow
                    key={`point-${activeAlert.id}`}
                    isOpen={true}
                    mode={activeAlert.viewOptions?.mode}
                    alert={activeAlert as unknown as MapAlert}
                />
            )}
        </>
    );
}

export default LocationMarkerAnimated;
