import { useState } from 'react';
import type { Marker as ClustererMarker } from '@googlemaps/markerclusterer';

import type { AlertMarkerViewMode } from '@/stores/trip-alerts.store';

import type { MapAlert } from '../BaseMap';
import { AlertMarker } from '../AlertMarker';
import TripAlertInfoWindow from '../info-window/TripAlertInfoWindow';
import { AlertRoute } from '../AlertRoute';

export type AlertMarkerWithInfoWindowProps = {
    alert: MapAlert;
    mode?: AlertMarkerViewMode;
    isOpen?: boolean;
    setMarkerRef?: (marker: ClustererMarker | null, key: string) => void;
    onRemoveMarkerClicked?: () => void;
};

export function AlertMarkerWithInfoWindow({
    alert,
    mode = 'single-point',
    isOpen = false,
    setMarkerRef,
    onRemoveMarkerClicked,
}: AlertMarkerWithInfoWindowProps) {
    const [isInfoWindowOpen, setIsInfoWindowOpen] = useState<boolean>(isOpen);

    const fromPosition = { lat: alert.fromState.lat, lng: alert.fromState.long };
    const toPosition = alert.toState
        ? {
              lat: alert.toState.lat,
              lng: alert.toState.long,
          }
        : fromPosition;

    return (
        <>
            <AlertMarker
                key={`point-${alert.id}`}
                alert={alert as unknown as MapAlert}
                position={fromPosition}
                onClick={() => {
                    if (!isInfoWindowOpen) setIsInfoWindowOpen(true);
                }}
                setMarkerRef={setMarkerRef}
            />

            {isInfoWindowOpen && (
                <TripAlertInfoWindow
                    tripAlert={alert}
                    onRemoveMarkerClicked={onRemoveMarkerClicked}
                    onClose={() => {
                        if (isInfoWindowOpen) setIsInfoWindowOpen(false);
                    }}
                />
            )}

            {mode === 'range' && (
                <>
                    <AlertRoute startPoint={fromPosition} endPoint={toPosition} zIndex={1002} />
                    <AlertMarker
                        key={`point-${alert.id}`}
                        alert={alert as unknown as MapAlert}
                        position={toPosition}
                        onClick={() => {
                            if (!isInfoWindowOpen) setIsInfoWindowOpen(true);
                        }}
                        setMarkerRef={setMarkerRef}
                    />
                </>
            )}
        </>
    );
}
