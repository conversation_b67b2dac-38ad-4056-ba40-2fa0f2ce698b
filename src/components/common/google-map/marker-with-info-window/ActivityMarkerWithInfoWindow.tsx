import { useState } from 'react';

import type { TripActivity } from '@/infrastructure/api/trip-activities/types';

import { TripActivityMarker } from '../TripActivityMarker';
import { TripActivityInfoWindow } from '../info-window/TripActivityInfoWindow';

export type ActivityMarkerWithInfoWindowProps = {
    activity: TripActivity;
    isOpen?: boolean;
    setMarkerRef?: (marker: google.maps.marker.AdvancedMarkerElement | null, key: string) => void;
};

export function ActivityMarkerWithInfoWindow({
    activity,
    setMarkerRef,
    isOpen = false,
}: ActivityMarkerWithInfoWindowProps) {
    const [isInfoWindowOpen, setIsInfoWindowOpen] = useState<boolean>(isOpen);

    return (
        <>
            <TripActivityMarker
                key={activity.id}
                activity={activity}
                setMarkerRef={setMarkerRef}
                onClick={() => {
                    if (!isInfoWindowOpen) setIsInfoWindowOpen(true);
                }}
            />

            {isInfoWindowOpen && (
                <TripActivityInfoWindow
                    tripActivity={activity}
                    onClose={() => {
                        if (isInfoWindowOpen) setIsInfoWindowOpen(false);
                    }}
                />
            )}
        </>
    );
}
