import { useState } from 'react';

import type { TripPing } from '@/stores/trip-ping.store';

import { LocationMarker } from '../LocationMarker';
import TripLocationInfoWindow from '../info-window/TripLocationInfoWindow';

export type ActivityMarkerWithInfoWindowProps = {
    location: TripPing;
    isOpen?: boolean;
    setMarkerRef?: (marker: google.maps.marker.AdvancedMarkerElement | null, key: string) => void;
};

export function LocationMarkerWithInfoWindow({
    location,
    setMarkerRef,
    isOpen = false,
}: ActivityMarkerWithInfoWindowProps) {
    const [isInfoWindowOpen, setIsInfoWindowOpen] = useState<boolean>(isOpen);

    return (
        <>
            <LocationMarker
                key={`location-${location.location.latitude}-${location.location.longitude}`}
                location={{ lat: location.location.latitude, lng: location.location.longitude }}
                setMarkerRef={setMarkerRef}
                onClick={() => {
                    if (!isInfoWindowOpen) setIsInfoWindowOpen(true);
                }}
            />

            {isInfoWindowOpen && (
                <TripLocationInfoWindow
                    tripPing={location}
                    position={{ lat: location.location.latitude, lng: location.location.longitude }}
                    onClose={() => {
                        if (isInfoWindowOpen) setIsInfoWindowOpen(false);
                    }}
                />
            )}
        </>
    );
}
