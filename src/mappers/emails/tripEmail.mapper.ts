import type { TripEmail } from '@/components/common/email-templates/TripTemplate';
import type { Trip } from '@/stores/trip-detail.store';

export function mapTripToTripEmail(trip: Trip): TripEmail {
    return {
        owner: trip.ownerDesc,
        driverPhoneNumber: trip.driver.mobileNo,
        driverNationality: trip.driver.passportCountry,
        driverName: trip.driver.name,
        vehiclePlateNumber: trip.vehicle.plateNo,
        vehicleType: '', // Not available in TripDetail
        vehicleDescription: '', // Not available in TripDetail
        transitSeqNumber: trip.transSeq,
        transitNumber: trip.transitNumber.toString(),
        shipmentDescription: trip.shipmentDescription || trip.newShipmentDescription || '',
        transitType: trip.transitTypeName,
        trackerSerialNumber: trip.tracker.serialNumber,
        eLockSerialNumbers: trip.eLocks.map((eLock) => eLock.deviceId.toString()),
        trackingPriority: trip.trackingPriority,
        entryPortName: trip.entryPort?.name || { arabic: '', english: '' },
        exitPortName: trip.exitPort?.name || { arabic: '', english: '' },
        startDate: new Date(trip.startDate),
        alerts: trip.activeAlerts.map((alert) => ({
            id: alert.alertStateChangeId,
            typeName: alert.alertType.name,
            fromStateTimeStamp: alert.fromState.timestamp,
            toStateTimeStamp: alert.toState?.timestamp || null,
        })),
    };
}

// const trip: TripEmail = {
//     owner: 'شركة النقل المتقدم',
//     driverPhoneNumber: '+966501234567',
//     driverNationality: 'سعودي',
//     driverName: 'أحمد محمد العتيبي',
//     vehiclePlateNumber: 'أ ب ج 1234',
//     vehicleType: 'شاحنة نقل',
//     vehicleDescription: 'شاحنة نقل بضائع - طراز 2023',
//     transitSeqNumber: 'TR-2024-001',
//     transitNumber: 'TXN-789456123',
//     shipmentDescription: 'بضائع إلكترونية ومعدات تقنية',
//     transitType: 'نقل بري',
//     trackerSerialNumber: 'GPS-TRK-789456',
//     ELockSerialNumbers: ['EL-001', 'EL-002', 'EL-003'],
//     EntryPortName: {
//         arabic: 'ميناء الدمام',
//         english: 'Dammam Port',
//     },
//     ExitPortName: {
//         arabic: 'ميناء جدة',
//         english: 'Jeddah Port',
//     },
//     StartDate: new Date('2024-01-15T08:30:00'),
//     alerts: [
//         {
//             name: {
//                 arabic: 'انقطاع الإشارة',
//                 english: 'Signal Loss',
//             },
//             timestamp: '2024-01-15T10:15:00',
//         },
//         {
//             name: {
//                 arabic: 'انخفاض البطارية',
//                 english: 'Low Battery',
//             },
//             timestamp: '2024-01-15T14:22:00',
//         },
//     ],
//     Employee: {
//         Name: {
//             arabic: 'محمد عبدالله السعيد',
//             english: 'Mohammed Abdullah Al-Saeed',
//         },
//         Phone1: '011/4013334',
//         Phone2: '011/4068953',
//         Department: {
//             arabic: 'قسم الترانزيت والمسافنة',
//             english: 'Transit and Monitoring Department',
//         },
//         Fax: '011/4092053',
//         Ex: '1122 -1468 - 1467',
//     },
// };
