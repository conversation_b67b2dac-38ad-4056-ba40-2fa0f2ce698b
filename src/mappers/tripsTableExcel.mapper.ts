// src/mappers/tripsTableExcel.mapper.ts
import type { TripsItem } from '@/infrastructure/api/trips/types';
import { TripStatus } from '@/shared/enums';
import { formatLocalizedDate } from '@/shared/utils/format-date.utils';

export interface ExcelSheetTable {
    title?: string;
    columns: string[];
    rows: (string | number | boolean)[][];
}

export const tripsTableExcelMapper = (trips: TripsItem[], t: Function, localized: Function): ExcelSheetTable[] => {
    const columns = [
        t('tripDetails.tripId'),
        t('tripDetails.securityNotes'),
        t('tripDetails.shipmentDescription'),
        t('tripDetails.vehicleCountry'),
        t('tripDetails.ownerDescription'),
        t('tripDetails.driverNationality'),
        t('tripDetails.transitType'),
        t('tripDetails.status'),
        t('tripDetails.routeId'),
        t('tripDetails.driverName'),
        t('tripDetails.startingDate'),
        t('tripDetails.endDate'),
        t('tripDetails.elocks'),
        t('tripDetails.trackerNo'),
        t('tripDetails.transitNumber'),
        t('tripDetails.completeDistance'),
        t('tripDetails.remainingDistance'),
        t('tripDetails.transitSeqNo'),
        t('tripDetails.entryPort'),
        t('tripDetails.exitPort'),
        t('tripDetails.alerts'),
        t('tripDetails.vehiclePlateNumber'),
        t('tripDetails.vehicleModel'),
        t('tripDetails.vehicleType'),
        t('tripDetails.vehicleColor'),
    ];

    const rows = trips.map((trip) => {
        // Merge all alerts into one cell
        const alertsInfo = trip.activeAlerts?.length
            ? trip.activeAlerts
                  .map((alert) => {
                      return localized(alert.type.name);
                  })
                  .join(', ')
            : t('common.noWarnings');

        return [
            trip.id ?? '',
            trip.securityNotes ?? '',
            trip.shipmentDescription ?? '',
            trip.vehicle.plateCountryName ?? '',
            trip.ownerDesc ?? '',
            trip.driver.passportCountry ?? '',
            trip.transitTypeName ?? '',
            trip.status ? TripStatus[trip.status].toLowerCase() : '',
            trip.routeId ?? '',
            trip.driver.name ?? '',
            formatLocalizedDate(trip.startDate),
            trip.endDate ? formatLocalizedDate(trip.endDate) : '',
            trip.eLocks?.map((e) => e.deviceId).join(', ') ?? '',
            trip.tracker.serialNumber ?? '',
            trip.transitNumber ?? '',
            trip.currentState?.completeDistanceInMeters ?? '',
            trip.currentState?.remainingDistanceInMeters ?? '',
            trip.transSeq ?? '',
            localized(trip.entryPort.name) ?? '',
            trip.exitPort ? localized(trip.exitPort.name) : '',
            alertsInfo,
            trip.vehicle.plateNo ?? '',
            trip.vehicle.model ?? '',
            trip.vehicle.type ?? '',
            trip.vehicle.color ?? '',
        ];
    });

    return [
        {
            title: t('reports.tripsReport'),
            columns,
            rows,
        },
    ];
};
