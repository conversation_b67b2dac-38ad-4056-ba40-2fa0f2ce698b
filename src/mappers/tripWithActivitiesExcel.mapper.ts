// src/mappers/tripWithActivitiesExcel.mapper.ts
import { formatLocalizedDate } from '@/shared/utils/format-date.utils';
import type { TripDetail } from '@/infrastructure/api/trips/types';
import type { TripActivity } from '@/infrastructure/api/trip-activities/types';

import { tripExcelMapper } from './tripExcel.mapper';

export interface ExcelSheetTable {
    title?: string;
    columns: string[];
    rows: (string | number | boolean)[][];
}

/**
 * Mapper for Trip Activities to Excel table
 * @param activities - Array of TripActivity objects
 * @param t - Translation function
 * @param localized - Localization function
 * @returns ExcelSheetTable representing trip activities
 */
const tripActivitiesExcelMapper = (activities: TripActivity[], t: Function, localized: Function): ExcelSheetTable => {
    const columns = [
        t('tripActivities.employee'),
        t('tripActivities.date'),
        t('tripActivities.state'),
        t('tripActivities.action'),
        t('tripActivities.details'),
        t('tripActivities.location'),
        t('tripActivities.place'),
        t('tripActivities.notes'),
        t('tripActivities.updatedDate'),
    ];

    const rows = activities.map((activity) => [
        activity.origin ?? '-',
        formatLocalizedDate(activity.createdAt),
        localized(activity.status?.name),
        localized(activity.action?.name),
        activity.details ?? '-',
        activity.location ? `${activity.location.latitude}, ${activity.location.longitude}` : '-',
        activity.address ?? '-',
        activity.note ?? '-',
        activity.updatedAt && formatLocalizedDate(activity.updatedAt),
    ]);

    return {
        title: t('reports.activities'),
        columns,
        rows,
    };
};

/**
 * Mapper for Trip and its Activities to Excel tables
 * @param trip - Trip object containing all trip details
 * @param activities - Array of TripActivity objects
 * @param t - Translation function
 * @param localized - Localization function for names
 * @returns Array of ExcelSheetTable with Trip details and Trip Activities
 */
export const tripWithActivitiesExcelMapper = (
    trip: TripDetail | null,
    activities: TripActivity[],
    t: Function,
    localized: Function,
): ExcelSheetTable[] => {
    // Map Trip details
    const tripTable = tripExcelMapper(trip, t, localized);

    // Map Trip Activities
    const activitiesTable = tripActivitiesExcelMapper(activities, t, localized);

    // Return both tables
    return [tripTable, activitiesTable];
};
