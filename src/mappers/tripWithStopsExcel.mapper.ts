// src/mappers/tripWithStopsExcel.mapper.ts
import { formatLocalizedDate } from '@/shared/utils/format-date.utils';
import type { TripDetail } from '@/infrastructure/api/trips/types';
import type { TripStop } from '@/infrastructure/api/trip-stops/types';

import { tripExcelMapper } from './tripExcel.mapper';

export interface ExcelSheetTable {
    title?: string;
    columns: string[];
    rows: (string | number | boolean)[][];
}

/**
 * Mapper for Trip Stops to Excel table
 * @param tripStops - Array of TripStop objects
 * @param t - Translation function
 * @returns ExcelSheetTable representing trip stops
 */
const tripStopsExcelMapper = (tripStops: TripStop[], t: Function, localized: Function): ExcelSheetTable => {
    const columns = [t('stops.placeName'), t('stops.from'), t('stops.to'), t('stops.duration')];

    const rows = tripStops.map((stop) => [
        localized(stop.address),
        formatLocalizedDate(stop.fromTime),
        formatLocalizedDate(stop.toTime),
        `${stop.period.hours}h ${stop.period.minutes}m`,
    ]);

    return {
        title: t('reports.stops'),
        columns,
        rows,
    };
};

/**
 * Mapper for Trip and its Stops to Excel tables
 * @param trip - Trip object containing all trip details
 * @param tripStops - Array of TripStop objects
 * @param t - Translation function
 * @param localized - Localization function for names
 * @returns Array of ExcelSheetTable with Trip details and Trip Stops
 */
export const tripWithStopsExcelMapper = (
    trip: TripDetail | null,
    tripStops: TripStop[],
    t: Function,
    localized: Function,
): ExcelSheetTable[] => {
    // Map Trip details
    const tripTable = tripExcelMapper(trip, t, localized);

    // Map Trip Stops
    const stopsTable = tripStopsExcelMapper(tripStops, t, localized);

    // Return both tables
    return [tripTable, stopsTable];
};
