// src/mappers/tripWithAlertsExcel.mapper.ts
import { formatLocalizedDate } from '@/shared/utils/format-date.utils';
import { constructTripStates } from '@/shared/utils/trips.utils';
import type { TripDetail } from '@/infrastructure/api/trips/types';
import type { TripAlert } from '@/infrastructure/api/trip-alerts/types';
import { formatFromTo } from '@/shared/utils/alerts.utils';

import { tripExcelMapper } from './tripExcel.mapper';

export interface ExcelSheetTable {
    title?: string;
    columns: string[];
    rows: (string | number | boolean)[][];
}

/**
 * Mapper for Trip Alerts to Excel table
 * @param alerts - Array of TripAlert objects
 * @param t - Translation function
 * @param localized - Localization function for names
 * @returns ExcelSheetTable representing trip alerts
 */
const tripAlertsExcelMapper = (alerts: TripAlert[], t: Function, localized: Function): ExcelSheetTable => {
    // Define the state types to split into separate columns
    const stateTypes = [
        'tripDetails.statusConditions.gpsSignalStrength',
        'tripDetails.statusConditions.gsmSignalStrength',
        'tripDetails.statusConditions.batteryLevel',
        'tripDetails.statusConditions.suspiciousZone',
        'tripDetails.statusConditions.routeGeofence',
        'tripDetails.statusConditions.chargerStatus',
    ];

    const columns = [
        t('common.alertType'),
        t('alert.timeStamp'),
        t('common.location'),
        t('common.speed'),
        t('common.address'),
        ...stateTypes.map((s) => t(s)),
        t('tripDetails.acknowledge'),
    ];

    const rows = alerts.map((alert) => {
        const fromState = alert.fromState;
        const toState = alert.toState;

        const fromStates = constructTripStates(fromState);
        const toStates = toState ? constructTripStates(toState) : [];

        // Build a map { id -> value } for easier access
        const fromStateMap = Object.fromEntries(
            fromStates.map((s) => [s.tooltipKey, s.unit ? `${s.value}${s.unit}` : `${s.value}`]),
        );
        const toStateMap = Object.fromEntries(
            toStates.map((s) => [s.tooltipKey, s.unit ? `${s.value}${s.unit}` : `${s.value}`]),
        );

        const stateValues = stateTypes.map((id) => formatFromTo(fromStateMap[id] ?? '-', toStateMap[id]));

        return [
            localized(alert.alertType.name),
            formatFromTo(
                formatLocalizedDate(fromState.trackerDateTime),
                toState ? formatLocalizedDate(toState.trackerDateTime) : undefined,
            ),
            formatFromTo(
                `(${fromState.lat}, ${fromState.long})`,
                toState ? `(${toState.lat}, ${toState.long})` : undefined,
            ),
            formatFromTo(fromState.currentSpeed, toState ? toState.currentSpeed : undefined),
            formatFromTo(localized(fromState.address), toState ? localized(toState.address) : undefined),
            ...stateValues,
            alert.acknowledgedAt ? t('common.yes') : t('common.no'),
        ];
    });

    return {
        title: t('common.tripAlerts'),
        columns,
        rows,
    };
};

/**
 * Mapper for Trip and its Alerts to Excel tables
 * @param trip - Trip object containing all trip details
 * @param alerts - Array of TripAlert objects
 * @param t - Translation function
 * @param localized - Localization function for names
 * @returns Array of ExcelSheetTable with Trip details and Trip Alerts
 */
export const tripWithAlertsExcelMapper = (
    trip: TripDetail | null,
    alerts: TripAlert[],
    t: Function,
    localized: Function,
): ExcelSheetTable[] => {
    // Map Trip details
    const tripTable = tripExcelMapper(trip, t, localized);

    // Map Trip Alerts
    const alertsTable = tripAlertsExcelMapper(alerts, t, localized);

    // Return both tables
    return [tripTable, alertsTable];
};
