import type { Alert } from '@/infrastructure/api/alerts/types';
import { formatFromTo, getZoneLabel } from '@/shared/utils/alerts.utils';
import { formatLocalizedDate } from '@/shared/utils/format-date.utils';
import { constructTripStates } from '@/shared/utils/trips.utils';

export interface ExcelSheetTable {
    title?: string;
    columns: string[];
    rows: (string | number | boolean)[][];
}

export const alertsTableExcelMapper = (alerts: Alert[], t: Function, localized: Function): ExcelSheetTable[] => {
    // Define the state types to split into separate columns
    const stateTypes = [
        'tripDetails.statusConditions.gpsSignalStrength',
        'tripDetails.statusConditions.gsmSignalStrength',
        'tripDetails.statusConditions.batteryLevel',
        'tripDetails.statusConditions.suspiciousZone',
        'tripDetails.statusConditions.routeGeofence',
        'tripDetails.statusConditions.chargerStatus',
    ];

    const columns = [
        t('tripDetails.tripId'),
        t('tripDetails.entryPort'),
        t('tripDetails.exitPort'),
        t('common.alertType'),
        t('alert.timeStamp'),
        t('common.location'),
        t('common.latestInformationAboutTheTarget'),
        t('common.speed'),
        t('common.address'),
        ...stateTypes.map((s) => t(s)),
        t('tripDetails.acknowledge'),
    ];

    const rows = alerts.map((alert) => {
        // Extract entry/exit ports
        const entryPort = alert.entryPort ? localized(alert.entryPort.name) : '-';
        const exitPort = alert.exitPort ? localized(alert.exitPort.name) : '-';

        // Route zone info
        const fromZone = alert.fromState.routeZone ? t(`filter.${getZoneLabel(alert.fromState.routeZone)}`) : '-';
        const toZone = alert.toState?.routeZone ? t(`filter.${getZoneLabel(alert.toState.routeZone)}`) : '-';

        // Construct trip state icons → represent as text in Excel
        const fromState = alert.fromState;
        const toState = alert.toState;

        const fromStates = constructTripStates(fromState);
        const toStates = toState ? constructTripStates(toState) : [];
        // Build a map { id -> value } for easier access
        const fromStateMap = Object.fromEntries(
            fromStates.map((s) => [s.tooltipKey, s.unit ? `${s.value}${s.unit}` : `${s.value}`]),
        );
        const toStateMap = Object.fromEntries(
            toStates.map((s) => [s.tooltipKey, s.unit ? `${s.value}${s.unit}` : `${s.value}`]),
        );

        const stateValues = stateTypes.map((id) => formatFromTo(fromStateMap[id] ?? '-', toStateMap[id]));

        return [
            alert.tripId ?? '',
            entryPort,
            exitPort,
            localized(alert.alertType.name) ?? '',
            formatFromTo(
                formatLocalizedDate(fromState.trackerDateTime),
                toState ? formatLocalizedDate(toState.trackerDateTime) : undefined,
            ),
            formatFromTo(
                `(${fromState.lat}, ${fromState.long})`,
                toState ? `(${toState.lat}, ${toState.long})` : undefined,
            ),
            formatFromTo(fromZone, toState ? toZone : undefined),
            formatFromTo(fromState.currentSpeed, toState ? toState.currentSpeed : undefined),
            formatFromTo(localized(fromState.address), toState ? localized(toState.address) : undefined),
            ...stateValues,
            alert.acknowledgedAt ? t('common.yes') : t('common.no'),
        ];
    });

    return [
        {
            title: t('reports.alertsReport'),
            columns,
            rows,
        },
    ];
};
