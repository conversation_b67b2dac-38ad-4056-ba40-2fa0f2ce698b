// src/mappers/tripExcel.mapper.ts
import { formatLocalizedDate } from '@/shared/utils/format-date.utils';
import type { TripDetail } from '@/infrastructure/api/trips/types';

export interface ExcelSheetTable {
    title?: string;
    columns: string[];
    rows: (string | number | boolean)[][];
}

/**
 * Maps a TripDetail object into an Excel table with Field | Value rows
 */
export const tripExcelMapper = (trip: TripDetail | null, t: Function, localized: Function): ExcelSheetTable => {
    if (!trip) return { title: t('tripDetails.tripDetails'), columns: [], rows: [] };

    const columns = [t('common.field'), t('common.value')];
    const rows: (string | number | boolean)[][] = [
        [t('filter.transitSequenceNumber'), trip.transSeq],
        [t('tripDetails.ownerDescription'), trip.ownerDesc],
        [t('tripDetails.entryPort'), trip.entryPort ? localized(trip.entryPort.name) : '-'],
        [t('tripDetails.exitPort'), trip.exitPort ? localized(trip.exitPort.name) : '-'],
        [t('tripDetails.transitNumber'), trip.transitNumber],
        [t('tripDetails.tripTracking'), trip.trackingStatus],
        [t('tripDetails.aboutVehicle'), `${trip.vehicle.plateNo} , ${trip.vehicle.id}`],
        [t('tripDetails.driverName'), trip.driver.name],
        [t('tripDetails.driverContactNo'), trip.driver.mobileNo],
        [t('tripDetails.driverPassportNumber'), trip.driver.passportNumber],
        [t('tripDetails.driverNationality'), trip.driver.passportCountry],
        [t('tripDetails.startingDate'), formatLocalizedDate(trip.startDate)],
        [t('tripDetails.endDate'), trip.endDate ? formatLocalizedDate(trip.endDate) : '-'],
        [t('tripDetails.transitType'), trip.transitTypeName],
        [t('tripDetails.completeDistance'), trip.currentState?.completeDistanceInMeters ?? '-'],
        [t('tripDetails.remainingDistance'), trip.currentState?.remainingDistanceInMeters ?? '-'],
        [t('tripDetails.shipmentDescription'), trip.shipmentDescription ?? '-'],
        [t('filter.trackerNumber'), trip.tracker.serialNumber ?? '-'],
        [t('tripDetails.elocks'), trip.eLocks?.map((e) => e.deviceId).join(', ') ?? '-'],
        [t('tripDetails.securityNotes'), trip.securityNotes ?? '-'],
        [t('tripDetails.arrivalTracking'), trip.isFocused ? t('common.yes') : t('common.no')],
        [t('tripDetails.suspectedTrip'), trip.isSuspicious ? t('common.yes') : t('common.no')],
        [t('tripDetails.status'), trip.status],
    ];

    return { title: t('tripDetails.tripDetails'), columns, rows };
};
